.K-Top-Layer{background-color:transparent;z-index:100;position:absolute;top:0;left:0;right:0;bottom:0}
.K-State-Disabled{cursor:default!important}
.K-Text-Content,.K-Img-Content,.K-Glyph-Content,.K-Decoration-Content{display:inline-block;vertical-align:middle}
.K-Layout-H>.K-Widget,.K-Layout-H>.K-Content{display:inline-block;vertical-align:middle}
.K-Layout-H>table.K-Widget{display:inline-table}
.K-Content,.K-Layout-H>.K-Content{margin:0 .1em}
.K-Layout-V>.K-Widget,.K-Layout-V>.K-Content{display:block;margin:.2em auto}
.K-Layout-V>table.K-Widget{display:table}
.K-Layout-V>.K-Widget{margin-left:0;margin-right:0}
.K-Layout-V>.K-Content{margin:.2em 0}
.K-Text-Hide>.K-Text-Content,.K-Glyph-Hide>.K-Glyph-Content{display:none}
.K-Text-Show>.K-Text-Content,.K-Glyph-Show>.K-Glyph-Content{display:inline-block}
.K-Widget{position:relative;font-size:.8em;font-family:Verdana,Arial;text-decoration:none;margin:.1em;z-index:1;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box}
.K-Widget.K-NonSelectable{-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
.K-Widget .K-Selectable{-webkit-user-select:text;-khtml-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}
.K-Widget,input.K-Widget,button.K-Widget{display:inline-block}
table.K-Widget{display:table}
.K-Widget .K-Widget{font-size:1em}
.K-Container .K-Widget{}
.K-Panel{padding:.7em}
.K-Panel,.K-Toolbar{border:1px solid}
.K-Toolbar.K-Button-Group{border:none}
.K-Button-Group .K-Widget{margin:0}
.K-Button-Group.K-Layout-H>.K-Widget{margin-left:-1px;height:100%}
.K-Button-Group.K-Layout-H>.K-Widget.K-First-Child{}
.K-Button-Group.K-Layout-V>.K-Widget{margin-top:-1px;width:100%}
.K-Button-Group.K-Layout-V>.K-Widget.K-First-Child{margin-top:0}
.K-Button-Group .K-Button.K-State-Focused,.K-Button-Group .K-Button.K-State-Hover,.K-Button-Group .K-Button.K-State-Active{z-index:2}

.K-Glyph{vertical-align:middle}
.K-Menu{cursor:default}
.K-Menu{border:1px solid;margin:0;padding:0}
.K-Menu .K-MenuItem{display:block;position:relative;list-style:none;margin:0;padding:.2em 2em;line-height:1.5;white-space:nowrap}
.K-Menu .K-MenuItem .K-SubMenu-Marker,.K-Menu .K-MenuItem .K-CheckMenu-Marker{display:inline-block;position:absolute}
.K-Menu .K-MenuItem .K-SubMenu-Marker{left:auto;right:.3em}
.K-Menu .K-MenuItem .K-CheckMenu-Marker{left:.3em}
.K-Menu.K-Layout-H>.K-MenuItem{float:left}
.K-Menu .K-MenuItem-Separator{}
.K-Menu.K-Layout-H>.K-MenuItem-Separator{margin:0 .2em;padding-left:0;padding-right:0;width:0;border-left:1px solid}
.K-Menu.K-Layout-V>.K-MenuItem-Separator{padding-top:0;padding-bottom:0;margin:.2em 0;height:0;border-top:1px solid}
.K-Menu .K-MenuItem>.K-Menu,.K-Menu .K-MenuItem>.K-Menu{display:none;position:absolute;left:100%;top:0}
.K-Menu .K-MenuItem:hover>.K-Menu,.K-Menu .K-MenuItem:active>.K-Menu,.K-Menu .K-MenuItem:focus>.K-Menu{display:block}
.K-Menu .K-MenuItem.K-State-Disabled:hover>.K-Menu,.K-Menu .K-MenuItem.K-State-Disabled:active>.K-Menu,.K-Menu .K-MenuItem.K-State-Disabled:focus>.K-Menu{display:none}
.K-Menu.K-Layout-H>.K-MenuItem>.K-Menu{left:0;top:100%}
.K-MsgPanel{display:block;text-align:center}
.K-MsgPanel .K-MsgPanel-Content{display:inline-block;padding:.3em .7em;border:1px solid}
.K-MsgPanel .K-Pri-Glyph-Content{width:16px;height:16px;display:none}
.K-MsgGroup.K-Widget-MsgGroup{position:absolute;bottom:0;top:100%;left:0;right:0;text-align:center;background-color:transparent}
.K-SysMsgGroup{position:fixed;bottom:3em;z-index:30000;margin:0 auto;left:0;right:0;text-align:center;background-color:transparent}
.K-MsgGroup .K-MsgPanel{margin:.3em auto}
/*底层修改*/
.K-Button,.K-PageNavigator > .K-PageNavigator-PageIndexer{position:relative;line-height:normal;cursor:pointer;vertical-align:middle;text-align:center;overflow:visible;border:none;outline:none}
.K-Button,.K-Button:link,.K-Button:visited,.K-Button:hover,.K-Button:active{text-decoration:none}
.K-Button .K-DropDownMark{font-size:70%;margin-left:.5em;margin-right:0}
.K-Button .K-Content{vertical-align:middle}
.K-Compact-Mark{display:block;position:absolute;right:0;bottom:0;height:0;width:0;border:4px solid;border-top:4px solid transparent;border-left:4px solid transparent;_border-top-color:white;_border-left-color:white;_filter:chroma(color=white);font-size:0;line-height:0}
.K-State-Collapsed .K-NestedContainer{display:none}
.K-TextBox{position:relative}
.K-ComboTextBox{position:relative}
.K-ComboTextBox .K-TextBox{margin:0}
.K-ComboTextBox.K-Overlap .K-TextBox{width:100%;box-sizing:border-box}
.K-ComboTextBox .K-ComboTextBox-Assoc-Widget{z-index:2;margin:0}
.K-ButtonTextBox .K-Button{height:100%;box-sizing:border-box;padding:0 .1em}
.K-ButtonTextBox.K-Overlap .K-Button.K-ComboTextBox-Assoc-Widget{border:none;background-color:transparent;opacity:.7;filter:Alpha(Opacity=70)}
.K-ButtonTextBox.K-Overlap .K-Button.K-State-Hover,.K-ButtonTextBox.K-Overlap .K-Button.K-State-Active{opacity:1;filter:Alpha(Opacity=100)}
.K-CheckBox input{display:inline-block;vertical-align:middle;margin:0;padding:0;margin-right:.3em}
.K-ComboBox{width:10em}
.K-ComboBox .K-ComboBox-TextWrapper{position:absolute;top:1px;left:2px;right:22px;bottom:1px;z-index:2}
.K-ComboBox .K-TextBox{position:absolute;border:0;width:100%;top:0;bottom:0}
.K-ComboBox .K-SelectBox{width:100%;height:100%;z-index:1}
.K-TextArea{min-width:5em;min-height:1em}
/* mTextArea 自定义 */
.K-MTextArea
{
    min-width: 1em;
    min-height: 1em;
}
input[type=range] {
    -webkit-appearance: none;
    width: 300px;
    border-radius: 10px; /*这个属性设置使填充进度条时的图形为圆角*/
}
input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
} 
input[type=range]::-webkit-slider-runnable-track {
    height: 4px;
    border-radius: 10px; /*将轨道设为圆角的*/
    background-color: #64B5F6;
    /*box-shadow: 0 1px 1px #def3f8,64B5F6 inset 0 .125em .125em #0d1112; 轨道内置阴影效果*/
}
input[type=range]:focus {
    outline: none;
    border:none;
}
input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 15px;
    width: 15px;
    margin-top: -5px; /*使滑块超出轨道部分的偏移量相等*/
    background-color: #64DD17; 
    box-shadow: 0 2px 6px 0 rgba(0,0,0,0.28);
    border-radius: 50%; /*外观设置为圆形*/
    border: 2px solid #FFFFFF; /*设置边框*/   
    /* box-shadow: 0 .125em .125em #3b4547; 添加底部阴影 */
}
.K-Chem-Composer-Common-Toolbar>.K-Button{    padding: 0;width: 32px;height: 39px;}


.K-TabButtonGroup{margin:0;padding:0;position:relative}
.K-TabButtonGroup .K-Button{margin:0;padding-top:.2em;padding-bottom:.2em}
.K-TabButtonGroup .K-Button.K-State-Hover,.K-TabButtonGroup .K-Button.K-State-Focused,.K-TabButtonGroup .K-Button.K-State-Active{z-index:2}
.K-TabButtonGroup.K-Layout-H>.K-Button,.K-TabButtonGroup.K-TabAtTop>.K-Button,.K-TabButtonGroup.K-TabAtBottom>.K-Button{margin-right:-1px}
.K-TabButtonGroup.K-Layout-V>.K-Button,.K-TabButtonGroup.K-TabAtLeft>.K-Button,.K-TabButtonGroup.K-TabAtRight>.K-Button{margin-bottom:-1px}
.K-TabButtonGroup.K-TabAtBottom>.K-Button{border-bottom:3px solid transparent}
.K-TabButtonGroup.K-TabAtTop>.K-Button{border-top:3px solid transparent}
.K-TabButtonGroup.K-TabAtRight>.K-Button{border-right:3px solid transparent;text-align:left}
.K-TabButtonGroup.K-TabAtLeft>.K-Button{border-left:3px solid transparent;text-align:right}
.K-Resize-Gripper{position:absolute;right:0;bottom:0;width:16px;height:16px;cursor:se-resize;z-index:1000}
.K-Resize-Gripper.K-State-Disabled{cursor:default}
.K-Widget.K-Transparent-Background,.K-Resize-Gripper.K-Normal-Background{background-color:transparent}
.K-TabView{position:relative}
.K-TabView-TabAtTop .K-TabView-TabButton-Container,.K-TabView-TabAtBottom .K-TabView-TabButton-Container,.K-TabView-TabAtTop .K-TabView-Page-Container,.K-TabView-TabAtBottom .K-TabView-Page-Container{display:block}
.K-TabView-TabAtLeft .K-TabView-TabButton-Container,.K-TabView-TabAtRight .K-TabView-TabButton-Container,.K-TabView-TabAtLeft .K-TabView-Page-Container,.K-TabView-TabAtRight .K-TabView-Page-Container{display:inline-block;vertical-align:top}
.K-TabView .K-TabView-TabButton-Container{position:relative;padding:0;margin:0}
.K-TabView .K-TabView-Page-Container{position:relative;border:1px solid;margin:0;padding:0}
.K-TabView .K-TabView-Page-Container .K-TabView-Page{display:none;left:0;top:0;border:none;margin:0;padding:.5em;overflow:hidden}
.K-TabView .K-TabView-Page-Container .K-TabView-Active-Page{display:block}
.K-TreeView{border:1px solid;cursor:default;padding:.2em .4em;overflow:auto}
.K-TreeView ul{padding:0;margin:0;padding-left:1em;line-height:1.1}
.K-TreeView li{list-style:none;white-space:nowrap}
.K-TreeView .K-TreeView-ItemContent,.K-TreeView .K-TreeView-ExpandMark{display:inline-block}
.K-TreeView .K-TreeView-ExpandMark{width:16px;height:16px;background-repeat:no-repeat}
.K-TreeView li.K-State-Empty .K-TreeView-ExpandMark{visibility:hidden}
.K-TreeView li .K-Text-Content{margin-left:.5em}
.K-Widget-Grid{display:table}
.K-Widget-Grid .K-Widget-Grid-Cell{float:left;position:relative;margin:3px;padding:5px;border:2px solid;width:10em;height:5em}
.K-Widget-Grid .K-Widget-Grid-Cell.K-Widget-Grid-Cell.K-Widget-Grid-Add-Cell{border:2px dashed;font-size:5em;font-family:Arial,Helvetica;text-align:center;cursor:pointer}
.K-Widget-Grid .K-Widget-Grid-Cell:hover,.K-Widget-Grid .K-Widget-Grid-Cell:focus,.K-Widget-Grid .K-Widget-Grid-Cell:active{overflow:visible}
.K-Widget-Grid .K-Widget-Grid-Cell .K-Widget-Grid-Interaction-Area{position:absolute;top:5px;right:5px;z-index:5;display:none}
.K-Widget-Grid.K-Widget-Grid-Enable-Cell-Interaction .K-Widget-Grid-Cell:hover .K-Widget-Grid-Interaction-Area,.K-Widget-Grid.K-Widget-Grid-Enable-Cell-Interaction .K-Widget-Grid-Cell:focus .K-Widget-Grid-Interaction-Area,.K-Widget-Grid.K-Widget-Grid-Enable-Cell-Interaction .K-Widget-Grid-Cell:active .K-Widget-Grid-Interaction-Area{display:block}
.K-Widget-Grid .K-Widget-Grid-Cell .K-Widget-Grid-Interaction-Area .K-Button{padding:.2em}
.K-Widget-Grid .K-Widget-Grid-Cell .K-Widget-Grid-Interaction-Area .K-Button .K-Assoc-Glyph-Content{display:none}
.K-Widget-Grid-Widget-Parent{position:absolute;display:block}
.K-Widget-Grid .K-Widget-Grid-Cell .K-Widget-Grid-Widget-Parent>.K-Widget{display:inline-block;vertical-align:middle}
.K-Dialog{border:1px solid;overflow:hidden;position:absolute;position:fixed}
.K-Dialog.K-Dialog-Overflow{position:absolute}
.K-Dialog.K-Dialog-Inside{position:absolute;position:fixed}
.K-Dialog .K-Dialog-Caption{display:block;padding:.4em 1em;border-bottom:1px solid;font-size:1.1em;font-family:Verdana,Arial;font-weight:bold}
.K-Dialog .K-Dialog-Client{padding:0 .5em .5em .5em}
.K-Dialog .K-Dialog-Button-Panel{padding:0 .25em;text-align:right;overflow:hidden}
.K-Dialog .K-Dialog-Button-Panel .K-Widget{margin:.5em .25em}
.K-Modal-Background{z-index:20000;position:absolute;position:fixed;top:0;left:0;right:0;bottom:0}
.K-TextEditor{}
.K-TextEditor .K-TextEditor-Toolbar,.K-TextEditor .K-TextEditor-TextArea{display:block;margin:0;width:100%}
.K-TextEditor .K-TextEditor-Toolbar{z-index:100;white-space:nowrap}
.K-TextEditor .K-TextEditor-TextArea{}
.K-TextEditor .K-TextEditor-Toolbar .K-Button{padding:.2em .4em}
.K-TextEditor .K-TextEditor-Toolbar .K-TextEditor-FontBox{}
.K-ColorPicker{border:1px solid;padding:.2em .5em}
.K-ColorPicker .K-ColorPicker-Header{overflow:hidden;padding:.2em 0}
.K-ColorPicker .K-ColorPicker-Input{visibility:hidden;width:0;height:0;margin:0;padding:0}
.K-ColorPicker .K-ColorPicker-Spec-Color-Palette,.K-ColorPicker .K-ColorPicker-HexBox,.K-ColorPicker .K-ColorPicker-Previewer,.K-ColorPicker .K-ColorPicker-Browse-Btn{float:left;font-size:80%;line-height:1.4em;padding:0 .5em;margin-right:.3em}
.K-ColorPicker .K-ColorPicker-HexBox,.K-ColorPicker .K-ColorPicker-Previewer{border:1px solid}
.K-ColorPicker .K-ColorPicker-HexBox{width:6em;background-color:transparent;white-space:nowrap;overflow:hidden}
.K-ColorPicker .K-ColorPicker-Previewer{width:2em}
.K-ColorPicker .K-ColorPicker-Browse-Btn{padding:0;margin:0;width:18px;float:right}
.K-ColorPicker .K-ColorPicker-Spec-Color-Palette{padding:0;margin:0;margin-right:.3em}
.K-ColorPicker .K-ColorPicker-Spec-Color-Palette .K-ColorPicker-Palette-Cell{float:left;height:1.4em;width:1.4em}
.K-ColorPicker .K-ColorPicker-Palette,.K-ColorPicker .K-ColorPicker-Spec-Color-Palette{float:left;border-right:1px solid;border-bottom:1px solid;cursor:pointer}
.K-ColorPicker .K-ColorPicker-Palette{min-width:180px}
.K-ColorPicker .K-ColorPicker-Palette-Line{font-size:0}
.K-ColorPicker .K-ColorPicker-Palette-Line,.K-ColorPicker .K-ColorPicker-Palette-Cell{margin:0;padding:0;line-height:1}
.K-ColorPicker .K-ColorPicker-Palette-Cell{display:inline-block;width:9px;height:9px;border-top:1px solid;border-left:1px solid}
.K-ColorDropTextBox .K-ColorPreviewer{border:1px solid;box-sizing:border-box;width:1.5em;height:80%;padding:0 .1em}
.K-ColorDropButton .K-Pri-Glyph-Content{border:1px solid}
.K-ValueListEditor{display:table;border:1px solid;border-collapse:collapse;padding:0;line-height:normal;cursor:default;vertical-align:top;empty-cells:show;table-layout:fixed;width:25em}
.K-ValueListEditor .K-ValueListEditor-Row{border-collapse:collapse;border-top:1px dotted;border-bottom:1px dotted}
.K-ValueListEditor .K-ValueListEditor-Cell{border-collapse:collapse;padding:.2em .4em;margin:0;vertical-align:middle;white-space:nowrap;overflow:hidden}
.K-ValueListEditor .K-ValueListEditor-CellContent{display:block;position:relative;margin:0}
.K-ValueListEditor .K-ValueListEditor-IndicatorCell{width:16px;padding:0;border:hidden}
.K-ValueListEditor .K-ValueListEditor-IndicatorCell span{display:block;width:16px;height:16px}
.K-ValueListEditor .K-ValueListEditor-KeyCell{width:12em}
.K-ValueListEditor .K-ValueListEditor-KeyCell,.K-ValueListEditor .K-ValueListEditor-ValueCell{overflow:hidden;text-overflow:ellipsis}
.K-ValueListEditor .K-ValueListEditor-CellContent{display:block;overflow:hidden;text-overflow:ellipsis}
.K-ValueListEditor .K-ValueListEditor-KeyCell{border-right:1px solid}
.K-ValueListEditor .K-ValueListEditor-ActiveRow{border-top:1px solid;border-bottom:1px solid}
.K-ValueListEditor .K-ValueListEditor-InlineEdit{position:absolute;display:block;font-size:1em;left:0;top:0;right:0;bottom:0;width:99%;border:none;padding:0;margin:0;outline:none}
.K-ValueListEditor .K-ValueListEditor-InlineEdit input{border:none;outline:none;padding:0}
.K-PropListEditor .K-PropListEditor-PropExpandMarker{position:relative;width:16px;height:16px;display:inline-block;margin:0 .2em}
.K-ObjInspector{width:25em;height:40em;border:1px solid;padding:0;overflow:hidden}
.K-ObjInspector .K-ObjInspector-PropListEditorContainer{position:absolute;width:100%;left:0;right:0;top:2em;bottom:5em;overflow-y:auto;overflow-x:hidden;outline:1px solid}
.K-ObjInspector .K-PropListEditor{width:100%;padding:0;margin:0}
.K-ObjInspector .K-ObjInspector-ObjsInfoPanel,.K-ObjInspector .K-ObjInspector-PropInfoPanel{position:absolute;left:0;right:0;overflow:hidden;padding:0 1em}
.K-ObjInspector .K-ObjInspector-ObjsInfoPanel{top:0;height:2em;line-height:2em;white-space:nowrap;font-weight:bold}
.K-ObjInspector .K-ObjInspector-PropInfoPanel{bottom:0;height:5em}
.K-ObjInspector .K-ObjInspector-PropInfoPanel .K-ObjInspector-PropInfoPanel-Title,.K-ObjInspector .K-ObjInspector-PropInfoPanel .K-ObjInspector-PropInfoPanel-Description{}
.K-ObjInspector .K-ObjInspector-PropInfoPanel .K-ObjInspector-PropInfoPanel-Title{line-height:1.5em;font-weight:bold;white-space:nowrap;margin:.5em 0 .2em 0}
.K-ObjInspector .K-ObjInspector-PropInfoPanel .K-ObjInspector-PropInfoPanel-Description{line-height:1.2em;margin-left:.5em;white-space:normal}
.K-DataTable table{display:block;border-collapse:collapse}
.K-DataTable td,.K-DataTable th{border:1px solid;padding:0}
.K-DataTable th.K-DataTable-HeadCellInteractable{cursor:pointer}
.K-DataTable th .K-DataTable-SortMark{display:inline-block;vertical-align:middle;padding:0}
.K-DataTable .K-DataTable-CellWrapper{display:inline-block;margin:.4em}
.K-PageNavigator.K-Layout-H{height:2.5em}
.K-PageNavigator > .K-PageNavigator-PageInput{width:3em;text-align:center}
.K-PageNavigator > .K-PageNavigator-PageIndexer{}
.K-Widget-Configurator .K-Widget-Configurator-Client{white-space:nowrap}
.K-Widget-Configurator .K-ObjInspector,.K-Widget-Configurator .K-TabButtonGroup{vertical-align:top;margin:0}
.K-Widget-Configurator .K-ObjInspector{width:30em;height:30em}
.K-Widget-Configurator .K-ObjInspector .K-ObjInspector-PropListEditorContainer{}
.K-Widget-Configurator .K-ObjInspector .K-ValueListEditor-KeyCell{width:18em}
input.K-Button::-moz-focus-inner,button.K-Button::-moz-focus-inner{border:0;padding:0}
.K-State-Focused,.K-State-Hover,.K-State-Active{}
.K-State-Focused{}
.K-State-Hover{}
.K-State-Active{}
.K-State-Selected{}
.K-Show-Dialog{z-index:10000}
.K-Show-Popup{position:absolute;z-index:21000}
.K-Show-ActiveModal{z-index:20500}
/*底层修改  */
.K-Corner-All,.K-Corner-Top,.K-Corner-Left,.K-Layout-H > .K-Corner-Leading,.K-Layout-V > .K-Corner-Leading,.K-Corner-TL{border-top-left-radius:0px}
.K-Corner-All,.K-Corner-Top,.K-Corner-Right,.K-Layout-H > .K-Corner-Tailing,.K-Layout-V > .K-Corner-Leading,.K-Corner-TR{border-top-right-radius:0px}
.K-Corner-All,.K-Corner-Bottom,.K-Corner-Left,.K-Layout-H > .K-Corner-Leading,.K-Layout-V > .K-Corner-Tailing,.K-Corner-BL{border-bottom-left-radius:0px}
.K-Corner-All,.K-Corner-Bottom,.K-Corner-Right,.K-Layout-H > .K-Corner-Tailing,.K-Layout-V > .K-Corner-Tailing,.K-Corner-Br{border-bottom-right-radius:0px}
.K-Fulfill{width:100%;height:100%;padding:0;margin:0;border:0;display:block}
.K-No-Wrap{white-space:nowrap}
#CssGaga{content:"170228204152,swt,334"}