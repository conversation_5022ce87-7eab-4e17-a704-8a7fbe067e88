<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ContentFilters</key>
	<dict/>
	<key>auto_connect</key>
	<true/>
	<key>data</key>
	<dict>
		<key>connection</key>
		<dict>
			<key>allowDataLocalInfile</key>
			<integer>0</integer>
			<key>database</key>
			<string>notebook_next</string>
			<key>enableClearTextPlugin</key>
			<integer>0</integer>
			<key>host</key>
			<string>localhost</string>
			<key>kcid</key>
			<string>-46689597787373698</string>
			<key>name</key>
			<string>root:password@localhost</string>
			<key>rdbms_type</key>
			<string>mysql</string>
			<key>sslCACertFileLocation</key>
			<string></string>
			<key>sslCACertFileLocationEnabled</key>
			<integer>0</integer>
			<key>sslCertificateFileLocation</key>
			<string></string>
			<key>sslCertificateFileLocationEnabled</key>
			<integer>0</integer>
			<key>sslKeyFileLocation</key>
			<string></string>
			<key>sslKeyFileLocationEnabled</key>
			<integer>0</integer>
			<key>type</key>
			<string>SPTCPIPConnection</string>
			<key>useSSL</key>
			<integer>0</integer>
			<key>user</key>
			<string>root</string>
		</dict>
		<key>session</key>
		<dict>
			<key>connectionEncoding</key>
			<string>utf8mb4</string>
			<key>contentFilterV2</key>
			<dict>
				<key>children</key>
				<array/>
				<key>filterClass</key>
				<string>groupNode</string>
				<key>isConjunction</key>
				<true/>
			</dict>
			<key>contentPageNumber</key>
			<integer>1</integer>
			<key>contentSelection</key>
			<data>
			YnBsaXN0MDDUAQIDBAUGBwpYJHZlcnNpb25ZJGFyY2hpdmVyVCR0
			b3BYJG9iamVjdHMSAAGGoF8QD05TS2V5ZWRBcmNoaXZlctEICVRk
			YXRhgAGtCwwZGhscHSEoLDA0OFUkbnVsbNMNDg8QFBhXTlMua2V5
			c1pOUy5vYmplY3RzViRjbGFzc6MREhOAAoADgASjFRYXgAWABoAI
			gAxUdHlwZVRyb3dzVGtleXNfECZTZWxlY3Rpb25EZXRhaWxUeXBl
			UHJpbWFyeUtleWVkRGV0YWlsc9MNDg8eHyCgoIAH0iIjJCVaJGNs
			YXNzbmFtZVgkY2xhc3Nlc18QE05TTXV0YWJsZURpY3Rpb25hcnmj
			JCYnXE5TRGljdGlvbmFyeVhOU09iamVjdNIODykroSqACYAL0g8t
			Li9ZTlMuc3RyaW5ngApSaWTSIiMxMl8QD05TTXV0YWJsZVN0cmlu
			Z6MxMydYTlNTdHJpbmfSIiM1Nl5OU011dGFibGVBcnJheaM1NydX
			TlNBcnJhedIiIyY5oiYnAAgAEQAaACQAKQAyADcASQBMAFEAUwBh
			AGcAbgB2AIEAiACMAI4AkACSAJYAmACaAJwAngCjAKgArQDWAN0A
			3gDfAOEA5gDxAPoBEAEUASEBKgEvATEBMwE1AToBRAFGAUkBTgFg
			AWQBbQFyAYEBhQGNAZIAAAAAAAACAQAAAAAAAAA6AAAAAAAAAAAA
			AAAAAAABlQ==
			</data>
			<key>contentSortColIsAsc</key>
			<true/>
			<key>contentViewport</key>
			<string>{{0, 0}, {693, 444}}</string>
			<key>isToolbarVisible</key>
			<true/>
			<key>queries</key>
			<string>CREATE USER 'root'@'%' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%';


SELECT * FROM words_english_chinese LEFT JOIN words_english
ON words_english_chinese.`english_id` = words_english.`id`;

SELECT words_english.word, words_english_chinese.`part_of_speech`, words_english_chinese.`translation`
  FROM words_english_chinese LEFT JOIN words_english
    ON words_english_chinese.`english_id` = words_english.`id`;


-- 允许只有词条没有中文解释 CREATE
CREATE VIEW words_english_chinese_summary AS
SELECT words_english_chinese.id as chinese_id, words_english.id, words_english.word,
       words_english.`accent`, `words_english`.script, `words_english`.`syllable`,
       words_english_chinese.`part_of_speech`, words_english_chinese.`translation`, 
       words_english_chinese.`script` as chinese_script,
       words_english_chinese.`phonetic_uk`, words_english_chinese.`phonetic_us`,
       words_english_chinese.`voice_id_uk`, words_english_chinese.`voice_id_us`,
       `words_english_chinese`.`voice_id_translation`, words_english_chinese.`deleted` as chinese_deleted
  FROM words_english_chinese RIGHT JOIN words_english
    ON words_english_chinese.`english_id` = words_english.`id`;

SELECT *
  FROM words_english_chinese RIGHT JOIN words_english
    ON words_english_chinese.`english_id` = words_english.`id`;

SELECT * FROM words_english_chinese wec
  JOIN words_english we ON wec.english_id = we.id;
  


SELECT words_english.word, words_english_chinese.`part_of_speech`, words_english_chinese.`translation`
  FROM words_english_chinese INNER JOIN words_english
    ON words_english_chinese.`english_id` = words_english.`id`;
    
    
        SELECT word, script, phonetic_uk, phonetic_us, voice_id_uk, voice_id_us
        FROM words_english_chinese_summary
        LIMIT 1    ;
    
    
    
    
-- 笔记本 API
CREATE VIEW notebook_words_english_summary AS
   SELECT notebook_words_english.id, notebook_words_english.`notebook_id` as nid, notebook_words_english.weight, notebook_words_english.note,notebook_words_english.`note_explain`, 
   			notebook_words_english.`deleted`,
          words_english.id as eid, words_english.word, words_english.`accent`, words_english.`script`,
          words_english_chinese.id as cid, words_english_chinese.part_of_speech as pos,
          words_english_chinese.`translation`, 
          words_english_chinese.`phonetic_uk`, words_english_chinese.`phonetic_us`, 
          words_english_chinese.`voice_id_uk`, words_english_chinese.`voice_id_us`,
          words_english_chinese.`script` as translation_script, words_english_chinese.`voice_id_translation`
     FROM notebook_words_english    
     JOIN words_english ON notebook_words_english.`english_id` = words_english.id
LEFT JOIN `words_english_chinese` ON notebook_words_english.`chinese_id` = words_english_chinese.id;
    
    
-- 
SELECT MAX(weight) as weight
  FROM notebook_words_english;    
    

-- 单词：英文-中文 API
   SELECT *
     FROM notebook_words_english    
     LEFT JOIN `words_english_chinese` ON notebook_words_english.english_id = words_english_chinese.english_id;


    SELECT *, notebook_words_english.id as wid, notebook_words_english.notebook_id as nid
      FROM words_english_chinese_summary 
LEFT JOIN notebook_words_english on words_english_chinese_summary.chinese_id = notebook_words_english.chinese_id;
    


-- 笔记一览 API
CREATE VIEW notebooks_notes_summary AS
 SELECT notebooks_types.title as `type`, notebooks_types.title_sub as `type_sub`, 
        notebooks.title as notebook_title, notebooks.title_sub as notebook_title_sub, 
        GROUP_CONCAT(`notebooks_topics`.title ORDER BY `notebooks_notes_topics`.tid SEPARATOR ', ') AS topics,
        notebooks_notes.* 
   FROM notebooks_notes
   LEFT JOIN notebooks_types on notebooks_types.id = notebooks_notes.tid
   LEFT JOIN notebooks on notebooks.id = notebooks_notes.nbid
   LEFT JOIN `notebooks_notes_topics` ON `notebooks_notes`.id = `notebooks_notes_topics`.nid
   LEFT JOIN notebooks_topics on notebooks_topics.id = `notebooks_notes_topics`.tid
   GROUP BY `notebooks_notes`.id 	 -- 聚类成一行
   ;
    
SELECT `notebooks_notes`.* 
FROM `notebooks_notes`
LEFT JOIN `notebooks_notes_topics` ON `notebooks_notes`.id = `notebooks_notes_topics`.nid
GROUP BY `notebooks_notes`.id
;    
    
    
CREATE TABLE `books` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(511) DEFAULT NULL,
  `sub_title` varchar(511) DEFAULT NULL,
  `created` datetime(6) NOT NULL,
  `updated` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
  );    
  
  ALTER TABLE books
MODIFY updated DATETIME DEFAULT CURRENT_TIMESTAMP;


DELIMITER //
CREATE PROCEDURE ResetUUIDs()
BEGIN
  DECLARE done INT DEFAULT FALSE;
  DECLARE cur_id BIGINT;
  DECLARE cur CURSOR FOR SELECT id FROM words_english_chinese;
  DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

  OPEN cur;
  read_loop: LOOP
    FETCH cur INTO cur_id;
    IF done THEN
      LEAVE read_loop;
    END IF;
    UPDATE words_english_chinese
    SET voice_id_uk = UUID(), voice_id_us = UUID()
    WHERE id = cur_id;
  END LOOP;
  CLOSE cur;
END //
DELIMITER ;

CALL ResetUUIDs();





-- 检查 UUID 唯一性
SELECT voice_id_uk, COUNT(*) AS count
FROM words_english_chinese
GROUP BY voice_id_uk
HAVING count &gt; 1;

SELECT voice_id_us, COUNT(*) AS count
FROM words_english_chinese
GROUP BY voice_id_us
HAVING count &gt; 1;

SELECT word, COUNT(*) AS count
FROM words_english
GROUP BY word
HAVING count &gt; 1;









SHOW TRIGGERS;
SET SESSION collation_connection = 'utf8mb4_unicode_ci';  -- 确保 视图、触发器 都是用通用的字符集排序规则创建。
--

DROP TRIGGER IF EXISTS update_updated_notebook_words_english;
DELIMITER $$
CREATE TRIGGER update_updated_notebook_words_english
BEFORE UPDATE ON notebook_words_english
FOR EACH ROW
BEGIN
  SET NEW.updated = CURRENT_TIMESTAMP;
END; $$


DROP TRIGGER IF EXISTS update_updated_words_english;
DELIMITER $$
CREATE TRIGGER update_updated_words_english
BEFORE UPDATE ON words_english
FOR EACH ROW
BEGIN
  SET NEW.updated = CURRENT_TIMESTAMP;
END; $$


DROP TRIGGER IF EXISTS update_updated_words_english_chinese;
DELIMITER $$
CREATE TRIGGER update_updated_words_english_chinese
BEFORE UPDATE ON words_english_chinese
FOR EACH ROW
BEGIN
  SET NEW.updated = CURRENT_TIMESTAMP;
END; $$

SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME
FROM information_schema.SCHEMATA
WHERE SCHEMA_NAME = 'notebook_next';

SHOW VARIABLES LIKE 'collation_server';
SHOW VARIABLES LIKE 'default_collation_for_utf8mb4';
SHOW VARIABLES LIKE 'collation_connection';



-- 设置UUID
/*
UPDATE words_english_chinese
SET voice_id_uk = (
    SELECT LOWER(CONCAT(
        HEX(RANDOM_BYTES(4)), '-',
        HEX(RANDOM_BYTES(2)), '-',
        '4', HEX(RANDOM_BYTES(2)), '-',
        HEX(FLOOR(RAND() * 4) + 8), HEX(RANDOM_BYTES(2)), '-',
        HEX(RANDOM_BYTES(6))
    ))
), voice_id_us = (
    SELECT LOWER(CONCAT(
        HEX(RANDOM_BYTES(4)), '-',
        HEX(RANDOM_BYTES(2)), '-',
        '4', HEX(RANDOM_BYTES(2)), '-',
        HEX(FLOOR(RAND() * 4) + 8), HEX(RANDOM_BYTES(2)), '-',
        HEX(RANDOM_BYTES(6))
    ))
); 
*/

-- 搬运音标
/* UPDATE words_english_chinese wec
JOIN words_english we ON wec.english_id = we.id
SET wec.phonetic_uk = we.phonetic_uk,
    wec.phonetic_us = we.phonetic_us; */</string>
			<key>table</key>
			<string>books_words_english</string>
			<key>view</key>
			<string>SP_VIEW_CUSTOMQUERY</string>
			<key>windowVerticalDividerPosition</key>
			<real>308</real>
		</dict>
	</dict>
	<key>encrypted</key>
	<false/>
	<key>format</key>
	<string>connection</string>
	<key>queryFavorites</key>
	<array/>
	<key>rdbms_type</key>
	<string>mysql</string>
	<key>rdbms_version</key>
	<string>8.3.0</string>
	<key>version</key>
	<integer>1</integer>
</dict>
</plist>
