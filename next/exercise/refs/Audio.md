https://github.com/openplayerjs/openplayerjs

https://github.com/Azure-Samples/cognitive-services-speech-sdk/tree/master/samples/batch-avatar
https://www.npmjs.com/package/microsoft-cognitiveservices-speech-sdk
https://learn.microsoft.com/en-us/azure/ai-services/speech-service/get-started-text-to-speech?tabs=macos%2Cterminal&pivots=programming-language-javascript

要实现这个需求，我们需要修改 `fetch-azure-tts.js` 脚本，让它：
1. 从 `words_english` 和 `words_english_chinese` 表中读取 `voice_id_uk` 和对应的 `word` 及 `script` 字段。
2. 检查指定路径 `./voice/en-US-JennyNeural/[UUID第一位]/UUID.wav` 是否存在。
3. 如果文件不存在，使用 Microsoft Azure Cognitive Services Speech SDK（`microsoft-cognitiveservices-speech-sdk`）根据 `script`（优先）或 `word` 生成 TTS 语音，并保存为 WAV 文件。
4. 确保脚本使用 ES Modules（因为你的项目有 `"type": "module"`)，并从 `.env.local` 加载环境变量。

以下是完整的实现代码，包含文件存在性检查、Azure TTS 语音生成，以及保存 WAV 文件的逻辑。

---

### 假设的表结构
根据之前的上下文：
- `words_english` 表：包含 `id`, `word`, `voice_id_uk` 等字段。
- `words_english_chinese` 表：包含 `english_id`, `script`, `voice_id_uk` 等字段，`english_id` 关联 `words_english.id`。
- 我们需要通过 `english_id` 关联两表，获取 `voice_id_uk` 和 `word`/`script`。

假设我们从 `words_english_chinese` 表读取 `voice_id_uk` 和 `script`，并通过 `english_id` 获取 `words_english` 的 `word`。

---

### 环境变量
确保 `.env.local` 包含 Azure TTS 和数据库配置：
```env
DB_HOST=localhost
DB_USER=your_username
DB_PASSWORD=your_password
DB_DATABASE=your_database
AZURE_TTS_KEY=your_azure_tts_subscription_key
AZURE_TTS_REGION=your_azure_region
```

---

### 修改后的 `fetch-azure-tts.js`
以下是完整的脚本，包含文件检查和 Azure TTS 语音生成逻辑：

```javascript
import { config } from 'dotenv';
import path from 'path';
import fs from 'fs/promises';
import mysql from 'mysql2/promise';
import { SpeechConfig, SpeechSynthesizer, AudioConfig } from 'microsoft-cognitiveservices-speech-sdk';

// 加载 .env.local 文件
async function loadEnvFile() {
  const envLocalPath = path.resolve(process.cwd(), '.env.local');
  try {
    await fs.access(envLocalPath, fs.constants.F_OK);
    console.log('.env.local found, loading it...');
    config({ path: envLocalPath });
  } catch (error) {
    console.error('.env.local not found. Please create a .env.local file with database and Azure TTS credentials.');
    throw new Error('Missing .env.local file');
  }

  // 验证环境变量
  if (!process.env.DB_HOST || !process.env.DB_USER || !process.env.DB_PASSWORD || !process.env.DB_DATABASE) {
    throw new Error('Missing required database environment variables');
  }
  if (!process.env.AZURE_TTS_KEY || !process.env.AZURE_TTS_REGION) {
    throw new Error('Missing required Azure TTS environment variables');
  }
}

async function fetchAzureTTS() {
  try {
    // 加载环境变量
    await loadEnvFile();

    // 连接 MySQL 数据库
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
    });

    // 查询 voice_id_uk, word, script
    const [rows] = await connection.execute(`
      SELECT wec.id, wec.english_id, wec.voice_id_uk, wec.script, we.word
      FROM words_english_chinese wec
      JOIN words_english we ON wec.english_id = we.id
      WHERE wec.voice_id_uk IS NOT NULL AND wec.voice_id_uk != ''
      LIMIT 10
    `);

    // 配置 Azure TTS
    const speechConfig = SpeechConfig.fromSubscription(
      process.env.AZURE_TTS_KEY,
      process.env.AZURE_TTS_REGION
    );
    speechConfig.speechSynthesisVoiceName = 'en-US-JennyNeural';

    // 处理每条记录
    for (const row of rows) {
      const { voice_id_uk, script, word } = row;
      const textToSpeak = script || word; // 优先使用 script，否则用 word
      const firstChar = voice_id_uk[0].toLowerCase(); // UUID 第一个字符
      const filePath = path.resolve(
        process.cwd(),
        `./voice/en-US-JennyNeural/${firstChar}/${voice_id_uk}.wav`
      );

      // 检查文件是否存在
      try {
        await fs.access(filePath, fs.constants.F_OK);
        console.log(`File exists for UUID ${voice_id_uk}: ${filePath}, skipping...`);
        continue;
      } catch (error) {
        // 文件不存在，生成 TTS
        console.log(`Generating TTS for UUID ${voice_id_uk}: ${textToSpeak}`);

        // 创建目录（如果不存在）
        const dirPath = path.dirname(filePath);
        await fs.mkdir(dirPath, { recursive: true });

        // 配置音频输出
        const audioConfig = AudioConfig.fromAudioFileOutput(filePath);
        const synthesizer = new SpeechSynthesizer(speechConfig, audioConfig);

        // 生成语音
        const result = await new Promise((resolve, reject) => {
          synthesizer.speakTextAsync(
            textToSpeak,
            (result) => {
              synthesizer.close();
              resolve(result);
            },
            (error) => {
              synthesizer.close();
              reject(error);
            }
          );
        });

        if (result.reason === 1) { // ResultReason.SynthesizingAudioCompleted
          console.log(`TTS generated and saved to ${filePath}`);
        } else {
          console.error(`TTS failed for ${textToSpeak}: ${result.errorDetails}`);
        }
      }
    }

    await connection.end();
    console.log('Azure TTS processing completed');
  } catch (error) {
    console.error('Error:', error.message);
    throw error;
  }
}

fetchAzureTTS();
```

---

### 代码说明
1. **环境变量加载**：
    - 使用 `fs.access` 检查 `.env.local` 是否存在。
    - 如果不存在，抛出错误（根据你的要求，不回退到 `.env`）。
    - 验证数据库和 Azure TTS 环境变量。

2. **数据库查询**：
    - 查询 `words_english_chinese` 和 `words_english`，获取 `voice_id_uk`, `script`, 和 `word`。
    - `JOIN` 确保获取关联的 `word`。
    - `LIMIT 10` 用于测试，防止一次性处理过多记录（可根据需要调整）。

3. **文件路径检查**：
    - 构造文件路径：`./voice/en-US-JennyNeural/[UUID第一位]/UUID.wav`。
    - 使用 `fs.access` 检查文件是否存在。
    - 如果文件存在，跳过该记录。

4. **Azure TTS 语音生成**：
    - 使用 `microsoft-cognitiveservices-speech-sdk` 的 `SpeechSynthesizer`。
    - 设置语音为 `en-US-JennyNeural`。
    - 优先使用 `script` 字段，若为空则使用 `word`。
    - 使用 `AudioConfig.fromAudioFileOutput` 将生成的语音保存为 WAV 文件。
    - `fs.mkdir` 确保目标目录存在（`recursive: true` 自动创建嵌套目录）。

5. **异步处理**：
    - 使用 `await` 确保按顺序处理：
        - 数据库连接 → 查询 → 文件检查 → TTS 生成 → 保存文件。
    - 每个记录的 TTS 生成是串行的（避免并发请求 Azure API 导致配额问题）。

---

### 依赖项
安装必要的包：
```bash
npm install dotenv mysql2 microsoft-cognitiveservices-speech-sdk
```

---

### 运行脚本
1. 确保 `.env.local` 包含正确配置：
   ```env
   DB_HOST=localhost
   DB_USER=your_username
   DB_PASSWORD=your_password
   DB_DATABASE=your_database
   AZURE_TTS_KEY=your_azure_tts_subscription_key
   AZURE_TTS_REGION=your_azure_region
   ```
2. 运行脚本：
   ```bash
   node scripts/fetch-azure-tts.js
   ```

---

### 预期输出
- 如果 `.env.local` 存在且配置正确：
  ```
  .env.local found, loading it...
  File exists for UUID 123e4567-e89b-12d3-a456-************: ./voice/en-US-JennyNeural/1/123e4567-e89b-12d3-a456-************.wav, skipping...
  Generating TTS for UUID abcdef12-3456-7890-abcd-ef1234567890: hello
  TTS generated and saved to ./voice/en-US-JennyNeural/a/abcdef12-3456-7890-abcd-ef1234567890.wav
  Azure TTS processing completed
  ```
- 如果 `.env.local` 不存在：
  ```
  .env.local not found. Please create a .env.local file with database and Azure TTS credentials.
  Error: Missing .env.local file
  ```

---

### 注意事项
1. **Azure TTS 配置**：
    - 确保 `AZURE_TTS_KEY` 和 `AZURE_TTS_REGION` 正确（Azure 门户中获取）。
    - `en-US-JennyNeural` 是 Azure 的神经语音之一，可替换为其他支持的语音（参考 [Azure TTS 文档](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/language-support?tabs=tts))。

2. **文件路径**：
    - 路径 `./voice/en-US-JennyNeural/[UUID第一位]/` 是相对路径，基于 `process.cwd()`（项目根目录）。
    - 如果路径格式不同（例如需要绝对路径），修改 `path.resolve`：
      ```javascript
      const filePath = path.resolve('/absolute/path/to/voice', `en-US-JennyNeural/${firstChar}/${voice_id_uk}.wav`);
      ```

3. **性能优化**：
    - 当前脚本串行处理每个记录的 TTS（避免 Azure API 限额问题）。
    - 如果需要并行处理（提高速度），使用 `Promise.all`：
      ```javascript
      await Promise.all(rows.map(async (row) => {
        // 文件检查和 TTS 逻辑
      }));
      ```
      但需注意 Azure TTS 的并发限制（通常 20 次/秒，视订阅而定）。

4. **错误处理**：
    - 脚本捕获所有错误（文件、数据库、TTS），并打印详细信息。
    - 如果 Azure TTS API 调用失败，检查 `result.errorDetails` 获取具体错误。

5. **目录权限**：
    - 确保 Node.js 有权限在 `./voice/en-US-JennyNeural/` 创建目录和写入文件。
    - 如果遇到权限问题，运行脚本前检查目录权限：
      ```bash
      chmod -R 755 ./voice
      ```

6. **大表处理**：
    - 如果表记录很多，建议分批处理：
      ```javascript
      const batchSize = 1000;
      let lastId = 0;
      let hasMore = true;
      while (hasMore) {
        const [rows] = await connection.execute(
          `SELECT wec.id, wec.english_id, wec.voice_id_uk, wec.script, we.word
           FROM words_english_chinese wec
           JOIN words_english we ON wec.english_id = we.id
           WHERE wec.id > ? AND wec.voice_id_uk IS NOT NULL AND wec.voice_id_uk != ''
           ORDER BY wec.id LIMIT ?`,
          [lastId, batchSize]
        );
        if (rows.length === 0) hasMore = false;
        // 处理 rows...
        lastId = rows[rows.length - 1].id;
      }
      ```

7. **Azure TTS 配额**：
    - Azure TTS 有请求限制和字符限制，检查你的订阅配额。
    - 如果处理大量记录，考虑添加延时或队列（如 Bull）管理请求。

---

### 验证
1. **检查 WAV 文件**：
    - 运行脚本后，检查 `./voice/en-US-JennyNeural/[UUID第一位]/` 目录，确认 WAV 文件生成。
    - 播放 WAV 文件，验证语音内容是否正确。

2. **数据库验证**：
    - 确认 `voice_id_uk` 已正确填充：
      ```sql
      SELECT id, voice_id_uk, script, word
      FROM words_english_chinese
      JOIN words_english ON words_english_chinese.english_id = words_english.id
      WHERE voice_id_uk IS NOT NULL
      LIMIT 10;
      ```

3. **日志**：
    - 检查控制台日志，确认每个 UUID 的处理状态（存在/生成/错误）。

---

### 生产环境部署
- **Vercel**：Vercel 不适合长时间运行的脚本，建议部署到 Node.js 服务器或使用云函数（如 AWS Lambda）运行。
- **Cron 调度**：定期运行脚本：
  ```javascript
  import cron from 'node-cron';
  cron.schedule('0 1 * * *', fetchAzureTTS); // 每天凌晨 1 点
  ```
- **环境变量**：在生产环境中，将 `.env.local` 的内容配置到服务器环境变量（例如 Vercel 仪表板或 Docker）。

---

### 总结
- 脚本检查 `.env.local` 是否存在，并加载环境变量。
- 查询 `voice_id_uk`, `script`, 和 `word`，检查 WAV 文件路径。
- 如果文件不存在，使用 Azure TTS 生成语音，保存到指定路径。
- 使用 `async`/`await` 确保操作顺序，适配 ES Modules。

如果需要进一步优化（例如并行处理、Azure TTS 参数调整、错误重试机制），或你有特定的 Azure TTS 配置（如 SSML 支持），请提供更多细节，我可以进一步完善代码！

<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
    <voice name="en-US-AvaNeural">
        <phoneme alphabet="ipa" ph="tə.ˈmeɪ.toʊ"> tomato </phoneme>
    </voice>
</speak>


















你希望在 `fetch-azure-tts.js` 脚本中使用 SSML（Speech Synthesis Markup Language）来生成 Azure TTS 语音，基于提供的 SSML 示例，结合 `words_english` 和 `words_english_chinese` 表中的 `phonetic_us`, `phonetic_uk`, 和 `word` 字段。以下是修改后的脚本，支持 SSML，优先使用 `script` 字段（若存在），否则使用 `word` 字段，并通过 `phoneme` 标签指定发音（优先使用 `phonetic_us`，若为空则用 `phonetic_uk`）。脚本还会检查 WAV 文件路径 `./voice/en-US-JennyNeural/[UUID第一位]/UUID.wav` 是否存在，并保存生成的语音。

---

### 假设和说明
- **表结构**：
   - `words_english`：包含 `id`, `word`, `phonetic_uk`, `phonetic_us`。
   - `words_english_chinese`：包含 `id`, `english_id`, `script`, `voice_id_uk`。
   - 通过 `english_id` 关联两表。
- **SSML 要求**：
   - 使用 `en-US-JennyNeural` 语音。
   - 使用 `<phoneme>` 标签指定发音，优先使用 `phonetic_us`，若为空则用 `phonetic_uk`。
   - 如果 `script` 字段有值，使用 `script` 作为语音内容，否则使用 `word`。
- **文件路径**：保存到 `./voice/en-US-JennyNeural/[UUID第一位]/UUID.wav`。
- **环境变量**：从 `.env.local` 加载数据库和 Azure TTS 配置。

---

### 更新后的 `fetch-azure-tts.js`
以下是完整的脚本，包含 SSML 支持、文件存在性检查和语音生成逻辑：

```javascript
import { config } from 'dotenv';
import path from 'path';
import fs from 'fs/promises';
import mysql from 'mysql2/promise';
import { SpeechConfig, SpeechSynthesizer, AudioConfig, ResultReason } from 'microsoft-cognitiveservices-speech-sdk';

// 加载 .env.local 文件
async function loadEnvFile() {
  const envLocalPath = path.resolve(process.cwd(), '.env.local');
  try {
    await fs.access(envLocalPath, fs.constants.F_OK);
    console.log('.env.local found, loading it...');
    config({ path: envLocalPath });
  } catch (error) {
    console.error('.env.local not found. Please create a .env.local file with database and Azure TTS credentials.');
    throw new Error('Missing .env.local file');
  }

  // 验证环境变量
  if (!process.env.DB_HOST || !process.env.DB_USER || !process.env.DB_PASSWORD || !process.env.DB_DATABASE) {
    throw new Error('Missing required database environment variables');
  }
  if (!process.env.AZURE_TTS_KEY || !process.env.AZURE_TTS_REGION) {
    throw new Error('Missing required Azure TTS environment variables');
  }
  if (!process.env.AZURE_TTS_VOICE) {
    console.warn('AZURE_TTS_VOICE not set, defaulting to en-US-JennyNeural');
    process.env.AZURE_TTS_VOICE = 'en-US-JennyNeural';
  }
}

// 生成 SSML
function generateSSML(word, phonetic_us, phonetic_uk) {
  const phonetic = phonetic_us || phonetic_uk || ''; // 优先使用 phonetic_us，否则用 phonetic_uk
  const textToSpeak = word; // word 或 script 已在上层处理
  return `
    <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
      <voice name="${process.env.AZURE_TTS_VOICE}">
        ${phonetic ? `<phoneme alphabet="ipa" ph="${phonetic}">${textToSpeak}</phoneme>` : textToSpeak}
      </voice>
    </speak>
  `;
}

async function fetchAzureTTS() {
  try {
    // 加载环境变量
    await loadEnvFile();

    // 连接 MySQL 数据库
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
    });

    // 查询 voice_id_uk, word, script, phonetic_us, phonetic_uk
    const [rows] = await connection.execute(`
      SELECT wec.id, wec.english_id, wec.voice_id_uk, wec.script, we.word, we.phonetic_us, we.phonetic_uk
      FROM words_english_chinese wec
      JOIN words_english we ON wec.english_id = we.id
      WHERE wec.voice_id_uk IS NOT NULL AND wec.voice_id_uk != ''
      LIMIT 10
    `);

    // 配置 Azure TTS
    const speechConfig = SpeechConfig.fromSubscription(
      process.env.AZURE_TTS_KEY,
      process.env.AZURE_TTS_REGION
    );
    speechConfig.speechSynthesisVoiceName = process.env.AZURE_TTS_VOICE;

    // 处理每条记录
    for (const row of rows) {
      const { voice_id_uk, script, word, phonetic_us, phonetic_uk } = row;
      const textToSpeak = script || word; // 优先使用 script，否则用 word
      const firstChar = voice_id_uk[0].toLowerCase(); // UUID 第一个字符
      const filePath = path.resolve(
        process.cwd(),
        `./voice/en-US-JennyNeural/${firstChar}/${voice_id_uk}.wav`
      );

      // 检查文件是否存在
      try {
        await fs.access(filePath, fs.constants.F_OK);
        console.log(`File exists for UUID ${voice_id_uk}: ${filePath}, skipping...`);
        continue;
      } catch (error) {
        // 文件不存在，生成 TTS
        console.log(`Generating TTS for UUID ${voice_id_uk}: ${textToSpeak}`);

        // 创建目录（如果不存在）
        const dirPath = path.dirname(filePath);
        await fs.mkdir(dirPath, { recursive: true });

        // 生成 SSML
        const ssml = generateSSML(textToSpeak, phonetic_us, phonetic_uk);

        // 配置音频输出
        const audioConfig = AudioConfig.fromAudioFileOutput(filePath);
        const synthesizer = new SpeechSynthesizer(speechConfig, audioConfig);

        // 生成语音
        const result = await new Promise((resolve, reject) => {
          synthesizer.speakSsmlAsync(
            ssml,
            (result) => {
              synthesizer.close();
              resolve(result);
            },
            (error) => {
              synthesizer.close();
              reject(error);
            }
          );
        });

        if (result.reason === ResultReason.SynthesizingAudioCompleted) {
          console.log(`TTS generated and saved to ${filePath}`);
        } else {
          console.error(`TTS failed for ${textToSpeak}: ${result.errorDetails}`);
        }
      }
    }

    await connection.end();
    console.log('Azure TTS processing completed');
  } catch (error) {
    console.error('Error:', error.message);
    throw error;
  }
}

fetchAzureTTS();
```

---

### 代码说明
1. **环境变量加载**：
   - 检查 `.env.local` 是否存在，若不存在抛出错误。
   - 验证数据库和 Azure TTS 环境变量（`AZURE_TTS_KEY`, `AZURE_TTS_REGION`, `AZURE_TTS_VOICE`）。
   - 如果 `AZURE_TTS_VOICE` 未设置，默认使用 `en-US-JennyNeural`。

2. **SSML 生成**：
   - 定义 `generateSSML` 函数，根据 `word`, `phonetic_us`, 和 `phonetic_uk` 生成 SSML。
   - 使用 `<phoneme>` 标签指定发音，优先用 `phonetic_us`，若为空则用 `phonetic_uk`。
   - 如果两者都为空，直接使用 `textToSpeak`（`script` 或 `word`）。

3. **数据库查询**：
   - 查询 `words_english_chinese` 和 `words_english`，获取 `voice_id_uk`, `script`, `word`, `phonetic_us`, `phonetic_uk`。
   - `LIMIT 10` 用于测试，可根据需要调整。

4. **文件检查和语音生成**：
   - 检查 WAV 文件路径 `./voice/en-US-JennyNeural/[UUID第一位]/UUID.wav`。
   - 如果文件存在，跳过；否则生成 SSML 并调用 `synthesizer.speakSsmlAsync`。
   - 使用 `AudioConfig.fromAudioFileOutput` 保存语音到指定路径。

5. **异步处理**：
   - 使用 `async`/`await` 确保按顺序执行：加载环境变量 → 数据库连接 → 查询 → 文件检查 → TTS 生成。
   - 每个记录的 TTS 生成是串行的，避免 Azure API 配额问题。

---

### 依赖项
安装必要的包：
```bash
npm install dotenv mysql2 microsoft-cognitiveservices-speech-sdk
```

---

### 环境变量配置
确保 `.env.local` 包含以下内容：
```env
DB_HOST=localhost
DB_USER=your_username
DB_PASSWORD=your_password
DB_DATABASE=your_database
AZURE_TTS_KEY=your_azure_tts_subscription_key
AZURE_TTS_REGION=your_azure_region
AZURE_TTS_VOICE=en-US-JennyNeural
```

---

### 运行脚本
运行命令：
```bash
node scripts/fetch-azure-tts.js
```

#### 预期输出：
- 如果 `.env.local` 存在且配置正确：
  ```
  .env.local found, loading it...
  File exists for UUID 123e4567-e89b-12d3-a456-************: ./voice/en-US-JennyNeural/1/123e4567-e89b-12d3-a456-************.wav, skipping...
  Generating TTS for UUID abcdef12-3456-7890-abcd-ef1234567890: hello
  TTS generated and saved to ./voice/en-US-JennyNeural/a/abcdef12-3456-7890-abcd-ef1234567890.wav
  Azure TTS processing completed
  ```
- 如果 `.env.local` 不存在：
  ```
  .env.local not found. Please create a .env.local file with database and Azure TTS credentials.
  Error: Missing .env.local file
  ```

---

### SSML 说明
- **SSML 结构**：
  ```xml
  <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="en-US">
    <voice name="en-US-JennyNeural">
      <phoneme alphabet="ipa" ph="həˈloʊ">hello</phoneme>
    </voice>
  </speak>
  ```
   - `version="1.0"` 和 `xmlns` 是标准 SSML 属性。
   - `xml:lang="en-US"` 指定语言。
   - `<voice name="...">` 使用环境变量 `AZURE_TTS_VOICE`（默认为 `en-US-JennyNeural`）。
   - `<phoneme alphabet="ipa" ph="...">` 指定 IPA 发音，优先使用 `phonetic_us`。

- **发音处理**：
   - 如果 `phonetic_us` 和 `phonetic_uk` 都为空，SSML 不包含 `<phoneme>` 标签，直接使用 `textToSpeak`。
   - 确保 `phonetic_us` 和 `phonetic_uk` 是有效的 IPA 格式（例如 `həˈloʊ`），否则 Azure TTS 可能报错。

---

### 注意事项
1. **IPA 格式**：
   - 确保数据库中的 `phonetic_us` 和 `phonetic_uk` 是有效的 IPA 发音字符串（参考 [Azure TTS 发音文档](https://learn.microsoft.com/en-us/azure/ai-services/speech-service/speech-synthesis-markup-pronunciation))。
   - 如果发音无效，Azure TTS 可能回退到默认发音或报错。

2. **文件路径**：
   - 路径 `./voice/en-US-JennyNeural/[UUID第一位]/` 是相对路径，基于 `process.cwd()`。
   - 如果需要绝对路径，修改 `filePath`：
     ```javascript
     const filePath = path.resolve('/absolute/path/to/voice', `en-US-JennyNeural/${firstChar}/${voice_id_uk}.wav`);
     ```

3. **性能优化**：
   - 当前脚本串行处理 TTS 请求，适合小规模操作。
   - 如果需要并行处理，使用 `Promise.all`：
     ```javascript
     await Promise.all(rows.map(async (row) => {
       // 文件检查和 TTS 逻辑
     }));
     ```
     但注意 Azure TTS 的并发限制（通常 20 次/秒，视订阅而定）。

4. **错误处理**：
   - 捕获所有错误（文件、数据库、TTS），通过 `result.errorDetails` 检查 TTS 失败原因。
   - 如果 SSML 格式错误，检查 `phonetic_us`/`phonetic_uk` 是否正确。

5. **目录权限**：
   - 确保 Node.js 有权限在 `./voice/en-US-JennyNeural/` 创建目录和写入文件：
     ```bash
     chmod -R 755 ./voice
     ```

6. **大表处理**：
   - 如果记录很多，建议分批处理：
     ```javascript
     const batchSize = 1000;
     let lastId = 0;
     let hasMore = true;
     while (hasMore) {
       const [rows] = await connection.execute(
         `SELECT wec.id, wec.english_id, wec.voice_id_uk, wec.script, we.word, we.phonetic_us, we.phonetic_uk
          FROM words_english_chinese wec
          JOIN words_english we ON wec.english_id = we.id
          WHERE wec.id > ? AND wec.voice_id_uk IS NOT NULL AND wec.voice_id_uk != ''
          ORDER BY wec.id LIMIT ?`,
         [lastId, batchSize]
       );
       if (rows.length === 0) hasMore = false;
       lastId = rows[rows.length - 1].id;
       // 处理 rows...
     }
     ```

7. **Azure TTS 配额**：
   - 检查 Azure 订阅的请求和字符限制。
   - 如果处理大量记录，考虑添加延时或使用队列（如 Bull）。

---

### 验证
1. **检查 WAV 文件**：
   - 运行脚本后，检查 `./voice/en-US-JennyNeural/[UUID第一位]/` 目录，确认 WAV 文件生成。
   - 播放 WAV 文件，验证语音是否符合 `script` 或 `word` 的发音（使用 `phonetic_us` 或 `phonetic_uk`）。

2. **数据库验证**：
   - 确认查询返回正确的 `voice_id_uk`, `script`, `word`, `phonetic_us`, `phonetic_uk`：
     ```sql
     SELECT wec.id, wec.voice_id_uk, wec.script, we.word, we.phonetic_us, we.phonetic_uk
     FROM words_english_chinese wec
     JOIN words_english we ON wec.english_id = we.id
     WHERE wec.voice_id_uk IS NOT NULL
     LIMIT 10;
     ```

3. **日志**：
   - 检查控制台日志，确认每个 UUID 的处理状态（存在/生成/错误）。

---

### 生产环境部署
- **Vercel**：不适合长时间运行的脚本，建议部署到 Node.js 服务器或云函数。
- **Cron 调度**：
  ```javascript
  import cron from 'node-cron';
  cron.schedule('0 1 * * *', fetchAzureTTS); // 每天凌晨 1 点
  ```
- **环境变量**：将 `.env.local` 的内容配置到生产环境（如 Vercel 仪表板或 Docker）。

---

### 总结
- 脚本使用 SSML（`<phoneme>`）生成 Azure TTS 语音，优先使用 `script`，否则用 `word`，发音基于 `phonetic_us` 或 `phonetic_uk`。
- 检查 `.env.local` 和 WAV 文件路径，确保正确加载和保存。
- 使用 `async`/`await` 保证操作顺序，适配 ES Modules。
- 提供分批处理建议，应对大表场景。

如果需要进一步优化（例如 SSML 高级配置、并发处理、错误重试），或有其他需求（如保存 TTS 元数据），请提供更多细节，我可以进一步完善代码！

















自动播放
---
---


要实现使用 `setInterval` 自动切换单词，并在当前单词的音频播放完毕后等待其音频长度时间再切换到下一个单词，我们需要监听音频的 `ended` 事件来触发计时，并根据音频的 `duration` 设置延迟。下面是对之前代码的更新，添加了自动播放功能。

以下是修改后的代码：

```jsx
import React, { useState, useEffect, useRef } from 'react';

const WordPlayer = () => {
  const [words, setWords] = useState([]);
  const [current, setCurrent] = useState(0);
  const audioRef = useRef(null);
  const intervalRef = useRef(null);

  // 模拟加载单词数据
  useEffect(() => {
    const sampleWords = [
      { text: 'apple', audio_url: '/audio/apple.mp3' },
      { text: 'banana', audio_url: '/audio/banana.mp3' },
      { text: 'orange', audio_url: '/audio/orange.mp3' },
    ];
    setWords(sampleWords);
  }, []);

  // 播放当前单词音频并设置定时器
  useEffect(() => {
    if (words.length === 0 || !words[current]?.audio_url) return;

    // 清理现有定时器和音频
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }

    // 创建新音频实例
    audioRef.current = new Audio(words[current].audio_url);

    // 监听音频元数据加载以获取时长
    audioRef.current.addEventListener('loadedmetadata', () => {
      const duration = audioRef.current.duration * 1000; // 转换为毫秒

      // 播放音频
      audioRef.current.play().catch(error => {
        console.error('Audio playback failed:', error);
      });

      // 监听音频播放结束
      const handleAudioEnded = () => {
        // 音频播放完毕后开始计时
        intervalRef.current = setInterval(() => {
          setCurrent((prev) => (prev + 1) % words.length);
        }, duration); // 使用音频时长作为延迟
      };

      audioRef.current.addEventListener('ended', handleAudioEnded);

      // 清理音频事件监听
      return () => {
        audioRef.current.removeEventListener('ended', handleAudioEnded);
      };
    });

    // 清理函数
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [current, words]);

  // 手动切换到下一个单词
  const handleNext = () => {
    setCurrent((prev) => (prev + 1) % words.length);
  };

  // 手动切换到上一个单词
  const handlePrevious = () => {
    setCurrent((prev) => (prev - 1 + words.length) % words.length);
  };

  if (words.length === 0) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex flex-col items-center gap-4 p-4">
      <h1 className="text-2xl font-bold">{words[current].text}</h1>
      <div className="flex gap-4">
        <button
          onClick={handlePrevious}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Previous
        </button>
        <button
          onClick={handleNext}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default WordPlayer;
```

### 主要变更和实现要点

1. **使用 `setInterval` 实现自动切换**：
    - 使用 `intervalRef`（通过 `useRef`）存储 `setInterval` 的引用，以便在需要时清理。
    - 在音频播放完毕后（`ended` 事件触发），启动 `setInterval`，延迟时间为音频的时长（`duration`）。

2. **等待音频播放完毕**：
    - 添加 `loadedmetadata` 事件监听器以获取音频的 `duration`（以秒为单位，转换为毫秒）。
    - 在音频播放结束（`ended` 事件）后，设置 `setInterval`，使用音频的 `duration` 作为切换到下一个单词的延迟时间。

3. **清理机制**：
    - 在 `useEffect` 的清理函数中，停止当前音频并清除 `setInterval`。
    - 移除 `ended` 事件监听器以防止内存泄漏。
    - 每次 `current` 或 `words` 改变时，清理现有定时器和音频，确保不会重复触发。

4. **手动切换兼容性**：
    - 保留了手动切换按钮（`handleNext` 和 `handlePrevious`），它们会更新 `current`，触发 `useEffect` 重新播放音频并重新设置定时器。
    - 手动切换时会立即停止当前音频并播放新音频。

5. **错误处理**：
    - 捕获音频播放错误（如浏览器限制自动播放）。
    - 检查 `words` 是否为空或 `audio_url` 是否存在，避免无效操作。

### 使用说明
- **音频文件**：确保 `audio_url` 指向有效的音频文件（如 `/public/audio/` 下的 MP3 文件）。
- **自动播放限制**：浏览器可能限制自动播放，需用户首次交互（如点击按钮）解除限制。
- **项目配置**：确保项目中已配置 Tailwind CSS（用于样式），并将组件放入 Next.js 项目的 `components` 目录。
- **数据加载**：将 `useEffect` 中的样本数据替换为实际的 API 数据。

### 运行流程
1. 组件加载时，`useEffect` 获取单词数据（这里是模拟数据）。
2. 每次 `current` 改变：
    - 停止当前音频和定时器（如果存在）。
    - 创建新 `Audio` 实例并播放当前单词的音频。
    - 监听 `loadedmetadata` 获取音频时长。
    - 音频播放完毕后，触发 `ended` 事件，启动 `setInterval`，以音频时长为延迟切换到下一个单词。
3. 手动点击“Next”或“Previous”会立即切换单词，重置音频和定时器。
4. 组件卸载或 `current` 改变时，清理音频和定时器。

### 注意事项
- **音频时长**：如果音频文件未正确加载或无 `duration`，可能导致 `setInterval` 无法设置。可以在 `loadedmetadata` 中添加默认延迟（如 2000ms）作为备用。
- **浏览器兼容性**：某些浏览器可能需要用户交互才能播放音频，建议测试时确保首次交互。
- **性能优化**：使用 `useRef` 避免重复创建音频实例和定时器，确保性能和内存管理。

这个实现确保了在当前单词音频播放完毕后，等待其时长后再自动切换到下一个单词，同时支持手动切换和正确的音频管理。






















### 什么是 `new Audio`？
`new Audio` 是 JavaScript 中用于创建和控制音频元素的内置构造函数。它允许你在网页中动态创建 `<audio>` 元素，用于播放音频文件，而无需在 HTML 中显式添加 `<audio>` 标签。通过 `new Audio`，你可以以编程方式控制音频的播放、暂停、音量等。

基本用法：
```javascript
const audio = new Audio('path/to/audio.mp3');
audio.play(); // 播放音频
```

### 核心功能
`new Audio` 创建的音频对象具有以下常用属性和方法：
- **属性**：
  - `src`：音频文件的路径（URL 或本地路径）。
  - `currentTime`：当前播放时间（秒）。
  - `duration`：音频总时长（秒）。
  - `paused`：布尔值，表示音频是否暂停。
  - `volume`：音量（0.0 到 1.0）。
  - `muted`：是否静音。
- **方法**：
  - `play()`：开始播放。
  - `pause()`：暂停播放。
  - `load()`：重新加载音频。
- **事件**：
  - `onplay`：播放开始时触发。
  - `onended`：播放结束时触发。
  - `onerror`：加载或播放错误时触发。

示例：
```javascript
const audio = new Audio('example.mp3');
audio.volume = 0.5; // 设置音量为 50%
audio.addEventListener('ended', () => {
  console.log('音频播放结束');
});
audio.play();
```

### 参考手册
以下是一些权威的参考资源，适合学习 `new Audio`：
1. **MDN Web Docs**（推荐）：
   - MDN 提供了最全面的 `Audio` 对象文档，包含属性、方法、事件和示例。
   - 链接：https://developer.mozilla.org/en-US/docs/Web/API/HTMLAudioElement
   - 特点：内容详尽，代码示例清晰，适合初学者和进阶开发者。
2. **W3Schools**：
   - 提供了简洁的 `HTMLAudioElement` 教程，适合快速入门。
   - 链接：https://www.w3schools.com/jsref/dom_obj_audio.asp
3. **Web.dev**：
   - 提供了与音频相关的现代 Web API 指南，适合学习与 `new Audio` 结合的高级用法。
   - 链接：https://web.dev/articles/audio
4. **HTML5 Audio API 规范**：
   - W3C 官方规范，适合需要深入了解底层实现的开发者。
   - 链接：https://www.w3.org/TR/html5/semantics-embedded-content.html#the-audio-element

### 推荐视频教程
以下是一些讲解 `new Audio` 或 HTML5 音频 API 的高质量视频资源（基于网络搜索和 X 平台内容）：
1. **YouTube - Traversy Media**：
   - 标题：*JavaScript Audio Player Tutorial*
   - 内容：Brad Traversy 讲解如何使用 `new Audio` 构建简单的音频播放器，涵盖播放、暂停、进度条等。
   - 搜索关键词：`Traversy Media JavaScript Audio Player`
   - 特点：讲解清晰，适合初学者，包含实际项目演示。
2. **YouTube - The Net Ninja**：
   - 标题：*HTML5 Audio API Tutorial*
   - 内容：系列教程，深入讲解 `new Audio` 和相关 API，适合中级开发者。
   - 搜索关键词：`The Net Ninja HTML5 Audio`
   - 特点：分步讲解，代码示例多，适合系统学习。
3. **FreeCodeCamp YouTube 频道**：
   - 标题：*Learn JavaScript by Building a Music Player*
   - 内容：通过构建音乐播放器项目，讲解 `new Audio` 的使用。
   - 搜索关键词：`FreeCodeCamp JavaScript Music Player`
   - 特点：项目驱动，适合实践学习。
4. **Bilibili**（适合中文用户）：
   - 搜索关键词：`HTML5 Audio API` 或 `JavaScript 音频播放`
   - 推荐：查找黑马程序员或尚硅谷的教程，他们的视频通常讲解详细，包含 `new Audio` 的实际应用。
   - 特点：中文讲解，适合国内开发者，代码示例贴近实际项目。

### 注意事项
- **浏览器兼容性**：`new Audio` 在现代浏览器（Chrome、Firefox、Safari 等）中广泛支持，但需确保音频格式（如 MP3、OGG、WAV）兼容目标浏览器。
- **跨域问题**：如果音频文件来自不同域名，需确保服务器支持 CORS（跨源资源共享）。
- **事件监听**：建议为 `canplay` 或 `canplaythrough` 事件添加监听，以确保音频加载完成后再调用 `play()`。

### 补充
如果你在实现播放条（如你的上一问题）时使用 `new Audio`，可以结合进度条（`<input type="range">`）和事件监听来控制播放进度。例如：

```jsx
const audio = new Audio('example.mp3');
const playButton = () => (
  <div
    className="d-flex justify-content-center align-items-center"
    onClick={() => {
      if (audio.paused) {
        audio.play();
      } else {
        audio.pause();
      }
    }}
  >
    {audio.paused ? <FaPlay /> : <FaPause />}
  </div>
);
```

如果需要更具体的代码示例或针对播放条的实现建议，请提供更多细节（例如是否使用 React、音频来源等），我可以进一步优化！


ʃrɪŋk

∫rɪŋk
