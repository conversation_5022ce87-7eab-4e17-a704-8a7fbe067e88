### 在单词播放界面添加编辑、增加单词功能
- 播放的单词来源于接口返回的如下SQL语句的JSON，words_english 是单词总表，words_english_chinese 是中文解释表，每个单词对应多个中文解释，每条记录收集了词性，音标，发音等，notebook_words_english 是笔记本，摘录哪个单词需要播放，及哪个中文需要搭配显示，weight是笔记本中单词的权重，按 lexorank 设置顺序。
```sql
   SELECT notebook_words_english.id, notebook_words_english.`notebook_id` as nid, notebook_words_english.weight, notebook_words_english.note,notebook_words_english.`note_explain`, 
          words_english.id as eid, words_english.word, words_english.`accent`, words_english.`script`,
          words_english_chinese.id as cid, words_english_chinese.part_of_speech as pos,
          words_english_chinese.`translation`, 
          words_english_chinese.`phonetic_uk`, words_english_chinese.`phonetic_us`, 
          words_english_chinese.`voice_id_uk`, words_english_chinese.`voice_id_us`,
          words_english_chinese.`script` as translation_script, words_english_chinese.`voice_id_translation`
     FROM notebook_words_english    
     JOIN words_english ON notebook_words_english.`english_id` = words_english.id
LEFT JOIN `words_english_chinese` ON notebook_words_english.`chinese_id` = words_english_chinese.id;
```
- 单词播放界面按编辑按钮，弹出对话框，可以编辑当前单词相关内容；
  - 新建接口 /api/word-english-chinese，当前中文解释有多条的情况，再次查询该接口，获取全部解释。
  - 用户可以编辑单词，和所有相关解释。可以新增解释。每条解释提供 check 框，选择解释是否加入 words_english_chinese 中。
  - 创建保存 api，点击对话框的保存按钮后所作更新反映到相关的表。
-  单词播放界面按增加按钮，弹出对话框，可以增加单词，和所有相关解释。
  - 用户输入单词后，查询 /api/word-english-chinese 查看单词是否存在。存在则加载既有内容。
  - 用户可以添加多条解释，每条解释提供 check 框，选择解释是否加入 words_english_chinese 中。
  - 点击对话框的保存按钮后所作更新反映到相关的表。











我将创建一个增强版的 Next.js 单词播放器组件，添加编辑和增加单词功能，基于提供的 SQL 结构和接口要求。以下是完整的实现，包括播放界面、编辑对话框和新增单词对话框。

### 实现要点
1. **数据结构**：
    - 根据 SQL 查询，`words` 数组包含从 `notebook_words_english`、`words_english` 和 `words_english_chinese` 表联接的数据。
    - 每个单词对象包含：`id`（笔记本ID）、`nid`（笔记本ID）、`weight`（排序权重）、`note`（笔记）、`note_explain`（笔记解释）、`eid`（单词ID）、`word`（单词）、`accent`（口音）、`script`（单词脚本）、`cid`（中文解释ID）、`pos`（词性）、`translation`（中文翻译）、`phonetic_uk`、`phonetic_us`、`voice_id_uk`、`voice_id_us`（英/美发音ID）、`translation_script`（翻译脚本）、`voice_id_translation`（翻译发音ID）。

2. **自动播放**：
    - 保留之前的自动播放逻辑，使用 `setInterval` 在音频播放完毕后等待其时长再切换。
    - 假设 `voice_id_us` 或 `voice_id_uk` 提供音频 URL（例如 `/audio/{voice_id_us}.mp3`）。

3. **编辑功能**：
    - 点击“编辑”按钮，弹出对话框，显示当前单词及其所有中文解释（通过 `/api/word-english-chinese` 获取）。
    - 用户可以编辑单词（`word`）、音标（`phonetic_uk`、`phonetic_us`）等字段，以及所有相关中文解释（`translation`、`pos` 等）。
    - 每条解释提供复选框，决定是否加入 `words_english_chinese` 表。
    - 保存时调用 API 更新相关表。

4. **新增功能**：
    - 点击“新增”按钮，弹出对话框，输入单词后查询 `/api/word-english-chinese` 检查是否存在。
    - 若存在，加载已有解释；若不存在，允许创建新单词和解释。
    - 保存时调用 API 更新相关表。

5. **UI 框架**：
    - 使用 Tailwind CSS 进行样式设计。
    - 使用 Headless UI 的 `Dialog` 组件实现模态对话框（需安装 `@headlessui/react`）。

### 代码实现

```jsx
import React, { useState, useEffect, useRef } from 'react';
import { Dialog, Transition } from '@headlessui/react';

const WordPlayer = () => {
  const [words, setWords] = useState([]);
  const [current, setCurrent] = useState(0);
  const audioRef = useRef(null);
  const intervalRef = useRef(null);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [editWord, setEditWord] = useState(null);
  const [newWord, setNewWord] = useState({ word: '', phonetic_uk: '', phonetic_us: '', translations: [{ translation: '', pos: '', include: true }] });
  const [searchWord, setSearchWord] = useState('');

  // 加载单词数据
  useEffect(() => {
    const fetchWords = async () => {
      try {
        const response = await fetch('/api/notebook-words');
        const data = await response.json();
        setWords(data);
      } catch (error) {
        console.error('Failed to fetch words:', error);
      }
    };
    fetchWords();
  }, []);

  // 播放音频逻辑
  useEffect(() => {
    if (words.length === 0 || !words[current]?.voice_id_us) return;

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }

    audioRef.current = new Audio(`/audio/${words[current].voice_id_us}.mp3`);

    audioRef.current.addEventListener('loadedmetadata', () => {
      const duration = audioRef.current.duration * 1000;

      audioRef.current.play().catch(error => {
        console.error('Audio playback failed:', error);
      });

      const handleAudioEnded = () => {
        intervalRef.current = setInterval(() => {
          setCurrent((prev) => (prev + 1) % words.length);
        }, duration);
      };

      audioRef.current.addEventListener('ended', handleAudioEnded);

      return () => {
        audioRef.current.removeEventListener('ended', handleAudioEnded);
      };
    });

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [current, words]);

  // 编辑单词
  const handleEdit = async () => {
    try {
      const response = await fetch(`/api/word-english-chinese?word=${words[current].word}`);
      const data = await response.json();
      setEditWord({
        ...words[current],
        translations: data.translations.map(t => ({ ...t, include: true })),
      });
      setIsEditOpen(true);
    } catch (error) {
      console.error('Failed to fetch translations:', error);
    }
  };

  // 保存编辑
  const handleSaveEdit = async () => {
    try {
      await fetch('/api/word-english-chinese', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          word_id: editWord.eid,
          word: editWord.word,
          phonetic_uk: editWord.phonetic_uk,
          phonetic_us: editWord.phonetic_us,
          translations: editWord.translations,
        }),
      });
      // 更新本地 words
      const updatedWords = [...words];
      updatedWords[current] = { ...editWord, translations: editWord.translations.filter(t => t.include) };
      setWords(updatedWords);
      setIsEditOpen(false);
    } catch (error) {
      console.error('Failed to save edits:', error);
    }
  };

  // 查询新增单词
  const handleSearchWord = async () => {
    try {
      const response = await fetch(`/api/word-english-chinese?word=${searchWord}`);
      const data = await response.json();
      if (data.exists) {
        setNewWord({
          word: data.word,
          phonetic_uk: data.phonetic_uk,
          phonetic_us: data.phonetic_us,
          translations: data.translations.map(t => ({ ...t, include: true })),
        });
      } else {
        setNewWord({ word: searchWord, phonetic_uk: '', phonetic_us: '', translations: [{ translation: '', pos: '', include: true }] });
      }
    } catch (error) {
      console.error('Failed to search word:', error);
    }
  };

  // 保存新增单词
  const handleSaveAdd = async () => {
    try {
      await fetch('/api/notebook-words', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          word: newWord.word,
          phonetic_uk: newWord.phonetic_uk,
          phonetic_us: newWord.phonetic_us,
          translations: newWord.translations.filter(t => t.include),
          notebook_id: words[0]?.nid, // 假设添加到当前笔记本
          weight: Math.max(...words.map(w => w.weight || 0)) + 1, // 简单递增权重
        }),
      });
      // 刷新单词列表
      const response = await fetch('/api/notebook-words');
      const data = await response.json();
      setWords(data);
      setIsAddOpen(false);
      setNewWord({ word: '', phonetic_uk: '', phonetic_us: '', translations: [{ translation: '', pos: '', include: true }] });
      setSearchWord('');
    } catch (error) {
      console.error('Failed to add word:', error);
    }
  };

  // 添加新翻译
  const addTranslation = (isEdit) => {
    if (isEdit) {
      setEditWord({
        ...editWord,
        translations: [...editWord.translations, { translation: '', pos: '', include: true }],
      });
    } else {
      setNewWord({
        ...newWord,
        translations: [...newWord.translations, { translation: '', pos: '', include: true }],
      });
    }
  };

  if (words.length === 0) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex flex-col items-center gap-4 p-4">
      <h1 className="text-2xl font-bold">{words[current].word}</h1>
      <p className="text-lg">{words[current].translation}</p>
      <p className="text-sm text-gray-500">UK: {words[current].phonetic_uk} | US: {words[current].phonetic_us}</p>
      <div className="flex gap-4">
        <button
          onClick={() => setCurrent((prev) => (prev - 1 + words.length) % words.length)}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Previous
        </button>
        <button
          onClick={() => setCurrent((prev) => (prev + 1) % words.length)}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Next
        </button>
        <button
          onClick={handleEdit}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Edit
        </button>
        <button
          onClick={() => setIsAddOpen(true)}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
        >
          Add
        </button>
      </div>

      {/* 编辑对话框 */}
      <Transition show={isEditOpen}>
        <Dialog onClose={() => setIsEditOpen(false)} className="relative z-50">
          <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
          <div className="fixed inset-0 flex items-center justify-center p-4">
            <Dialog.Panel className="bg-white rounded-lg p-6 max-w-lg w-full">
              <Dialog.Title className="text-lg font-bold">Edit Word</Dialog.Title>
              {editWord && (
                <div className="mt-4 space-y-4">
                  <input
                    type="text"
                    value={editWord.word}
                    onChange={(e) => setEditWord({ ...editWord, word: e.target.value })}
                    className="w-full p-2 border rounded"
                    placeholder="Word"
                  />
                  <input
                    type="text"
                    value={editWord.phonetic_uk}
                    onChange={(e) => setEditWord({ ...editWord, phonetic_uk: e.target.value })}
                    className="w-full p-2 border rounded"
                    placeholder="UK Phonetic"
                  />
                  <input
                    type="text"
                    value={editWord.phonetic_us}
                    onChange={(e) => setEditWord({ ...editWord, phonetic_us: e.target.value })}
                    className="w-full p-2 border rounded"
                    placeholder="US Phonetic"
                  />
                  {editWord.translations.map((t, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={t.include}
                        onChange={(e) => {
                          const newTranslations = [...editWord.translations];
                          newTranslations[index].include = e.target.checked;
                          setEditWord({ ...editWord, translations: newTranslations });
                        }}
                      />
                      <input
                        type="text"
                        value={t.translation}
                        onChange={(e) => {
                          const newTranslations = [...editWord.translations];
                          newTranslations[index].translation = e.target.value;
                          setEditWord({ ...editWord, translations: newTranslations });
                        }}
                        className="flex-1 p-2 border rounded"
                        placeholder="Translation"
                      />
                      <input
                        type="text"
                        value={t.pos}
                        onChange={(e) => {
                          const newTranslations = [...editWord.translations];
                          newTranslations[index].pos = e.target.value;
                          setEditWord({ ...editWord, translations: newTranslations });
                        }}
                        className="w-24 p-2 border rounded"
                        placeholder="Part of Speech"
                      />
                    </div>
                  ))}
                  <button
                    onClick={() => addTranslation(true)}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    Add Translation
                  </button>
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => setIsEditOpen(false)}
                      className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSaveEdit}
                      className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                    >
                      Save
                    </button>
                  </div>
                </div>
              )}
            </Dialog.Panel>
          </div>
        </Dialog>
      </Transition>

      {/* 新增对话框 */}
      <Transition show={isAddOpen}>
        <Dialog onClose={() => setIsAddOpen(false)} className="relative z-50">
          <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
          <div className="fixed inset-0 flex items-center justify-center p-4">
            <Dialog.Panel className="bg-white rounded-lg p-6 max-w-lg w-full">
              <Dialog.Title className="text-lg font-bold">Add Word</Dialog.Title>
              <div className="mt-4 space-y-4">
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={searchWord}
                    onChange={(e) => setSearchWord(e.target.value)}
                    className="flex-1 p-2 border rounded"
                    placeholder="Enter word to search"
                  />
                  <button
                    onClick={handleSearchWord}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    Search
                  </button>
                </div>
                <input
                  type="text"
                  value={newWord.word}
                  onChange={(e) => setNewWord({ ...newWord, word: e.target.value })}
                  className="w-full p-2 border rounded"
                  placeholder="Word"
                />
                <input
                  type="text"
                  value={newWord.phonetic_uk}
                  onChange={(e) => setNewWord({ ...newWord, phonetic_uk: e.target.value })}
                  className="w-full p-2 border rounded"
                  placeholder="UK Phonetic"
                />
                <input
                  type="text"
                  value={newWord.phonetic_us}
                  onChange={(e) => setNewWord({ ...newWord, phonetic_us: e.target.value })}
                  className="w-full p-2 border rounded"
                  placeholder="US Phonetic"
                />
                {newWord.translations.map((t, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={t.include}
                      onChange={(e) => {
                        const newTranslations = [...newWord.translations];
                        newTranslations[index].include = e.target.checked;
                        setNewWord({ ...newWord, translations: newTranslations });
                      }}
                    />
                    <input
                      type="text"
                      value={t.translation}
                      onChange={(e) => {
                        const newTranslations = [...newWord.translations];
                        newTranslations[index].translation = e.target.value;
                        setNewWord({ ...newWord, translations: newTranslations });
                      }}
                      className="flex-1 p-2 border rounded"
                      placeholder="Translation"
                    />
                    <input
                      type="text"
                      value={t.pos}
                      onChange={(e) => {
                        const newTranslations = [...newWord.translations];
                        newTranslations[index].pos = e.target.value;
                        setNewWord({ ...newWord, translations: newTranslations });
                      }}
                      className="w-24 p-2 border rounded"
                      placeholder="Part of Speech"
                    />
                  </div>
                ))}
                <button
                  onClick={() => addTranslation(false)}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                  Add Translation
                </button>
                <div className="flex justify-end gap-2">
                  <button
                    onClick={() => setIsAddOpen(false)}
                    className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSaveAdd}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                  >
                    Save
                  </button>
                </div>
              </div>
            </Dialog.Panel>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};

export default WordPlayer;
```

### 实现说明

1. **数据加载**：
    - 使用 `useEffect` 从 `/api/notebook-words` 获取单词数据，基于提供的 SQL 查询结构。
    - 数据格式与 SQL 字段对应，`words` 数组包含所有字段（如 `word`、`translation`、`phonetic_uk` 等）。

2. **自动播放**：
    - 沿用之前的逻辑，使用 `voice_id_us` 构造音频 URL（例如 `/audio/{voice_id_us}.mp3`）。
    - 音频播放完毕后，等待其 `duration` 时长，通过 `setInterval` 切换到下一个单词。

3. **编辑功能**：
    - 点击“Edit”按钮，调用 `/api/word-english-chinese?word={word}` 获取当前单词的所有中文解释。
    - 对话框显示单词、音标（UK/US）以及所有翻译（`translations`），每条翻译有复选框（`include`）控制是否保存到 `words_english_chinese`。
    - 用户可编辑单词、音标、翻译内容和词性，并添加新翻译。
    - 保存时，调用 `/api/word-english-chinese`（POST）更新 `words_english` 和 `words_english_chinese` 表，仅保存 `include` 为 `true` 的翻译。
    - 更新本地 `words` 状态，反映编辑后的内容。

4. **新增功能**：
    - 点击“Add”按钮，打开对话框，输入单词后点击“Search”查询 `/api/word-english-chinese?word={searchWord}`。
    - 若单词存在，加载其音标和所有翻译；若不存在，初始化空数据。
    - 用户可添加多条翻译，每条有复选框控制是否保存。
    - 保存时，调用 `/api/notebook-words`（POST）创建新单词（`words_english`）、翻译（`words_english_chinese`）和笔记本记录（`notebook_words_english`），并设置 `weight`（简单递增）。
    - 刷新 `words` 列表以包含新单词。

5. **UI 设计**：
    - 使用 Tailwind CSS 进行样式布局，主界面显示单词、翻译和音标。
    - 使用 `@headlessui/react` 的 `Dialog` 和 `Transition` 实现模态对话框，带半透明背景。
    - 编辑和新增对话框包含输入框（单词、音标、翻译、词性）和复选框，支持动态添加翻译。

### API 假设
- **GET `/api/notebook-words`**：返回 SQL 查询的 JSON 数据。
- **GET `/api/word-english-chinese?word={word}`**：返回指定单词的详细信息，包括所有翻译，格式如：
  ```json
  {
    "exists": true,
    "word": "apple",
    "phonetic_uk": "/ˈæp.l/",
    "phonetic_us": "/ˈæpəl/",
    "translations": [
      { "id": 1, "translation": "苹果", "pos": "noun" },
      { "id": 2, "translation": "苹果树", "pos": "noun" }
    ]
  }
  ```
- **POST `/api/word-english-chinese`**：更新单词和翻译，接收：
  ```json
  {
    "word_id": 123,
    "word": "apple",
    "phonetic_uk": "/ˈæp.l/",
    "phonetic_us": "/ˈæpəl/",
    "translations": [
      { "id": 1, "translation": "苹果", "pos": "noun", "include": true },
      { "translation": "新翻译", "pos": "noun", "include": true }
    ]
  }
  ```
- **POST `/api/notebook-words`**：添加新单词到笔记本，接收：
  ```json
  {
    "word": "apple",
    "phonetic_uk": "/ˈæp.l/",
    "phonetic_us": "/ˈæpəl/",
    "translations": [
      { "translation": "苹果", "pos": "noun", "include": true }
    ],
    "notebook_id": 1,
    "weight": 100
  }
  ```

### 使用说明
1. **依赖安装**：
   ```bash
   npm install @headlessui/react
   ```
   确保项目已配置 Tailwind CSS。

2. **文件结构**：
    - 将组件放入 `components/WordPlayer.jsx`。
    - 确保音频文件（如 `/public/audio/{voice_id_us}.mp3`）存在。

3. **API 实现**：
    - 实现 `/api/notebook-words` 和 `/api/word-english-chinese` 路由，处理数据库操作。
    - 确保音频 URL 有效（`voice_id_us` 或 `voice_id_uk` 映射到实际音频文件）。

4. **在页面中使用**：
   ```jsx
   import WordPlayer from '../components/WordPlayer';

   export default function Home() {
     return <WordPlayer />;
   }
   ```

### 注意事项
- **自动播放**：浏览器可能限制自动播放，需用户首次交互（如点击按钮）。
- **权重管理**：当前使用简单递增权重，实际应实现 LexoRank 算法以支持动态排序。
- **错误处理**：API 调用失败会在控制台记录错误，建议添加用户提示（如 Toast 通知）。
- **音频时长**：若 `voice_id_us` 无效或音频未加载，可添加默认延迟（如 2000ms）。
- **数据验证**：实际实现中，应对用户输入进行验证（如非空检查）。

这个实现满足了自动播放、编辑和新增单词的需求，符合 SQL 数据结构，并通过对话框提供直观的用户交互。


















我将为 Next.js 项目创建 `/api/notebook-words` 和 `/api/word-english-chinese` 两个 API 路由的代码，基于提供的 SQL 查询结构，实现获取、查询、更新和新增单词的功能。这些 API 将使用 SQLite 作为数据库（便于示例），并与 `words_english`, `words_english_chinese`, 和 `notebook_words_english` 表交互。你可以根据需要替换为其他数据库（如 MySQL 或 PostgreSQL）。

### 假设和环境
- **数据库**：使用 SQLite（通过 `better-sqlite3`），包含三张表：
    - `words_english`：存储单词信息（`id`, `word`, `accent`, `script`）。
    - `words_english_chinese`：存储中文解释（`id`, `english_id`, `part_of_speech`, `translation`, `phonetic_uk`, `phonetic_us`, `voice_id_uk`, `voice_id_us`, `script`, `voice_id_translation`）。
    - `notebook_words_english`：存储笔记本单词（`id`, `notebook_id`, `english_id`, `chinese_id`, `weight`, `note`, `note_explain`）。
- **音频文件**：假设 `voice_id_us` 和 `voice_id_uk` 映射到 `/public/audio/{voice_id}.mp3`。
- **依赖**：
    - `better-sqlite3`：用于 SQLite 数据库操作。
    - `uuid`：生成唯一 ID。
- **目录**：API 路由位于 `pages/api/`（Next.js API 路由约定）。

### 安装依赖
```bash
npm install better-sqlite3 uuid
```

### 数据库初始化
创建一个 `lib/db.js` 文件，用于初始化 SQLite 数据库和创建表结构。

```javascript
const Database = require('better-sqlite3');
const path = require('path');

const db = new Database(path.join(process.cwd(), 'database.sqlite'));

// 初始化表结构
db.exec(`
  CREATE TABLE IF NOT EXISTS words_english (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    word TEXT NOT NULL,
    accent TEXT,
    script TEXT
  );

  CREATE TABLE IF NOT EXISTS words_english_chinese (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    english_id INTEGER,
    part_of_speech TEXT,
    translation TEXT,
    phonetic_uk TEXT,
    phonetic_us TEXT,
    voice_id_uk TEXT,
    voice_id_us TEXT,
    script TEXT,
    voice_id_translation TEXT,
    FOREIGN KEY (english_id) REFERENCES words_english(id)
  );

  CREATE TABLE IF NOT EXISTS notebook_words_english (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    notebook_id INTEGER,
    english_id INTEGER,
    chinese_id INTEGER,
    weight INTEGER,
    note TEXT,
    note_explain TEXT,
    FOREIGN KEY (english_id) REFERENCES words_english(id),
    FOREIGN KEY (chinese_id) REFERENCES words_english_chinese(id)
  );
`);

// 插入示例数据（仅在开发时使用）
const insertSampleData = () => {
  const count = db.prepare('SELECT COUNT(*) as count FROM words_english').get().count;
  if (count === 0) {
    const insertEnglish = db.prepare('INSERT INTO words_english (word, accent, script) VALUES (?, ?, ?)');
    const insertChinese = db.prepare('INSERT INTO words_english_chinese (english_id, part_of_speech, translation, phonetic_uk, phonetic_us, voice_id_uk, voice_id_us) VALUES (?, ?, ?, ?, ?, ?, ?)');
    const insertNotebook = db.prepare('INSERT INTO notebook_words_english (notebook_id, english_id, chinese_id, weight, note, note_explain) VALUES (?, ?, ?, ?, ?, ?)');

    // 示例数据
    const words = [
      { word: 'apple', accent: 'US', script: null, translations: [
        { pos: 'noun', translation: '苹果', phonetic_uk: '/ˈæp.l/', phonetic_us: '/ˈæpəl/', voice_id_uk: 'apple_uk', voice_id_us: 'apple_us' }
      ]},
      { word: 'banana', accent: 'US', script: null, translations: [
        { pos: 'noun', translation: '香蕉', phonetic_uk: '/bəˈnæn.ə/', phonetic_us: '/bəˈnænə/', voice_id_uk: 'banana_uk', voice_id_us: 'banana_us' }
      ]},
    ];

    words.forEach((word, index) => {
      const englishId = insertEnglish.run(word.word, word.accent, word.script).lastInsertRowid;
      word.translations.forEach((trans) => {
        const chineseId = insertChinese.run(englishId, trans.pos, trans.translation, trans.phonetic_uk, trans.phonetic_us, trans.voice_id_uk, trans.voice_id_us).lastInsertRowid;
        insertNotebook.run(1, englishId, chineseId, index + 1, 'Sample note', 'Sample explanation');
      });
    });
  }
};

insertSampleData();

module.exports = db;
```

### API 路由 1: `/api/notebook-words`
实现 GET（获取笔记本单词列表）和 POST（添加新单词到笔记本）。

```javascript
const db = require('../../lib/db');

export default function handler(req, res) {
  if (req.method === 'GET') {
    try {
      const words = db.prepare(`
        SELECT notebook_words_english.id, notebook_words_english.notebook_id as nid, notebook_words_english.weight, 
               notebook_words_english.note, notebook_words_english.note_explain,
               words_english.id as eid, words_english.word, words_english.accent, words_english.script,
               words_english_chinese.id as cid, words_english_chinese.part_of_speech as pos,
               words_english_chinese.translation, words_english_chinese.phonetic_uk, 
               words_english_chinese.phonetic_us, words_english_chinese.voice_id_uk, 
               words_english_chinese.voice_id_us, words_english_chinese.script as translation_script,
               words_english_chinese.voice_id_translation
        FROM notebook_words_english
        JOIN words_english ON notebook_words_english.english_id = words_english.id
        LEFT JOIN words_english_chinese ON notebook_words_english.chinese_id = words_english_chinese.id
        ORDER BY notebook_words_english.weight
      `).all();
      res.status(200).json(words);
    } catch (error) {
      console.error('GET /api/notebook-words error:', error);
      res.status(500).json({ error: 'Failed to fetch words' });
    }
  } else if (req.method === 'POST') {
    try {
      const { word, phonetic_uk, phonetic_us, translations, notebook_id, weight } = req.body;

      // 插入或获取 words_english
      let englishId;
      const existingWord = db.prepare('SELECT id FROM words_english WHERE word = ?').get(word);
      if (existingWord) {
        englishId = existingWord.id;
      } else {
        englishId = db.prepare('INSERT INTO words_english (word, accent) VALUES (?, ?)')
          .run(word, 'US').lastInsertRowid;
      }

      // 插入 translations 到 words_english_chinese
      const insertChinese = db.prepare('INSERT INTO words_english_chinese (english_id, part_of_speech, translation, phonetic_uk, phonetic_us, voice_id_uk, voice_id_us) VALUES (?, ?, ?, ?, ?, ?, ?)');
      const chineseIds = translations.map((trans) => {
        if (trans.include) {
          return insertChinese.run(
            englishId,
            trans.pos || null,
            trans.translation || null,
            phonetic_uk || null,
            phonetic_us || null,
            trans.voice_id_uk || `${word}_uk`, // 假设生成 voice_id
            trans.voice_id_us || `${word}_us`
          ).lastInsertRowid;
        }
        return null;
      }).filter(id => id !== null);

      // 插入到 notebook_words_english
      const insertNotebook = db.prepare('INSERT INTO notebook_words_english (notebook_id, english_id, chinese_id, weight, note, note_explain) VALUES (?, ?, ?, ?, ?, ?)');
      chineseIds.forEach((chineseId) => {
        insertNotebook.run(notebook_id || 1, englishId, chineseId, weight || 1, null, null);
      });

      res.status(201).json({ message: 'Word added successfully' });
    } catch (error) {
      console.error('POST /api/notebook-words error:', error);
      res.status(500).json({ error: 'Failed to add word' });
    }
  } else {
    res.status(405).json({ error: 'Method not allowed' });
  }
}
```

### API 路由 2: `/api/word-english-chinese`
实现 GET（查询单词及其翻译）和 POST（更新单词和翻译）。

```javascript
const db = require('../../lib/db');
const { v4: uuidv4 } = require('uuid');

export default function handler(req, res) {
  if (req.method === 'GET') {
    try {
      const { word } = req.query;
      if (!word) {
        return res.status(400).json({ error: 'Word parameter is required' });
      }

      const wordData = db.prepare('SELECT id, word, phonetic_uk, phonetic_us FROM words_english WHERE word = ?').get(word);
      if (!wordData) {
        return res.status(200).json({ exists: false });
      }

      const translations = db.prepare('SELECT id, part_of_speech as pos, translation, phonetic_uk, phonetic_us, voice_id_uk, voice_id_us FROM words_english_chinese WHERE english_id = ?')
        .all(wordData.id);

      res.status(200).json({
        exists: true,
        word: wordData.word,
        phonetic_uk: wordData.phonetic_uk || '',
        phonetic_us: wordData.phonetic_us || '',
        translations,
      });
    } catch (error) {
      console.error('GET /api/word-english-chinese error:', error);
      res.status(500).json({ error: 'Failed to fetch word' });
    }
  } else if (req.method === 'POST') {
    try {
      const { word_id, word, phonetic_uk, phonetic_us, translations } = req.body;

      if (!word_id || !word) {
        return res.status(400).json({ error: 'word_id and word are required' });
      }

      // 更新 words_english
      db.prepare('UPDATE words_english SET word = ?, phonetic_uk = ?, phonetic_us = ? WHERE id = ?')
        .run(word, phonetic_uk || null, phonetic_us || null, word_id);

      // 删除旧的翻译
      db.prepare('DELETE FROM words_english_chinese WHERE english_id = ?').run(word_id);

      // 插入新的翻译
      const insertChinese = db.prepare('INSERT INTO words_english_chinese (english_id, part_of_speech, translation, phonetic_uk, phonetic_us, voice_id_uk, voice_id_us) VALUES (?, ?, ?, ?, ?, ?, ?)');
      translations.forEach((trans) => {
        if (trans.include) {
          insertChinese.run(
            word_id,
            trans.pos || null,
            trans.translation || null,
            phonetic_uk || null,
            phonetic_us || null,
            trans.voice_id_uk || `${word}_uk_${uuidv4().slice(0, 8)}`,
            trans.voice_id_us || `${word}_us_${uuidv4().slice(0, 8)}`
          );
        }
      });

      // 更新 notebook_words_english 的 chinese_id（假设只更新第一条翻译）
      const newChineseId = db.prepare('SELECT id FROM words_english_chinese WHERE english_id = ? LIMIT 1').get(word_id)?.id;
      if (newChineseId) {
        db.prepare('UPDATE notebook_words_english SET chinese_id = ? WHERE english_id = ?')
          .run(newChineseId, word_id);
      }

      res.status(200).json({ message: 'Word updated successfully' });
    } catch (error) {
      console.error('POST /api/word-english-chinese error:', error);
      res.status(500).json({ error: 'Failed to update word' });
    }
  } else {
    res.status(405).json({ error: 'Method not allowed' });
  }
}
```

### 实现说明

1. **数据库初始化 (`lib/db.js`)**：
    - 创建 SQLite 数据库文件 `database.sqlite`。
    - 定义三张表，包含外键约束。
    - 插入示例数据（`apple`, `banana`），仅在表为空时执行。
    - 导出数据库实例供 API 路由使用。

2. **GET `/api/notebook-words`**：
    - 执行提供的 SQL 查询，返回笔记本中的单词列表，按 `weight` 排序。
    - 返回 JSON 格式，包含所有字段（如 `id`, `nid`, `word`, `translation` 等）。

3. **POST `/api/notebook-words`**：
    - 接收 `word`, `phonetic_uk`, `phonetic_us`, `translations`, `notebook_id`, `weight`。
    - 检查单词是否已存在于 `words_english`，若存在则复用 `english_id`，否则创建新记录。
    - 为每条 `include: true` 的翻译插入 `words_english_chinese`。
    - 将单词和翻译关联插入 `notebook_words_english`。
    - 返回成功消息。

4. **GET `/api/word-english-chinese?word={word}`**：
    - 根据 `word` 查询 `words_english` 表。
    - 若存在，返回单词信息和所有相关翻译；若不存在，返回 `{ exists: false }`。
    - 翻译包括 `id`, `pos`, `translation`, `phonetic_uk`, `phonetic_us`, `voice_id_uk`, `voice_id_us`。

5. **POST `/api/word-english-chinese`**：
    - 接收 `word_id`, `word`, `phonetic_uk`, `phonetic_us`, `translations`。
    - 更新 `words_english` 表中的单词和音标。
    - 删除旧的翻译（`words_english_chinese`），插入新的 `include: true` 的翻译。
    - 更新 `notebook_words_english` 的 `chinese_id`（假设取第一条翻译）。
    - 使用 `uuid` 生成唯一的 `voice_id_uk` 和 `voice_id_us`（若未提供）。

### 使用说明
1. **文件结构**：
    - `lib/db.js`：数据库初始化。
    - `pages/api/notebook-words.js`：笔记本单词 API。
    - `pages/api/word-english-chinese.js`：单词和翻译 API。
    - 确保音频文件（如 `/public/audio/apple_us.mp3`）存在。

2. **依赖**：
    - 安装 `better-sqlite3` 和 `uuid`。
    - 确保 Next.js 项目支持 API 路由。

3. **数据库配置**：
    - SQLite 数据库文件保存在项目根目录的 `database.sqlite`。
    - 替换为其他数据库（如 MySQL）时，修改 `lib/db.js` 并使用相应驱动（如 `mysql2`）。

4. **与前端集成**：
    - 这些 API 与之前的 `WordPlayer.jsx` 组件兼容。
    - 确保前端发送正确的请求体格式（如 `translations` 包含 `include` 字段）。

5. **音频文件**：
    - `voice_id_uk` 和 `voice_id_us` 映射到音频文件路径（`/audio/{voice_id}.mp3`）。
    - 需要实际音频文件支持播放。

### 注意事项
- **权重管理**：当前 POST `/api/notebook-words` 使用简单递增权重，实际应实现 LexoRank 算法。
- **音频生成**：`voice_id_uk` 和 `voice_id_us` 在创建新翻译时生成默认值（`{word}_uk_{uuid}`），实际需上传音频文件并生成真实 ID。
- **错误处理**：API 已包含基本错误处理，建议在生产环境中添加更详细的日志和用户提示。
- **数据库事务**：为确保数据一致性，建议在 POST 请求中使用事务（`db.transaction`）。
- **安全性**：实际部署时，应对输入进行验证和清理，防止 SQL 注入（当前使用参数化查询已安全）。

这些 API 实现了单词播放器所需的后台功能，支持获取、编辑和新增单词，与前端组件无缝集成。如果需要其他数据库（如 MySQL）或更复杂的权重管理（如 LexoRank），请提供更多细节，我可以进一步调整代码。