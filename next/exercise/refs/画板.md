做一个H5的画板，主要画矢量图形，可以保存 svg 到 mySQL 数据库。
- 支持画布背景网格
- 支持以不同颜色插入：直线，带箭头直线，圆管，圆，弧线，三角形，正方形，三棱锥，四棱锥，圆锥体，正方体，球体，或者导入其他svg图片
- 插入的图形可以拖动调整大小、位置，可以旋转。
- 插入的图形可以粘附到网格。
- 支持直线、圆管粘附到图形关键点。
- 插入的图形可以删除。
- 插入的图形可以在图形列表中拖动调整叠放顺序。
- 支持导出svg文件。




我将创建一个基于HTML5 Canvas的画板应用，支持矢量图形绘制、编辑、网格吸附、保存到MySQL数据库以及导出SVG文件。由于这是一个复杂的应用，我会提供前端代码（HTML/JS with p5.js for canvas rendering）和后端代码（Node.js with Express for MySQL integration）。以下是实现方案：

### 功能实现说明
- **前端**：使用p5.js绘制画布和图形，支持拖拽、调整大小、旋转、网格吸附等功能。使用HTML5拖拽API管理图形列表的叠放顺序。
- **后端**：使用Node.js和Express提供API，连接MySQL数据库保存SVG数据。
- **网格背景**：绘制网格，图形可吸附到网格点。
- **图形支持**：直线、带箭头直线、圆管、圆、弧线、三角形、正方形、三棱锥、四棱锥、圆锥体、正方体、球体，以及导入SVG。
- **交互**：支持拖拽、调整大小、旋转、删除、叠放顺序调整。
- **导出**：将画布内容导出为SVG文件。
- **数据库**：保存和读取SVG数据到MySQL。

由于代码量较大，我将提供核心代码片段，并确保功能完整。以下是前端和后端的实现。

---

### 前端代码（index.html）
使用p5.js实现画布绘制，支持所有要求的图形和交互功能。

```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>H5 Vector Drawing Board</title>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.4.2/p5.min.js"></script>
  <style>
    body { margin: 0; font-family: Arial; }
    #canvas-container { display: inline-block; vertical-align: top; }
    #controls { display: inline-block; vertical-align: top; padding: 10px; }
    #shape-list { max-height: 400px; overflow-y: auto; }
    .shape-item { padding: 5px; border: 1px solid #ccc; margin: 2px; cursor: move; }
    .shape-item:hover { background: #f0f0f0; }
  </style>
</head>
<body>
  <div id="canvas-container"></div>
  <div id="controls">
    <h3>工具栏</h3>
    <select id="shape-type">
      <option value="line">直线</option>
      <option value="arrow">带箭头直线</option>
      <option value="tube">圆管</option>
      <option value="circle">圆</option>
      <option value="arc">弧线</option>
      <option value="triangle">三角形</option>
      <option value="square">正方形</option>
      <option value="tetrahedron">三棱锥</option>
      <option value="pyramid">四棱锥</option>
      <option value="cone">圆锥体</option>
      <option value="cube">正方体</option>
      <option value="sphere">球体</option>
      <option value="svg">导入SVG</option>
    </select>
    <input type="color" id="shape-color" value="#000000">
    <input type="file" id="svg-file" accept=".svg">
    <button onclick="addShape()">添加图形</button>
    <button onclick="exportSVG()">导出SVG</button>
    <button onclick="saveToDatabase()">保存到数据库</button>
    <button onclick="loadFromDatabase()">从数据库加载</button>
    <h3>图形列表</h3>
    <div id="shape-list"></div>
  </div>

  <script>
    let shapes = [];
    let selectedShape = null;
    let isDragging = false;
    let isResizing = false;
    let isRotating = false;
    let gridSize = 20;

    function setup() {
      let canvas = createCanvas(800, 600);
      canvas.parent('canvas-container');
    }

    function draw() {
      background(255);
      drawGrid();
      for (let shape of shapes) {
        shape.draw();
      }
      if (selectedShape) {
        selectedShape.drawHandles();
      }
    }

    function drawGrid() {
      stroke(200);
      strokeWeight(1);
      for (let x = 0; x < width; x += gridSize) {
        line(x, 0, x, height);
      }
      for (let y = 0; y < height; y += gridSize) {
        line(0, y, width, y);
      }
    }

    class Shape {
      constructor(type, x, y, size, color, rotation = 0) {
        this.type = type;
        this.x = x;
        this.y = y;
        this.size = size;
        this.color = color;
        this.rotation = rotation;
        this.points = [];
        this.svgContent = null;
        if (type === 'line' || type === 'arrow' || type === 'tube') {
          this.points = [{x, y}, {x: x + size, y}];
        }
      }

      draw() {
        push();
        translate(this.x, this.y);
        rotate(this.rotation);
        stroke(this.color);
        fill(this.color);
        strokeWeight(this.type === 'tube' ? 5 : 1);

        if (this.type === 'line') {
          line(this.points[0].x - this.x, this.points[0].y - this.y, this.points[1].x - this.x, this.points[1].y - this.y);
        } else if (this.type === 'arrow') {
          let [p1, p2] = this.points;
          line(p1.x - this.x, p1.y - this.y, p2.x - this.x, p2.y - this.y);
          let angle = atan2(p2.y - p1.y, p2.x - p1.x);
          push();
          translate(p2.x - this.x, p2.y - this.y);
          rotate(angle);
          line(0, 0, -10, -5);
          line(0, 0, -10, 5);
          pop();
        } else if (this.type === 'tube') {
          strokeWeight(5);
          line(this.points[0].x - this.x, this.points[0].y - this.y, this.points[1].x - this.x, this.points[1].y - this.y);
        } else if (this.type === 'circle') {
          ellipse(0, 0, this.size);
        } else if (this.type === 'arc') {
          arc(0, 0, this.size, this.size, 0, PI);
        } else if (this.type === 'triangle') {
          triangle(-this.size / 2, this.size / 2, this.size / 2, this.size / 2, 0, -this.size / 2);
        } else if (this.type === 'square') {
          rect(-this.size / 2, -this.size / 2, this.size, this.size);
        } else if (this.type === 'tetrahedron') {
          // 简化为2D投影
          triangle(0, -this.size / 2, -this.size / 2, this.size / 2, this.size / 2, this.size / 2);
          line(0, -this.size / 2, 0, this.size / 2);
        } else if (this.type === 'pyramid') {
          quad(-this.size / 2, this.size / 2, this.size / 2, this.size / 2, this.size / 4, -this.size / 2, -this.size / 4, -this.size / 2);
        } else if (this.type === 'cone') {
          ellipse(0, this.size / 2, this.size, this.size / 2);
          triangle(-this.size / 2, this.size / 2, this.size / 2, this.size / 2, 0, -this.size / 2);
        } else if (this.type === 'cube') {
          rect(-this.size / 2, -this.size / 2, this.size, this.size);
          line(-this.size / 2, -this.size / 2, -this.size / 4, -this.size / 4);
          line(this.size / 2, -this.size / 2, this.size / 4, -this.size / 4);
        } else if (this.type === 'sphere') {
          ellipse(0, 0, this.size);
          ellipse(0, 0, this.size * 0.8, this.size * 0.4);
        } else if (this.type === 'svg') {
          // SVG rendering placeholder (requires external library like SVG.js)
          text('Imported SVG', 0, 0);
        }
        pop();
      }

      drawHandles() {
        push();
        translate(this.x, this.y);
        rotate(this.rotation);
        fill(0, 0, 255);
        noStroke();
        ellipse(this.size / 2, 0, 10, 10); // Resize handle
        fill(255, 0, 0);
        ellipse(0, -this.size / 2, 10, 10); // Rotate handle
        pop();
      }

      isOver(x, y) {
        let d = dist(x, y, this.x, this.y);
        return d < this.size / 2;
      }

      isOverResizeHandle(x, y) {
        let handleX = this.x + cos(this.rotation) * this.size / 2;
        let handleY = this.y + sin(this.rotation) * this.size / 2;
        return dist(x, y, handleX, handleY) < 10;
      }

      isOverRotateHandle(x, y) {
        let handleX = this.x - sin(this.rotation) * this.size / 2;
        let handleY = this.y + cos(this.rotation) * this.size / 2;
        return dist(x, y, handleX, handleY) < 10;
      }

      snapToGrid(x, y) {
        return {
          x: Math.round(x / gridSize) * gridSize,
          y: Math.round(y / gridSize) * gridSize
        };
      }
    }

    function addShape() {
      let type = select('#shape-type').value();
      let color = select('#shape-color').value();
      let shape = new Shape(type, width / 2, height / 2, 50, color);
      shapes.push(shape);
      updateShapeList();
    }

    function mousePressed() {
      for (let shape of shapes.slice().reverse()) {
        if (shape.isOverResizeHandle(mouseX, mouseY)) {
          selectedShape = shape;
          isResizing = true;
          return;
        }
        if (shape.isOverRotateHandle(mouseX, mouseY)) {
          selectedShape = shape;
          isRotating = true;
          return;
        }
        if (shape.isOver(mouseX, mouseY)) {
          selectedShape = shape;
          isDragging = true;
          return;
        }
      }
      selectedShape = null;
    }

    function mouseDragged() {
      if (selectedShape) {
        if (isDragging) {
          let snapped = selectedShape.snapToGrid(mouseX, mouseY);
          selectedShape.x = snapped.x;
          selectedShape.y = snapped.y;
        } else if (isResizing) {
          let d = dist(mouseX, mouseY, selectedShape.x, selectedShape.y);
          selectedShape.size = d * 2;
        } else if (isRotating) {
          let angle = atan2(mouseY - selectedShape.y, mouseX - selectedShape.x);
          selectedShape.rotation = angle;
        }
      }
    }

    function mouseReleased() {
      isDragging = false;
      isResizing = false;
      isRotating = false;
    }

    function keyPressed() {
      if (keyCode === DELETE && selectedShape) {
        shapes = shapes.filter(s => s !== selectedShape);
        selectedShape = null;
        updateShapeList();
      }
    }

    function updateShapeList() {
      let list = select('#shape-list');
      list.html('');
      shapes.forEach((shape, index) => {
        let item = createDiv(`Shape ${index + 1}: ${shape.type}`);
        item.class('shape-item');
        item.attribute('draggable', 'true');
        item.attribute('data-index', index);
        item.parent(list);
        item.elt.addEventListener('dragstart', (e) => {
          e.dataTransfer.setData('text/plain', index);
        });
        item.elt.addEventListener('dragover', (e) => e.preventDefault());
        item.elt.addEventListener('drop', (e) => {
          e.preventDefault();
          let fromIndex = e.dataTransfer.getData('text');
          let toIndex = index;
          let [movedShape] = shapes.splice(fromIndex, 1);
          shapes.splice(toIndex, 0, movedShape);
          updateShapeList();
        });
      });
    }

    function exportSVG() {
      let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
      for (let shape of shapes) {
        svg += shapeToSVG(shape);
      }
      svg += '</svg>';
      let blob = new Blob([svg], { type: 'image/svg+xml' });
      let url = URL.createObjectURL(blob);
      let a = document.createElement('a');
      a.href = url;
      a.download = 'drawing.svg';
      a.click();
      URL.revokeObjectURL(url);
    }

    function shapeToSVG(shape) {
      let svg = '';
      let transform = `transform="translate(${shape.x}, ${shape.y}) rotate(${degrees(shape.rotation)})"`;
      if (shape.type === 'line' || shape.type === 'arrow' || shape.type === 'tube') {
        let [p1, p2] = shape.points;
        svg = `<line x1="${p1.x}" y1="${p1.y}" x2="${p2.x}" y2="${p2.y}" stroke="${shape.color}" stroke-width="${shape.type === 'tube' ? 5 : 1}" ${shape.type === 'arrow' ? 'marker-end="url(#arrow)"' : ''} />`;
      } else if (shape.type === 'circle') {
        svg = `<circle cx="0" cy="0" r="${shape.size / 2}" fill="${shape.color}" ${transform} />`;
      } else if (shape.type === 'arc') {
        svg = `<path d="M ${-shape.size / 2} 0 A ${shape.size / 2} ${shape.size / 2} 0 0 1 ${shape.size / 2} 0" stroke="${shape.color}" fill="none" ${transform} />`;
      } else if (shape.type === 'triangle') {
        svg = `<polygon points="0,${-shape.size / 2} ${-shape.size / 2},${shape.size / 2} ${shape.size / 2},${shape.size / 2}" fill="${shape.color}" ${transform} />`;
      } else if (shape.type === 'square') {
        svg = `<rect x="${-shape.size / 2}" y="${-shape.size / 2}" width="${shape.size}" height="${shape.size}" fill="${shape.color}" ${transform} />`;
      }
      return svg;
    }

    async function saveToDatabase() {
      let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
      for (let shape of shapes) {
        svg += shapeToSVG(shape);
      }
      svg += '</svg>';
      await fetch('/save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ svg })
      });
      alert('保存成功');
    }

    async function loadFromDatabase() {
      let res = await fetch('/load');
      let data = await res.json();
      shapes = [];
      // 解析SVG并还原shapes（简化为清空当前画布）
      alert('加载成功（示例）');
      updateShapeList();
    }
  </script>
</body>
</html>
```

---

### 后端代码（server.js）
使用Node.js和Express连接MySQL数据库，保存和加载SVG数据。

```javascript
const express = require('express');
const mysql = require('mysql2');
const cors = require('cors');
const app = express();

app.use(cors());
app.use(express.json());

const connection = mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: 'your_password',
  database: 'drawing_board'
});

connection.connect(err => {
  if (err) throw err;
  console.log('Connected to MySQL');
});

app.post('/save', (req, res) => {
  const { svg } = req.body;
  const query = 'INSERT INTO drawings (svg_content) VALUES (?)';
  connection.query(query, [svg], (err) => {
    if (err) throw err;
    res.send({ status: 'success' });
  });
});

app.get('/load', (req, res) => {
  const query = 'SELECT svg_content FROM drawings ORDER BY id DESC LIMIT 1';
  connection.query(query, (err, results) => {
    if (err) throw err;
    res.send(results[0] || {});
  });
});

app.listen(3000, () => {
  console.log('Server running on port 3000');
});
```

---

### MySQL数据库初始化
运行以下SQL语句创建数据库和表：

```sql
CREATE DATABASE drawing_board;
USE drawing_board;
CREATE TABLE drawings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  svg_content TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

### 使用说明
1. **运行后端**：
   - 安装Node.js和MySQL。
   - 安装依赖：`npm install express mysql2 cors`.
   - 更新`server.js`中的MySQL连接配置（`host`, `user`, `password`）。
   - 运行`node server.js`启动后端服务。
2. **运行前端**：
   - 将`index.html`托管在静态服务器（如使用`http-server`）。
   - 打开浏览器访问`index.html`。
3. **操作**：
   - 选择图形类型和颜色，点击“添加图形”插入。
   - 点击图形选中，拖动调整位置，拖动蓝色手柄调整大小，红色手柄旋转。
   - 按Delete键删除选中图形。
   - 拖动图形列表中的项调整叠放顺序。
   - 点击“导出SVG”下载SVG文件。
   - 点击“保存到数据库”保存当前画布到MySQL，点击“从数据库加载”加载最近的画布。
4. **网格吸附**：图形拖动时自动吸附到网格点。
5. **关键点吸附**：直线和圆管会吸附到附近图形的关键点（需进一步实现）。
6. **SVG导入**：当前仅支持文件选择，实际渲染需额外库支持。

---

### 注意事项
- **复杂图形（如三棱锥、四棱锥等）**：为了简化，使用2D投影表示3D图形。
- **SVG导入**：需引入额外的SVG解析库（如SVG.js）实现完整支持。
- **关键点吸附**：当前代码简化为网格吸附，需扩展以支持关键点检测。
- **数据库**：确保MySQL服务运行并正确配置。
- **性能**：大型画布可能需要优化渲染性能。

如果需要进一步扩展某部分功能（如关键点吸附或SVG导入），请告诉我！

---


根据需求（H5画板、支持矢量图形、网格吸附、拖拽旋转、导出SVG、保存到MySQL数据库等），以下是一些开源项目，可以实现部分或全部功能。这些项目可以作为参考或直接用于你的项目，结合自定义开发以满足特定需求。以下是推荐的开源项目，基于网络搜索结果和功能匹配度：

---

### 1. SVG-Edit
- **项目地址**: [GitHub - SVG-Edit/svgedit](https://github.com/SVG-Edit/svgedit)[](https://github.com/SVG-Edit/svgedit)
- **描述**: SVG-Edit 是一个基于浏览器的JavaScript驱动的SVG绘图编辑器，支持在现代浏览器中运行。它基于SVG画布，提供矢量图形编辑功能。
- **相关功能**:
  - 支持绘制直线、圆、矩形、多边形等基本矢量图形。
  - 支持拖拽、调整大小、旋转图形。
  - 支持导出SVG文件。
  - 提供基本的图形编辑工具栏，用户可以选择颜色、线条粗细等。
- **缺失功能**:
  - 不支持网格吸附（需要自定义扩展）。
  - 不支持直接保存到MySQL数据库（需添加后端API）。
  - 不支持复杂3D图形（如三棱锥、四棱锥、圆锥体）的2D投影。
  - SVG导入功能有限，可能需要额外开发。
- **适用性**: 适合作为基础框架，扩展网格吸附和数据库保存功能。代码结构清晰，易于修改。
- **如何使用**: 可直接在HTML中引入其JavaScript库，或克隆仓库进行本地开发。

---

### 2. Snap.svg
- **项目地址**: [snapsvg.io](http://snapsvg.io/)[](http://snapsvg.io/)
- **描述**: Snap.svg 是一个JavaScript库，专注于操作和创建SVG图形，提供交互式、响应式的矢量图形编辑功能。
- **相关功能**:
  - 支持创建和编辑矢量图形（如直线、圆、弧线、多边形等）。
  - 支持拖拽、旋转、缩放等交互操作。
  - 提供网格吸附的扩展工具（如Snap.svg.FreeTransform插件）。[](https://github.com/ibrierley/Snap.svg.FreeTransform)
  - 支持导出SVG内容。
- **缺失功能**:
  - 不直接支持MySQL数据库存储（需后端实现）。
  - 3D图形（如三棱锥、圆锥体）需手动实现2D投影。
  - 界面需自行开发（如工具栏、图形列表）。
- **适用性**: 适合需要高度自定义的场景，结合Snap.svg.FreeTransform可快速实现拖拽、旋转、网格吸附功能。需额外开发后端和UI。
- **如何使用**: 通过CDN引入Snap.svg库，或使用npm安装，结合HTML5 Canvas或SVG元素开发。

---

### 3. Fabric.js
- **项目地址**: [GitHub - fabricjs/fabric.js](https://github.com/fabricjs/fabric.js)
- **描述**: Fabric.js 是一个功能强大的HTML5 Canvas库，支持矢量图形编辑，广泛用于交互式绘图应用。
- **相关功能**:
  - 支持绘制直线、圆、矩形、多边形等图形，支持颜色选择。
  - 支持拖拽、缩放、旋转图形。
  - 支持网格吸附（需自定义实现，参考社区插件）。
  - 支持导入和导出SVG文件。
  - 支持复杂交互，如图形分组、叠放顺序调整。
- **缺失功能**:
  - 不直接支持MySQL数据库存储（需后端API）。
  - 复杂3D图形需手动实现2D投影。
  - 网格吸附和关键点吸附需额外开发。
- **适用性**: Fabric.js 是功能最全面的Canvas库之一，适合快速构建交互式画板。扩展性强，可通过插件实现网格吸附和数据库功能。
- **如何使用**: 通过CDN或npm引入Fabric.js，结合HTML5 Canvas开发界面和交互逻辑。

---

### 4. Paper.js
- **项目地址**: [GitHub - paperjs/paper.js](https://github.com/paperjs/paper.js)
- **描述**: Paper.js 是一个基于HTML5 Canvas的矢量图形框架，专注于交互式图形编辑，适合复杂的矢量图形操作。
- **相关功能**:
  - 支持绘制直线、圆、弧线、多边形等图形。
  - 支持拖拽、缩放、旋转等交互。
  - 支持网格吸附（需自定义实现）。
  - 支持导出SVG。
- **缺失功能**:
  - 不支持直接保存到MySQL（需后端支持）。
  - 复杂3D图形需手动实现。
  - 图形列表管理需自行开发。
- **适用性**: 适合需要高性能矢量图形编辑的场景，代码灵活但需要更多自定义开发。
- **如何使用**: 通过CDN或npm引入Paper.js，结合Canvas实现画板功能。

---

### 5. Konva.js
- **项目地址**: [GitHub - konvajs/konva](https://github.com/konvajs/konva)
- **描述**: Konva.js 是一个高性能的HTML5 Canvas库，专注于2D图形编辑，支持复杂交互。
- **相关功能**:
  - 支持直线、圆、矩形、多边形等图形绘制。
  - 支持拖拽、缩放、旋转、叠放顺序调整。
  - 支持网格吸附（需自定义）。
  - 支持导出SVG（通过插件或手动转换）。
- **缺失功能**:
  - 不支持MySQL数据库存储（需后端）。
  - 3D图形投影需手动实现。
  - SVG导入需额外开发。
- **适用性**: 适合需要高性能交互的场景，易于集成到现有项目中。
- **如何使用**: 通过CDN或npm引入Konva.js，结合Canvas实现画板。

---

### 6. Custom SVG Canvas Editor (YouTube Tutorial)
- **参考资源**: [YouTube - Build a SVG Canvas Editor in Browser](https://www.youtube.com)[](https://www.youtube.com/watch?v=UpozMfZP1V4)
- **描述**: 这是一个教程，展示如何使用HTML、CSS和JavaScript构建一个SVG编辑器，基于HTML5 Canvas。
- **相关功能**:
  - 支持基本的SVG图形绘制和编辑。
  - 支持导出SVG文件。
  - 可扩展以支持拖拽、旋转等交互。
- **缺失功能**:
  - 不支持网格吸附、关键点吸附（需扩展）。
  - 不支持数据库存储（需后端）。
  - 复杂图形（如三棱锥）需手动实现。
- **适用性**: 适合学习如何从头构建SVG编辑器，代码简单但需要大量自定义开发。
- **如何使用**: 参考教程代码，结合后端（如Node.js）实现完整功能。

---

### 如何选择
- **如果需要快速上手**: Fabric.js 是最佳选择，因其功能全面、社区活跃、文档完善，适合快速实现拖拽、旋转、导出SVG等功能。
- **如果需要轻量级框架**: Snap.svg 结合FreeTransform插件适合轻量级项目，尤其是需要网格吸附的场景。[](https://github.com/ibrierley/Snap.svg.FreeTransform)
- **如果需要现成编辑器**: SVG-Edit 提供开箱即用的编辑器，适合快速原型开发，但需扩展数据库功能。[](https://github.com/SVG-Edit/svgedit)
- **如果需要高性能**: Konva.js 或 Paper.js 适合高性能场景，但需要更多自定义开发。
- **数据库集成**: 所有项目都不直接支持MySQL存储，需自行开发后端API（如Node.js + Express + MySQL，参考我的前述代码）。

---

### 实现缺失功能的建议
1. **网格吸附**: 使用Fabric.js或Snap.svg的插件（如Snap.svg.FreeTransform），通过计算鼠标坐标与网格点的距离实现吸附。[](https://github.com/ibrierley/Snap.svg.FreeTransform)
2. **关键点吸附**: 遍历所有图形的边界点，检测鼠标坐标与关键点的接近程度，自动调整线条端点坐标。
3. **3D图形投影**: 手动定义三棱锥、四棱锥、圆锥体等的2D投影路径（如前述代码中的实现）。
4. **MySQL存储**: 使用Node.js和Express搭建API，参考我的`server.js`代码，将SVG内容保存为TEXT字段。
5. **SVG导入**: 使用SVG.js或Fabric.js的SVG解析功能，加载外部SVG文件并转换为可编辑对象。

---

### 推荐开发路径
1. **基础框架**: 选择Fabric.js作为前端核心库，因其支持大部分所需功能且扩展性强。
2. **网格和关键点吸附**: 扩展Fabric.js，添加网格绘制和吸附逻辑（参考Snap.svg.FreeTransform的实现）。
3. **后端集成**: 使用Node.js + Express + MySQL实现SVG数据的保存和加载。
4. **UI开发**: 基于我的`index.html`代码，完善工具栏和图形列表，添加拖拽排序功能。
5. **测试和优化**: 在现代浏览器中测试，确保交互流畅，优化大规模图形渲染性能。

如果你需要针对某个项目的具体集成代码或进一步定制某部分功能，请告诉我，我可以提供更详细的实现！








好的，创建一个 H5 矢量画板并支持将 SVG 保存到 MySQL 数据库是一个相对复杂的项目，涉及到前端的图形绘制、交互处理以及后端的存储逻辑。下面我将提供一个实现思路和关键代码片段的概述。

**核心思路：**

1.  **前端 (H5 + JavaScript)**：
    * 使用 HTML 构建画板的基本结构。
    * 使用 CSS 进行样式美化，包括画布、工具栏等。
    * 使用 JavaScript (可以考虑使用现成的库如 `fabric.js`, `SVG.js`, `Konva.js` 等来简化 SVG 操作，或者原生 SVG 操作) 来实现以下功能：
        * 画布初始化及背景网格绘制。
        * 图形（直线、带箭头直线、圆管、圆、弧线、三角形、正方形、以及简化的3D图形表示和导入SVG）的创建和插入。
        * 图形的交互操作：拖动、调整大小、旋转。
        * 图形的吸附功能：吸附到网格、吸附到其他图形的关键点。
        * 图形的删除。
        * 图形列表及拖拽调整叠放顺序。
        * 导出为 SVG 文件。
2.  **后端 (例如 Node.js + Express + MySQL)**：
    * 提供 API 接口，用于接收前端发送的 SVG 数据。
    * 连接 MySQL 数据库。
    * 将接收到的 SVG 数据存储到数据库中。

**技术选型建议：**

* **前端图形库**: 强烈建议使用 `fabric.js`。它对 SVG 有良好的支持，并且内置了对象模型、事件处理、拖拽、缩放、旋转等功能，可以大大简化开发。
* **后端**: Node.js + Express 是一个轻量级且高效的选择，与 JavaScript 前端语言统一。
* **数据库**: MySQL 按要求使用。

---

## 前端实现 (H5 + JavaScript - 以 fabric.js 为例)

### 1. HTML 结构 (`index.html`)

```html
<!DOCTYPE html>
<html>
<head>
    <title>H5 矢量画板</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="toolbar">
        <button id="addLine">直线</button>
        <button id="addArrowLine">箭头直线</button>
        <button id="addCircle">圆</button>
        <button id="addRect">正方形</button>
        <button id="addTriangle">三角形</button>
        <input type="color" id="shapeColor" value="#000000">
        <button id="deleteObject">删除选中</button>
        <button id="exportSVG">导出SVG</button>
        <button id="saveToDB">保存到数据库</button>
        <input type="file" id="importSVGFile" accept=".svg">
    </div>
    <div class="canvas-container">
        <canvas id="c"></canvas>
    </div>
    <div class="layer-list-container">
        <h3>图层列表</h3>
        <ul id="layerList"></ul>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
```

### 2. CSS 样式 (`style.css`)

```css
body {
    font-family: sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0;
}

.toolbar {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #f0f0f0;
    border-radius: 5px;
}

.toolbar button, .toolbar input {
    margin-right: 5px;
}

.canvas-container {
    border: 1px solid #ccc;
    margin-bottom: 10px;
}

/* 网格背景 */
#c {
    /* background-image: linear-gradient(to right, #e0e0e0 1px, transparent 1px),
                       linear-gradient(to bottom, #e0e0e0 1px, transparent 1px);
    background-size: 20px 20px; */ /* 通过JS动态绘制网格更灵活 */
}

.layer-list-container {
    width: 200px;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

#layerList {
    list-style: none;
    padding: 0;
    margin: 0;
}

#layerList li {
    padding: 5px;
    border-bottom: 1px solid #eee;
    cursor: grab;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#layerList li:hover {
    background-color: #f9f9f9;
}
```

### 3. JavaScript 逻辑 (`app.js`)

```javascript
const canvas = new fabric.Canvas('c', {
    width: 800,
    height: 600,
    backgroundColor: '#fff',
    // selection: true, // fabric.js 默认开启
    // skipTargetFind: false,
});

const grid = 20; // 网格大小
let currentShapeFunction = null; // 当前要绘制的图形函数
let isDrawingMode = false;
let startPoint = null;

// --- 1. 支持画布背景网格 ---
function drawGrid() {
    const gridColor = '#e0e0e0';
    // 垂直线
    for (let i = 0; i < (canvas.width / grid); i++) {
        canvas.add(new fabric.Line([i * grid, 0, i * grid, canvas.height], {
            stroke: gridColor,
            selectable: false, // 网格线不可选中
            evented: false,   // 网格线不响应事件
            excludeFromExport: true // 导出SVG时不包含网格线
        }));
    }
    // 水平线
    for (let i = 0; i < (canvas.height / grid); i++) {
        canvas.add(new fabric.Line([0, i * grid, canvas.width, i * grid], {
            stroke: gridColor,
            selectable: false,
            evented: false,
            excludeFromExport: true
        }));
    }
    canvas.sendToBackGrid(); // fabric.js 没有这个方法，需要手动将网格线置于底层
    // 手动将网格线置于底层 (Fabric.js 5.x+)
    const objects = canvas.getObjects();
    objects.forEach(obj => {
        if (obj.excludeFromExport) { // 假设网格线都设置了这个属性
            canvas.sendToBack(obj);
        }
    });
    canvas.renderAll();
}
drawGrid();


// --- 工具函数 ---
function getShapeColor() {
    return document.getElementById('shapeColor').value;
}

// --- 2. 支持以不同颜色插入图形 ---

// 直线
document.getElementById('addLine').addEventListener('click', () => {
    const line = new fabric.Line([50, 50, 200, 50], {
        left: 50,
        top: 50,
        stroke: getShapeColor(),
        strokeWidth: 2
    });
    canvas.add(line);
    updateLayerList();
});

// 带箭头直线 (Fabric.js 本身不直接支持箭头，需要自定义或组合)
// 简单实现：画一条线 + 一个三角形作为箭头
function createArrowLine(x1, y1, x2, y2, color) {
    const line = new fabric.Line([x1, y1, x2, y2], {
        stroke: color,
        strokeWidth: 2,
        selectable: false, // 组合的一部分，单独不可选
        evented: false,
    });

    const angle = Math.atan2(y2 - y1, x2 - x1);
    const arrowSize = 10;
    const arrow = new fabric.Triangle({
        left: x2,
        top: y2,
        originX: 'center',
        originY: 'center',
        width: arrowSize,
        height: arrowSize * 1.5, // 调整箭头形状
        fill: color,
        angle: fabric.util.radiansToDegrees(angle) + 90, // 调整角度使尖端指向线的末端
        selectable: false,
        evented: false,
    });
    // 调整箭头位置使其尖端在线的末端
    arrow.left -= (arrowSize / 2) * Math.sin(angle);
    arrow.top += (arrowSize / 2) * Math.cos(angle);


    const group = new fabric.Group([line, arrow], {
        left: x1 < x2 ? x1 : x2, // 粗略计算group的位置
        top: y1 < y2 ? y1 : y2,
        // lockMovementX: true, // 可以根据需要锁定
        // lockMovementY: true,
        // hasControls: false // 组合的控制点可能不直观，可以考虑自定义控制逻辑
    });
    return group;
}

document.getElementById('addArrowLine').addEventListener('click', () => {
    const arrowLine = createArrowLine(70, 70, 220, 70, getShapeColor());
    canvas.add(arrowLine);
    updateLayerList();
});


// 圆管 (可以用两个同心圆或者一个带描边的粗圆环表示)
// 这里用一个带粗描边的圆实现
document.getElementById('addCirclePipe').addEventListener('click', () => { // 假设有此按钮
    const circlePipe = new fabric.Circle({
        radius: 30,
        fill: 'transparent', // 透明填充
        stroke: getShapeColor(),
        strokeWidth: 10, // 圆管的厚度
        left: 100,
        top: 100
    });
    canvas.add(circlePipe);
    updateLayerList();
});


// 圆
document.getElementById('addCircle').addEventListener('click', () => {
    const circle = new fabric.Circle({
        radius: 30,
        fill: getShapeColor(),
        left: 100,
        top: 100
    });
    canvas.add(circle);
    updateLayerList();
});

// 弧线 (Fabric.js 的 Path 对象可以创建弧线)
document.getElementById('addArc').addEventListener('click', () => { // 假设有此按钮
    // M = moveto, A = arc
    // A rx ry x-axis-rotation large-arc-flag sweep-flag x y
    const arc = new fabric.Path('M 50 50 A 30 30 0 0 1 100 100', {
        fill: 'transparent', // 弧线通常只有描边
        stroke: getShapeColor(),
        strokeWidth: 2,
        left: 150,
        top: 150
    });
    canvas.add(arc);
    updateLayerList();
});

// 三角形
document.getElementById('addTriangle').addEventListener('click', () => {
    const triangle = new fabric.Triangle({
        width: 50,
        height: 70,
        fill: getShapeColor(),
        left: 150,
        top: 50
    });
    canvas.add(triangle);
    updateLayerList();
});

// 正方形 (使用 Rect)
document.getElementById('addRect').addEventListener('click', () => {
    const rect = new fabric.Rect({
        width: 50,
        height: 50,
        fill: getShapeColor(),
        left: 200,
        top: 100
    });
    canvas.add(rect);
    updateLayerList();
});


// --- 3D 图形 (在2D画布上通常是其2D投影或简化表示) ---
// fabric.js 主要用于2D。真正的3D需要WebGL (如Three.js)。
// 这里我们用组合的2D图形来模拟。

// 正方体 (用多个矩形组合) - 这是一个简化的示例
function createCube(x, y, size, color) {
    const face1 = new fabric.Rect({ // Front
        width: size, height: size, fill: color, stroke: 'black', strokeWidth: 1,
        originX: 'center', originY: 'center'
    });
    const face2 = new fabric.Rect({ // Top
        width: size, height: size * 0.5, fill: fabric.Color.fromHex(color).brighten(0.2).toRgb(), stroke: 'black', strokeWidth: 1,
        skewX: 30, originX: 'center', originY: 'bottom'
    });
    const face3 = new fabric.Rect({ // Side
        width: size * 0.5, height: size, fill: fabric.Color.fromHex(color).darken(0.2).toRgb(), stroke: 'black', strokeWidth: 1,
        skewY: -30, originX: 'left', originY: 'center' // fabric.js 中 skewY 的效果可能需要调整
    });

    //  定位这些面以形成一个看起来像3D的立方体是复杂的，并且需要精确计算变换。
    //  对于简单的视觉表示，可以手动调整它们的相对位置。
    //  一个更健壮的方法是使用预先设计好的SVG作为立方体。
    const cubeGroup = new fabric.Group([face1, face2, face3], {
        left: x,
        top: y,
        // subTargetCheck: true // 允许选择组内对象, 可能需要也可能不需要
    });
    return cubeGroup;
}
// document.getElementById('addCube').addEventListener('click', () => {
//     const cube = createCube(250, 250, 50, getShapeColor());
//     canvas.add(cube);
//     updateLayerList();
// });
// 对于三棱锥、四棱锥、圆锥体、球体：
// - 球体: 可以用一个圆形加上渐变填充来模拟光照效果。
// - 圆锥体: 一个三角形 + 一个椭圆底部。
// - 棱锥: 多个三角形组合。
// **强烈建议**: 对于复杂的3D形状，直接导入设计好的SVG图片会更简单高效。

// 导入其他SVG图片
document.getElementById('importSVGFile').addEventListener('change', function (e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function (f) {
            fabric.loadSVGFromString(f.target.result, function (objects, options) {
                const obj = fabric.util.groupSVGElements(objects, options);
                obj.set({
                    left: 50,
                    top: 50,
                    // 可以根据需要调整大小
                    // scaleX: 0.5,
                    // scaleY: 0.5
                });
                canvas.add(obj).renderAll();
                updateLayerList();
            });
        };
        reader.readAsText(file);
        e.target.value = ''; // 清空选择，以便下次还能选择同一个文件
    }
});


// --- 3. 插入的图形可以拖动调整大小、位置，可以旋转。---
// Fabric.js 默认支持这些操作。选中图形后会出现控制点。

// --- 4. 插入的图形可以粘附到网格。 ---
canvas.on('object:moving', function (options) {
    const target = options.target;
    target.set({
        left: Math.round(target.left / grid) * grid,
        top: Math.round(target.top / grid) * grid
    });
});

canvas.on('object:scaling', function(options) { // 调整大小时也吸附到网格
    const target = options.target;
    // 注意：scaling时left/top可能会根据originX/originY变化，吸附逻辑可能需要更复杂
    // 这里简单处理，可能需要根据实际情况调整
    const newWidth = Math.round(target.width * target.scaleX / grid) * grid;
    const newHeight = Math.round(target.height * target.scaleY / grid) * grid;

    if (newWidth > 0 && newHeight > 0) {
        target.scaleX = newWidth / target.width;
        target.scaleY = newHeight / target.height;
    }

    target.set({
        left: Math.round(target.left / grid) * grid,
        top: Math.round(target.top / grid) * grid
    });
});


// --- 5. 支持直线、圆管粘附到图形关键点。 ---
// 这个功能比较复杂，需要检测图形间的靠近程度，并找到关键点（如顶点、中心点）。
// Fabric.js 没有内置此功能，需要自己实现。
// 思路：
// 1. 在 `object:moving` 事件中获取当前拖动的对象。
// 2. 遍历画布上的其他对象，获取它们的关键点坐标 (getBoundingRect, oCoords)。
// 3. 计算拖动对象与这些关键点之间的距离。
// 4. 如果距离小于某个阈值，则将拖动对象的位置设置为该关键点的位置。
// 这是一个简化的概念，实际实现会更复杂。

// --- 6. 插入的图形可以删除。 ---
document.getElementById('deleteObject').addEventListener('click', () => {
    const activeObject = canvas.getActiveObject();
    if (activeObject) {
        if (activeObject.type === 'activeSelection') { // 如果是多选
            activeObject.forEachObject(function(obj) {
                canvas.remove(obj);
            });
        } else { // 单个对象
            canvas.remove(activeObject);
        }
        canvas.discardActiveObject();
        canvas.renderAll();
        updateLayerList();
    }
});

// --- 7. 插入的图形可以在图形列表中拖动调整叠放顺序。 ---
const layerList = document.getElementById('layerList');

function updateLayerList() {
    layerList.innerHTML = '';
    const objects = canvas.getObjects().filter(obj => !obj.excludeFromExport); // 不显示网格线等辅助对象
    objects.forEach((obj, index) => {
        const li = document.createElement('li');
        li.textContent = `${obj.type || 'SVG Group'} #${canvas.getObjects().indexOf(obj)}`; // 显示类型和原始索引
        li.dataset.index = canvas.getObjects().indexOf(obj); // 存储原始索引
        li.draggable = true;

        li.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', e.target.dataset.index);
            e.target.style.opacity = '0.5';
        });

        li.addEventListener('dragend', (e) => {
            e.target.style.opacity = '1';
        });

        li.addEventListener('dragover', (e) => {
            e.preventDefault(); // Necessary to allow drop
            e.target.style.background = '#e0e0e0';
        });
        li.addEventListener('dragleave', (e) => {
            e.target.style.background = '';
        });

        li.addEventListener('drop', (e) => {
            e.preventDefault();
            e.target.style.background = '';
            const fromIndex = parseInt(e.dataTransfer.getData('text/plain'));
            const toIndexOriginal = parseInt(e.target.dataset.index); // 目标元素在原始canvas.getObjects()中的索引

            const objectToMove = canvas.item(fromIndex);
            if (objectToMove) {
                // fabric.js 使用 bringToFront, sendToBack, bringForward, sendBackwards
                // 或者直接操作 _objects 数组然后重新渲染 (不推荐直接操作私有属性)
                // 更安全的方式是 remove 和 addAt
                // 或者计算出需要调用 bringForward/sendBackwards 的次数

                // 获取目标对象在当前画布对象数组中的实际位置
                let targetObjectActualIndex = -1;
                canvas.getObjects().forEach((item, idx) => {
                    if (item === canvas.item(toIndexOriginal)) {
                        targetObjectActualIndex = idx;
                    }
                });

                if (targetObjectActualIndex !== -1) {
                    // 简单地将对象移动到目标索引位置
                    // Fabric.js 没有直接的 moveTo(object, index) 方法，但可以 remove 然后 addAt
                    // 或者，更直观的是使用 fabric.Object.prototype.moveTo(index)
                    objectToMove.moveTo(targetObjectActualIndex);
                }
            }
            canvas.renderAll();
            updateLayerList(); // 重新生成列表以反映新的顺序
        });

        // 点击列表项选中画布中对应的图形
        li.addEventListener('click', () => {
            const objIndex = parseInt(li.dataset.index);
            const targetObj = canvas.item(objIndex);
            if (targetObj) {
                canvas.setActiveObject(targetObj);
                canvas.renderAll();
            }
        });

        layerList.appendChild(li);
    });
}
updateLayerList(); // 初始化

canvas.on('object:added', updateLayerList);
canvas.on('object:removed', updateLayerList);
// canvas.on('object:modified', updateLayerList); // 修改（如旋转）不影响层级，但可以按需更新


// --- 8. 支持导出svg文件。 ---
document.getElementById('exportSVG').addEventListener('click', () => {
    const svg = canvas.toSVG();
    const a = document.createElement('a');
    const blob = new Blob([svg], { type: 'image/svg+xml' });
    a.href = URL.createObjectURL(blob);
    a.download = 'drawing.svg';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(a.href);
});


// --- 保存到数据库 (按钮事件) ---
document.getElementById('saveToDB').addEventListener('click', async () => {
    const svgData = canvas.toSVG();
    try {
        const response = await fetch('/api/save-svg', { // 后端API端点
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ svg: svgData, name: 'MyDrawing' }), // 可以添加其他元数据，如名称
        });
        if (response.ok) {
            const result = await response.json();
            alert('SVG 保存成功! ID: ' + result.id);
        } else {
            const errorData = await response.text();
            alert('SVG 保存失败: ' + errorData);
        }
    } catch (error) {
        console.error('保存SVG时出错:', error);
        alert('保存SVG时出错: ' + error.message);
    }
});

// 初始加载时可以考虑从数据库加载SVG (如果需要编辑功能)
async function loadSVGFromDB(id) {
    try {
        const response = await fetch(`/api/get-svg/${id}`);
        if (response.ok) {
            const data = await response.json();
            if (data && data.svg_data) {
                fabric.loadSVGFromString(data.svg_data, function (objects, options) {
                    canvas.clear(); // 清除现有画布
                    drawGrid(); // 重新绘制网格
                    const obj = fabric.util.groupSVGElements(objects, options);
                    canvas.add(obj).renderAll();
                    updateLayerList();
                });
            }
        } else {
            console.error('加载SVG失败');
        }
    } catch (error) {
        console.error('加载SVG时出错:', error);
    }
}
// 示例: loadSVGFromDB(1); // 加载ID为1的SVG

```

**关于前端的几点说明和补充：**

* **3D 图形**:
    * **三棱锥、四棱锥、圆锥体**: 可以用 `fabric.Path` 或多个 `fabric.Polygon` (三角形) 组合来模拟其2D投影。这需要一些几何计算来确定顶点坐标。例如，圆锥体可以是一个三角形和底部的椭圆组合。
    * **正方体**: 前面给出了一个简化的 `fabric.Group` 示例。更精确的表示可能需要6个经过变换的 `fabric.Rect`。
    * **球体**: `fabric.Circle` 配合径向渐变 (`fabric.Gradient`) 可以模拟光照，产生球体感。
    ```javascript
    // 示例：球体
    const sphere = new fabric.Circle({
        radius: 40,
        left: 300,
        top: 300,
    });
    sphere.setGradient('fill', {
        type: 'radial',
        r1: sphere.radius / 3, // 内圆半径
        r2: sphere.radius,     // 外圆半径
        x1: sphere.width / 2,  // 渐变中心 x
        y1: sphere.height / 2, // 渐变中心 y
        x2: sphere.width / 2,
        y2: sphere.height / 2,
        colorStops: {
            0: '#ffffff',     // 高光颜色
            0.5: getShapeColor(), // 球体基本颜色
            1: fabric.Color.fromHex(getShapeColor()).darken(0.3).toRgb() // 暗部颜色
        }
    });
    canvas.add(sphere);
    ```
* **圆管**: 前面用带 `strokeWidth` 的圆实现了一种方式。另一种是用两个同心圆组合成 `fabric.Group`，外圆填充颜色，内圆填充背景色或透明。
* **直线/圆管吸附到图形关键点**:
    * 当拖动直线或圆管的端点 (如果是自定义的带控制点的直线/圆管) 或整个对象时。
    * 遍历画布上其他对象，获取其 `oCoords` (角点、中点) 或自定义的关键点。
    * 计算距离，如果小于吸附阈值 (e.g., `snapThreshold = 10` pixels)，则将直线端点或对象位置对齐到目标关键点。
    * 对于直线，需要更新其 `x1, y1, x2, y2` 属性。对于圆管，更新 `left, top`。
    * 这是一个高级功能，实现起来需要细致的几何计算和事件处理。
* **图形列表拖拽调整叠放顺序**:
    * Fabric.js 中，对象的叠放顺序由其在 `canvas._objects` 数组中的位置决定。数组末尾的对象在最顶层。
    * `object.bringToFront()`: 将对象移到数组末尾。
    * `object.sendToBack()`: 将对象移到数组开头。
    * `object.bringForward()`: 将对象在数组中上移一位。
    * `object.sendBackwards()`: 将对象在数组中下移一位。
    * `object.moveTo(index)`: 将对象移动到数组的指定索引位置。
    * 在 `drop` 事件中，你需要获取拖动对象和目标对象在 `canvas._objects` 中的当前索引，然后使用 `moveTo` 方法调整。之后务必调用 `updateLayerList()` 来刷新列表的显示顺序。

---

## 后端实现 (Node.js + Express + MySQL)

### 1. 项目设置

```bash
mkdir h5-drawing-board-backend
cd h5-drawing-board-backend
npm init -y
npm install express mysql2 body-parser cors
# mysql2 是一个流行的MySQL驱动
# body-parser 用于解析请求体
# cors 用于处理跨域请求（如果前端和后端不在同一域）
```

### 2. 数据库表结构 (SQL)

```sql
CREATE DATABASE IF NOT EXISTS drawing_db;
USE drawing_db;

CREATE TABLE IF NOT EXISTS saved_svgs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) DEFAULT 'Untitled Drawing',
    svg_data LONGTEXT NOT NULL, -- LONGTEXT 用于存储可能很大的SVG字符串
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. 后端服务器代码 (`server.js`)

```javascript
const express = require('express');
const mysql = require('mysql2/promise'); // 使用 promise 版本的 mysql2
const bodyParser = require('body-parser');
const cors = require('cors');

const app = express();
const port = 3000; // 后端服务端口

// 中间件
app.use(cors()); // 允许所有跨域请求，生产环境应配置具体域名
app.use(bodyParser.json({ limit: '50mb' })); // 解析 JSON 请求体, 增大限制以容纳大的SVG
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

// MySQL 连接池配置 (更推荐使用连接池)
const dbPool = mysql.createPool({
    host: 'localhost',         // 你的 MySQL 主机
    user: 'your_mysql_user',   // 你的 MySQL 用户名
    password: 'your_mysql_password', // 你的 MySQL 密码
    database: 'drawing_db',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

// API 端点

// POST /api/save-svg - 保存SVG数据
app.post('/api/save-svg', async (req, res) => {
    const { name, svg } = req.body;

    if (!svg) {
        return res.status(400).json({ error: 'SVG data is required' });
    }

    try {
        const connection = await dbPool.getConnection();
        const [result] = await connection.execute(
            'INSERT INTO saved_svgs (name, svg_data) VALUES (?, ?)',
            [name || 'Untitled Drawing', svg]
        );
        connection.release();
        res.status(201).json({ message: 'SVG saved successfully', id: result.insertId });
    } catch (error) {
        console.error('Error saving SVG to MySQL:', error);
        res.status(500).json({ error: 'Failed to save SVG', details: error.message });
    }
});

// GET /api/get-svg/:id - 根据ID获取SVG数据
app.get('/api/get-svg/:id', async (req, res) => {
    const { id } = req.params;
    if (!id) {
        return res.status(400).json({ error: 'ID is required' });
    }

    try {
        const connection = await dbPool.getConnection();
        const [rows] = await connection.execute(
            'SELECT id, name, svg_data, created_at FROM saved_svgs WHERE id = ?',
            [id]
        );
        connection.release();

        if (rows.length > 0) {
            res.status(200).json(rows[0]);
        } else {
            res.status(404).json({ error: 'SVG not found' });
        }
    } catch (error) {
        console.error('Error fetching SVG from MySQL:', error);
        res.status(500).json({ error: 'Failed to fetch SVG', details: error.message });
    }
});

// GET /api/list-svgs - 获取所有已保存的SVG列表（可选）
app.get('/api/list-svgs', async (req, res) => {
    try {
        const connection = await dbPool.getConnection();
        const [rows] = await connection.execute(
            'SELECT id, name, created_at FROM saved_svgs ORDER BY created_at DESC'
        );
        connection.release();
        res.status(200).json(rows);
    } catch (error) {
        console.error('Error listing SVGs from MySQL:', error);
        res.status(500).json({ error: 'Failed to list SVGs', details: error.message });
    }
});


app.listen(port, () => {
    console.log(`Backend server running at http://localhost:${port}`);
});

// 优雅地关闭连接池
process.on('SIGINT', async () => {
    console.log('Closing MySQL connection pool...');
    await dbPool.end();
    console.log('MySQL connection pool closed.');
    process.exit(0);
});
```

**启动后端服务：**

```bash
node server.js
```

---

**总结与后续步骤：**

1.  **完善前端图形绘制与交互**:
    * 为所有要求的图形（圆管、弧线、三棱锥、四棱锥、圆锥体、球体）添加创建按钮和对应的 `fabric.js` 实现。对于3D图形，考虑是使用组合2D图形模拟还是直接提示用户导入SVG。
    * 实现更精细的吸附逻辑，特别是图形关键点吸附。
    * 优化图层列表的拖拽体验和与 `canvas` 对象的同步。
2.  **连接前端与后端**:
    * 确保前端 `fetch` 请求的 URL (`/api/save-svg`, `/api/get-svg/:id`) 与后端 `Express` 路由匹配。
    * 在前端添加加载已保存SVG的功能（例如，通过一个列表选择或输入ID）。
3.  **错误处理和用户体验**:
    * 在前端和后端都添加更完善的错误处理和用户反馈。
    * 考虑加载状态、禁用按钮等UI细节。
4.  **安全性**:
    * 对于数据库操作，防止SQL注入（`mysql2` 的预处理语句有助于此）。
    * 如果部署到公网，考虑API的认证和授权。
5.  **代码组织**:
    * 随着功能增多，将前端 JavaScript 代码模块化（例如，每个图形类型一个模块，交互逻辑一个模块）。

这个概述提供了一个比较完整的框架。每个具体功能的实现都需要进一步细化代码。使用 `fabric.js` 会极大地简化前端的图形操作部分。