要判断 `.env.local` 文件是否存在，可以使用 Node.js 的 `fs`（文件系统）模块来检查文件路径。如果文件不存在，可以提供默认行为（如回退到 `.env` 文件、抛出错误或跳过加载）。以下是修改后的 `scripts/fetch-azure-tts.js`，添加了 `.env.local` 文件存在性检查，并保持 ES Module 语法（因为你的项目使用 `"type": "module"`）。

---

### 修改后的 `fetch-azure-tts.js`
以下代码在加载 `.env.local` 之前检查文件是否存在，如果不存在则回退到 `.env` 或抛出错误（根据你的需求选择）。

```javascript
import { config } from 'dotenv';
import path from 'path';
import fs from 'fs/promises'; // 使用 promises API 进行异步文件操作
import mysql from 'mysql2/promise';

async function loadEnvFile() {
  const envLocalPath = path.resolve(process.cwd(), '.env.local');
  const envPath = path.resolve(process.cwd(), '.env');

  try {
    // 检查 .env.local 是否存在
    await fs.access(envLocalPath, fs.constants.F_OK);
    console.log('.env.local found, loading it...');
    config({ path: envLocalPath });
  } catch (error) {
    console.warn('.env.local not found, falling back to .env or skipping...');
    // 回退到 .env（可选）
    try {
      await fs.access(envPath, fs.constants.F_OK);
      console.log('.env found, loading it...');
      config({ path: envPath });
    } catch (fallbackError) {
      console.error('No .env file found. Please provide .env.local or .env with database credentials.');
      throw new Error('Environment file missing');
    }
  }

  // 验证环境变量是否加载
  if (!process.env.DB_HOST || !process.env.DB_USER || !process.env.DB_PASSWORD || !process.env.DB_DATABASE) {
    throw new Error('Missing required database environment variables');
  }
}

async function fetchAzureTTS() {
  try {
    // 加载环境变量
    await loadEnvFile();

    // 连接 MySQL 数据库
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
    });

    // 示例：查询需要 TTS 的记录
    const [rows] = await connection.execute(`
      SELECT id, word FROM words_english WHERE voice_id_uk IS NULL LIMIT 10
    `);

    // 处理 Azure TTS 逻辑（占位）
    for (const row of rows) {
      console.log(`Processing word: ${row.word}`);
      // 在此处添加 Azure TTS API 调用逻辑
      await connection.execute(`
        UPDATE words_english_chinese
        SET voice_id_uk = LOWER(CONCAT(
          HEX(RANDOM_BYTES(4)), '-',
          HEX(RANDOM_BYTES(2)), '-',
          '4', HEX(RANDOM_BYTES(2)), '-',
          HEX(FLOOR(RAND() * 4) + 8), HEX(RANDOM_BYTES(2)), '-',
          HEX(RANDOM_BYTES(6))
        ))
        WHERE english_id = ?
      `, [row.id]);
    }

    await connection.end();
    console.log('Azure TTS fetch and update completed');
  } catch (error) {
    console.error('Error:', error.message);
    throw error;
  }
}

fetchAzureTTS();
```

---

### 关键修改点
1. **检查文件存在性**：
    - 使用 `fs/promises` 的 `fs.access` 方法异步检查 `.env.local` 文件是否存在：
      ```javascript
      await fs.access(envLocalPath, fs.constants.F_OK);
      ```
    - `fs.constants.F_OK` 检查文件是否可访问（存在）。
    - 如果 `.env.local` 不存在，捕获错误并回退到 `.env`（可选）。

2. **回退机制**：
    - 如果 `.env.local` 不存在，尝试加载 `.env` 文件：
      ```javascript
      await fs.access(envPath, fs.constants.F_OK);
      config({ path: envPath });
      ```
    - 如果 `.env` 也不存在，抛出错误提示用户提供环境文件。

3. **环境变量验证**：
    - 确保所有必要的环境变量（如 `DB_HOST`、`DB_USER` 等）都已加载：
      ```javascript
      if (!process.env.DB_HOST || !process.env.DB_USER || !process.env.DB_PASSWORD || !process.env.DB_DATABASE) {
        throw new Error('Missing required database environment variables');
      }
      ```

4. **模块导入**：
    - 使用 ES Module 语法 `import fs from 'fs/promises'`，因为你的项目使用 `"type": "module"`.
    - `fs/promises` 提供异步文件操作，适合现代 Node.js 脚本。

---

### 依赖项
确保已安装必要的依赖：
```bash
npm install dotenv mysql2
```

`fs` 和 `path` 是 Node.js 内置模块，无需额外安装。

---

### 示例 `.env.local` 文件
确保 `.env.local` 文件存在于项目根目录，并包含必要的配置：
```env
DB_HOST=localhost
DB_USER=your_username
DB_PASSWORD=your_password
DB_DATABASE=your_database
AZURE_TTS_KEY=your_azure_tts_subscription_key
AZURE_TTS_REGION=your_azure_region
```

---

### 运行脚本
运行脚本以验证：
```bash
node scripts/fetch-azure-tts.js
```

#### 预期输出：
- 如果 `.env.local` 存在：
  ```
  .env.local found, loading it...
  Processing word: <word1>
  Processing word: <word2>
  ...
  Azure TTS fetch and update completed
  ```
- 如果 `.env.local` 不存在但 `.env` 存在：
  ```
  .env.local not found, falling back to .env or skipping...
  .env found, loading it...
  Processing word: <word1>
  ...
  ```
- 如果两者都不存在：
  ```
  .env.local not found, falling back to .env or skipping...
  No .env file found. Please provide .env.local or .env with database credentials.
  Error: Environment file missing
  ```

---

### 替代方案：不回退到 `.env`
如果你只想加载 `.env.local` 且不回退到 `.env`（即如果 `.env.local` 不存在就直接报错），可以简化 `loadEnvFile` 函数：

```javascript
async function loadEnvFile() {
  const envLocalPath = path.resolve(process.cwd(), '.env.local');

  try {
    await fs.access(envLocalPath, fs.constants.F_OK);
    console.log('.env.local found, loading it...');
    config({ path: envLocalPath });
  } catch (error) {
    console.error('.env.local not found. Please create a .env.local file with database credentials.');
    throw new Error('Missing .env.local file');
  }

  if (!process.env.DB_HOST || !process.env.DB_USER || !process.env.DB_PASSWORD || !process.env.DB_DATABASE) {
    throw new Error('Missing required database environment variables');
  }
}
```

---

### 生产环境考虑
- **`.env.local` 不用于生产**：`.env.local` 通常仅用于本地开发。生产环境中，建议将环境变量配置在服务器环境（如 Vercel 仪表板、Docker 或云服务）。
- **文件路径**：确保 `path.resolve(process.cwd(), '.env.local')` 正确指向文件。如果脚本在子目录运行，确认 `process.cwd()` 返回项目根目录。
- **Cron 调度**：如果脚本需要定期运行（如通过 `node-cron` 或系统 `cron`），确保 `.env.local` 文件在运行环境中可访问，或将变量注入到环境中：
  ```bash
  DB_HOST=localhost DB_USER=your_username node scripts/fetch-azure-tts.js
  ```

---

### 验证
1. **检查文件加载**：
    - 运行脚本，观察日志是否显示 `.env.local found, loading it...`。
    - 如果 `.env.local` 不存在，确认是否正确回退到 `.env` 或抛出错误。

2. **检查数据库更新**：
    - 运行后，查询数据库确认 `voice_id_uk` 是否更新：
      ```sql
      SELECT id, voice_id_uk FROM words_english_chinese WHERE voice_id_uk IS NOT NULL LIMIT 10;
      ```

3. **测试文件不存在**：
    - 临时重命名 `.env.local`（如改为 `.env.local.bak`），运行脚本，验证是否按预期处理（回退或报错）。

---

### Azure TTS 集成（可选）
如果你的脚本需要调用 Azure TTS API，请确保 `.env.local` 包含 Azure 凭据，并添加相关逻辑。例如：
```javascript
import { SpeechSynthesizer, SpeechConfig } from '@azure/cognitiveservices-speechservices';

async function fetchAzureTTS() {
  await loadEnvFile();

  const speechConfig = SpeechConfig.fromSubscription(
    process.env.AZURE_TTS_KEY,
    process.env.AZURE_TTS_REGION
  );
  const synthesizer = new SpeechSynthesizer(speechConfig);

  // 数据库和 TTS 逻辑...
}
```

安装 Azure SDK：
```bash
npm install @azure/cognitiveservices-speechservices
```

---

### 总结
通过 `fs.access` 检查 `.env.local` 文件是否存在，并使用 `dotenv` 的 `config({ path: ... })` 加载，你可以安全地从 `.env.local` 读取环境变量。如果文件不存在，可以选择回退到 `.env` 或抛出错误。代码已适配 ES Modules，确保与你的 Next.js 项目兼容。

如果需要进一步优化（如 Azure TTS 具体实现、错误处理或生产部署），请分享更多细节，我可以提供更精确的代码！