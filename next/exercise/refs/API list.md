https://github.com/inokawa/virtua/blob/main/stories/react/advanced/With%20dnd-kit.stories.tsx




I'll help you create a Next.js API route that executes the provided SQL query and returns the results as JSON. I'll assume you're using Next.js 14 with the App Router and a MySQL database with a library like `mysql2` for database connectivity.

Here's the implementation:

```javascript
import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'your_username',
  password: process.env.DB_PASSWORD || 'your_password',
  database: process.env.DB_NAME || 'your_database',
};

// GET handler for the API route
export async function GET() {
  try {
    // Create database connection
    const connection = await mysql.createConnection(dbConfig);

    // Execute the SQL query
    const [rows] = await connection.execute(`
      SELECT words_english.word, words_english_chinese.part_of_speech, words_english_chinese.translation
      FROM words_english_chinese 
      LEFT JOIN words_english 
      ON words_english_chinese.english_id = words_english.id
    `);

    // Close the connection
    await connection.end();

    // Return JSON response
    return NextResponse.json({
      success: true,
      data: rows,
    }, { status: 200 });

  } catch (error) {
    console.error('Database query error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
    }, { status: 500 });
  }
}
```

To use this API route:

1. **Install dependencies**:
    - Ensure you have `mysql2` installed:
      ```bash
      npm install mysql2
      ```

2. **Environment Variables**:
    - Create a `.env.local` file in your Next.js project root and add your database credentials:
      ```
      DB_HOST=your_host
      DB_USER=your_username
      DB_PASSWORD=your_password
      DB_NAME=your_database
      ```

3. **Access the API**:
    - The API will be available at `/api/words`.
    - You can test it by making a GET request to `http://localhost:3000/api/words` (or your deployed URL).
    - The response will be a JSON object with a `success` boolean and a `data` array containing the query results with `word`, `part_of_speech`, and `translation` fields.

4. **Notes**:
    - The SQL query performs a LEFT JOIN to include all records from `words_english_chinese`, even if there's no matching record in `words_english`.
    - Error handling is included to return a 500 status code with an error message if the query fails.
    - The database connection is properly closed after each request to prevent resource leaks.
    - Ensure your database user has the necessary permissions to execute the query.

If you need additional features (e.g., pagination, filtering, or authentication), let me know, and I can modify the code accordingly!

```javascript
import { NextResponse } from 'next/server';
import mysql from 'mysql2/promise';

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'your_username',
  password: process.env.DB_PASSWORD || 'your_password',
  database: process.env.DB_NAME || 'your_database',
};

// GET handler for the API route
export async function GET() {
  try {
    // Create database connection
    const connection = await mysql.createConnection(dbConfig);

    // Execute the SQL query
    const [rows] = await connection.execute(`
      SELECT words_english.word, words_english_chinese.part_of_speech, words_english_chinese.translation
      FROM words_english_chinese 
      LEFT JOIN words_english 
      ON words_english_chinese.english_id = words_english.id
    `);

    // Close the connection
    await connection.end();

    // Return JSON response
    return NextResponse.json({
      success: true,
      data: rows,
    }, { status: 200 });

  } catch (error) {
    console.error('Database query error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
    }, { status: 500 });
  }
}
```