你的问题是希望在按下右箭头键（`ArrowRight`）时，不仅切换到下一个单词（增加 `status.currentWordIndex`），还能重置 2 秒自动播放的定时器（`setInterval`），使自动播放从新的单词开始重新计时 2 秒。当前代码中，`useEffect` 管理定时器，但右箭头键的逻辑在 `handleKeyDown` 函数中（位于 `common.js`），两者是分开的。

以下是用中文详细分析问题，并提供解决方案，结合你的 Next.js、React、`useState` 项目上下文（单词学习应用、键盘事件、定时器）。

---

### 1. 问题分析
- **当前行为**：
  - 你的 `useEffect` 在 `status.isPlaying` 为 `true` 时，每 2 秒运行一次 `setInterval`，递增 `status.currentWordIndex`。
  - 右箭头键通过 `handleKeyDown` 递增 `status.currentWordIndex`，但不会影响定时器。
  - 如果定时器已经运行了一段时间（例如还有 0.5 秒就触发下一次切换），按下右箭头键后，定时器不会重置，可能导致新单词显示后很快再次切换。

- **目标**：
  - 按下右箭头键时，重置定时器的 2 秒计时，确保新单词有完整的 2 秒显示时间。
  - 保持现有功能：自动播放、键盘导航、暂停/播放切换。

- **挑战**：
  - 定时器逻辑在 `useEffect` 中（`page.jsx`），而右箭头键逻辑在 `handleKeyDown`（`common.js`）。
  - 需要在 `handleKeyDown` 中触发定时器重置，但 `timer` 是 `useEffect` 的局部变量，无法直接访问。

---

### 2. 解决方案
为了实现按下右箭头键时重置定时器，我们需要：
1. 使定时器可控（例如通过状态或 ref 管理）。
2. 在 `handleKeyDown` 中通知定时器重置。
3. 确保重置后定时器从头开始计时 2 秒。

以下是具体方案：

#### 方案 1：使用 `useRef` 存储 `timer` 并重置
- **思路**：
  - 使用 `useRef` 存储 `timer`，使其在组件生命周期内可访问。
  - 在 `handleKeyDown` 中，通过状态更新（例如 `resetTimer` 标志）触发定时器重置。
  - 在 `useEffect` 中，监听 `resetTimer` 并重置定时器。

- **实现**：
  1. **修改 `page.jsx`**：
     ```javascript
     // src/app/page.jsx
     "use client";
     import { useState, useEffect, useRef } from 'react';
     import { handleKeyDown } from '../utils/common';

     export default function Page() {
       const [status, setStatus] = useState({
         currentWordIndex: 0,
         isPlaying: false,
         resetTimer: false, // 新增标志
       });
       const timerRef = useRef(null); // 存储 timer

       // 键盘事件
       useEffect(() => {
         const keyDownCallback = (event) =>
           handleKeyDown(event, status, setStatus);
         document.addEventListener('keydown', keyDownCallback);
         return () => {
           document.removeEventListener('keydown', keyDownCallback);
         };
       }, []);

       // 定时器
       useEffect(() => {
         if (status.isPlaying) {
           // 清除旧定时器
           if (timerRef.current) {
             clearInterval(timerRef.current);
           }

           // 设置新定时器
           timerRef.current = setInterval(() => {
             console.debug('auto next word.');
             setStatus((prev) => ({
               ...prev,
               currentWordIndex: prev.currentWordIndex + 1,
             }));
           }, 2000);
         }

         return () => {
           if (timerRef.current) {
             clearInterval(timerRef.current);
             timerRef.current = null;
           }
         };
       }, [status.isPlaying, status.resetTimer]); // 监听 resetTimer

       return (
         <div>
           <h1>当前单词索引: {status.currentWordIndex}</h1>
           <p>播放状态: {status.isPlaying ? '播放中' : '暂停'}</p>
           <p>
             按右箭头键前进，左箭头键后退，按空格键切换播放/暂停
           </p>
         </div>
       );
     }
     ```

  2. **修改 `common.js`**：
     ```javascript
     // src/utils/common.js
     export const handleKeyDown = (event, status, setStatus) => {
       if (event.key === 'F5') {
         event.preventDefault();
         console.log('F5 被阻止');
       }
       if (event.key === 'ArrowRight') {
         console.debug('next word');
         setStatus({
           ...status,
           currentWordIndex: status.currentWordIndex + 1,
           resetTimer: !status.resetTimer, // 触发定时器重置
         });
       }
       if (event.key === 'ArrowLeft') {
         console.debug('previous word');
         setStatus({
           ...status,
           currentWordIndex: Math.max(0, status.currentWordIndex - 1),
           resetTimer: !status.resetTimer, // 可选：左箭头也重置
         });
       }
       if (event.key === ' ') {
         console.debug('play pronunciation');
         setStatus({
           ...status,
           isPlaying: !status.isPlaying,
         });
       }
     };
     ```

- **说明**：
  - **状态更新**：
    - 新增 `status.resetTimer`（布尔值），作为触发定时器重置的标志。
    - 右箭头键更新 `currentWordIndex` 时，切换 `resetTimer`（`!status.resetTimer` 确保每次按键都触发 `useEffect`）。
  - **`useRef`**：
    - `timerRef.current` 存储定时器 ID，允许在 `useEffect` 外访问。
    - 清除定时器时设为 `null`，确保状态清晰。
  - **`useEffect`**：
    - 依赖 `[status.isPlaying, status.resetTimer]`，当 `resetTimer` 变化时重置定时器。
    - 每次运行时，先清除旧定时器（`clearInterval`），再设置新定时器（`setInterval`）。
  - **效果**：
    - 按右箭头键时，`currentWordIndex` 递增，定时器重置，新单词有完整的 2 秒显示时间。
    - 自动播放继续每 2 秒切换单词。

- **优点**：
  - 简单，符合 React 状态驱动模型。
  - `useRef` 确保 `timer` 可控，不依赖全局变量。
- **缺点**：
  - `resetTimer` 标志增加状态复杂性。
  - 每次按键触发 `useEffect`，可能略影响性能（但实际影响微小）。

#### 方案 2：将定时器控制逻辑移到 `handleKeyDown`
- **思路**：
  - 将定时器管理逻辑（`setInterval` 和 `clearInterval`）移到 `handleKeyDown`，由状态变化直接控制。
  - 使用 `useRef` 存储定时器状态，允许 `handleKeyDown` 访问和重置。
  - `useEffect` 只负责清理定时器。

- **实现**：
  1. **修改 `page.jsx`**：
     ```javascript
     // src/app/page.jsx
     "use client";
     import { useState, useEffect, useRef } from 'react';
     import { handleKeyDown } from '../utils/common';

     export default function Page() {
       const [status, setStatus] = useState({
         currentWordIndex: 0,
         isPlaying: false,
       });
       const timerRef = useRef(null);

       // 键盘事件
       useEffect(() => {
         const keyDownCallback = (event) =>
           handleKeyDown(event, status, setStatus, timerRef);
         document.addEventListener('keydown', keyDownCallback);
         return () => {
           document.removeEventListener('keydown', keyDownCallback);
         };
       }, []);

       // 清理定时器（组件卸载时）
       useEffect(() => {
         return () => {
           if (timerRef.current) {
             clearInterval(timerRef.current);
             timerRef.current = null;
           }
         };
       }, []);

       return (
         <div>
           <h1>当前单词索引: {status.currentWordIndex}</h1>
           <p>播放状态: {status.isPlaying ? '播放中' : '暂停'}</p>
           <p>
             按右箭头键前进，左箭头键后退，按空格键切换播放/暂停
           </p>
         </div>
       );
     }
     ```

  2. **修改 `common.js`**：
     ```javascript
     // src/utils/common.js
     export const handleKeyDown = (event, status, setStatus, timerRef) => {
       if (event.key === 'F5') {
         event.preventDefault();
         console.log('F5 被阻止');
       }

       // 辅助函数：设置或重置定时器
       const setupTimer = () => {
         if (timerRef.current) {
           clearInterval(timerRef.current);
         }
         timerRef.current = setInterval(() => {
           console.debug('auto next word.');
           setStatus((prev) => ({
             ...prev,
             currentWordIndex: prev.currentWordIndex + 1,
           }));
         }, 2000);
       };

       if (event.key === 'ArrowRight') {
         console.debug('next word');
         setStatus({
           ...status,
           currentWordIndex: status.currentWordIndex + 1,
         });
         if (status.isPlaying) {
           setupTimer(); // 重置定时器
         }
       }

       if (event.key === 'ArrowLeft') {
         console.debug('previous word');
         setStatus({
           ...status,
           currentWordIndex: Math.max(0, status.currentWordIndex - 1),
         });
         if (status.isPlaying) {
           setupTimer(); // 可选：左箭头也重置
         }
       }

       if (event.key === ' ') {
         console.debug('play pronunciation');
         const newIsPlaying = !status.isPlaying;
         setStatus({
           ...status,
           isPlaying: newIsPlaying,
         });
         if (newIsPlaying) {
           setupTimer(); // 开启播放时设置定时器
         } else if (timerRef.current) {
           clearInterval(timerRef.current); // 暂停时清除
           timerRef.current = null;
         }
       }
     };
     ```

- **说明**：
  - **`timerRef`**：
    - 使用 `useRef` 存储定时器 ID，传递给 `handleKeyDown`。
    - 允许 `handleKeyDown` 直接清除和重置定时器。
  - **`setupTimer`**：
    - 封装定时器设置逻辑，清除旧定时器并创建新定时器。
    - 在右箭头键、左箭头键（可选）或空格键（开启播放）时调用。
  - **键盘事件**：
    - 右箭头键：递增 `currentWordIndex`，如果 `isPlaying` 为 `true`，重置定时器。
    - 空格键：切换 `isPlaying`，开启时设置定时器，暂停时清除。
  - **`useEffect`**：
    - 仅负责组件卸载时清理定时器。
    - 定时器逻辑移到 `handleKeyDown`，减少 `useEffect` 依赖。

- **优点**：
  - 逻辑集中，`handleKeyDown` 直接控制定时器，清晰直观。
  - 避免额外状态（`resetTimer`），减少复杂性。
  - 右箭头键重置定时器只在 `isPlaying` 时触发，符合需求。
- **缺点**：
  - `timerRef` 传递到 `handleKeyDown`，增加函数参数。
  - 需要小心管理 `timerRef.current` 的状态（例如设为 `null`）。

- **推荐**：此方案更简洁，适合你的需求，优先考虑。

#### 方案 3：使用自定义 Hook 封装定时器
- **思路**：
  - 创建一个自定义 Hook（`useWordTimer`），封装定时器逻辑，提供重置方法。
  - 在 `handleKeyDown` 中调用重置方法，触发定时器重置。

- **实现**：
  1. **创建自定义 Hook**：
     ```javascript
     // src/hooks/useWordTimer.js
     import { useEffect, useRef } from 'react';

     export function useWordTimer(isPlaying, setStatus, resetTrigger) {
       const timerRef = useRef(null);

       useEffect(() => {
         if (isPlaying) {
           if (timerRef.current) {
             clearInterval(timerRef.current);
           }
           timerRef.current = setInterval(() => {
             console.debug('auto next word.');
             setStatus((prev) => ({
               ...prev,
               currentWordIndex: prev.currentWordIndex + 1,
             }));
           }, 2000);
         }
         return () => {
           if (timerRef.current) {
             clearInterval(timerRef.current);
             timerRef.current = null;
           }
         };
       }, [isPlaying, resetTrigger, setStatus]);

       return () => {
         if (timerRef.current) {
           clearInterval(timerRef.current);
           timerRef.current = setInterval(() => {
             console.debug('auto next word.');
             setStatus((prev) => ({
               ...prev,
               currentWordIndex: prev.currentWordIndex + 1,
             }));
           }, 2000);
         }
       };
     }
     ```

  2. **修改 `page.jsx`**：
     ```javascript
     // src/app/page.jsx
     "use client";
     import { useState, useEffect } from 'react';
     import { handleKeyDown } from '../utils/common';
     import { useWordTimer } from '../hooks/useWordTimer';

     export default function Page() {
       const [status, setStatus] = useState({
         currentWordIndex: 0,
         isPlaying: false,
         resetTimer: false,
       });

       const resetTimer = useWordTimer(
         status.isPlaying,
         setStatus,
         status.resetTimer
       );

       useEffect(() => {
         const keyDownCallback = (event) =>
           handleKeyDown(event, status, setStatus, resetTimer);
         document.addEventListener('keydown', keyDownCallback);
         return () => {
           document.removeEventListener('keydown', keyDownCallback);
         };
       }, [resetTimer]);

       return (
         <div>
           <h1>当前单词索引: {status.currentWordIndex}</h1>
           <p>播放状态: {status.isPlaying ? '播放中' : '暂停'}</p>
           <p>
             按右箭头键前进，左箭头键后退，按空格键切换播放/暂停
           </p>
         </div>
       );
     }
     ```

  3. **修改 `common.js`**：
     ```javascript
     // src/utils/common.js
     export const handleKeyDown = (event, status, setStatus, resetTimer) => {
       if (event.key === 'F5') {
         event.preventDefault();
         console.log('F5 被阻止');
       }
       if (event.key === 'ArrowRight') {
         console.debug('next word');
         setStatus({
           ...status,
           currentWordIndex: status.currentWordIndex + 1,
           resetTimer: !status.resetTimer, // 触发重置
         });
         if (status.isPlaying) {
           resetTimer(); // 重置定时器
         }
       }
       if (event.key === 'ArrowLeft') {
         console.debug('previous word');
         setStatus({
           ...status,
           currentWordIndex: Math.max(0, status.currentWordIndex - 1),
           resetTimer: !status.resetTimer,
         });
         if (status.isPlaying) {
           resetTimer();
         }
       }
       if (event.key === ' ') {
         console.debug('play pronunciation');
         setStatus({
           ...status,
           isPlaying: !status.isPlaying,
         });
       }
     };
     ```

- **说明**：
  - **自定义 Hook**：
    - `useWordTimer` 封装定时器逻辑，返回 `resetTimer` 函数。
    - 依赖 `resetTrigger`（`status.resetTimer`），当其变化时重置定时器。
  - **键盘事件**：
    - 右箭头键更新 `currentWordIndex` 和 `resetTimer`，并调用 `resetTimer()`。
    - 空格键切换 `isPlaying`，由 Hook 自动管理定时器。
  - **效果**：
    - 按右箭头键时，定时器重置，新单词显示 2 秒。
    - 代码模块化，适合复用。

- **优点**：
  - 逻辑封装清晰，定时器管理独立于组件。
  - 易于扩展（例如调整间隔时间）。
- **缺点**：
  - 引入自定义 Hook，增加代码量。
  - 仍需 `resetTimer` 状态，复杂性略高。

---

### 3. 推荐方案
- **方案 2（将定时器控制逻辑移到 `handleKeyDown`）** 是最佳选择，因为：
  - 逻辑集中，`handleKeyDown` 直接管理定时器重置，清晰直观。
  - 避免额外状态（`resetTimer`），减少复杂性。
  - 只需要 `useRef` 和少量修改，易于实现。
- **方案 1** 适合快速实现，但状态管理稍复杂。
- **方案 3** 适合需要高复用性或复杂定时器逻辑的场景，但对你的简单需求略显繁琐。

以下是基于方案 2 的完整代码：

```javascript
// src/app/page.jsx
"use client";
import { useState, useEffect, useRef } from 'react';
import { handleKeyDown } from '../utils/common';

export default function Page() {
  const [status, setStatus] = useState({
    currentWordIndex: 0,
    isPlaying: false,
  });
  const timerRef = useRef(null);

  useEffect(() => {
    const keyDownCallback = (event) =>
      handleKeyDown(event, status, setStatus, timerRef);
    document.addEventListener('keydown', keyDownCallback);
    return () => {
      document.removeEventListener('keydown', keyDownCallback);
    };
  }, []);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);

  return (
    <div>
      <h1>当前单词索引: {status.currentWordIndex}</h1>
      <p>播放状态: {status.isPlaying ? '播放中' : '暂停'}</p>
      <p>按右箭头键前进，左箭头键后退，按空格键切换播放/暂停</p>
    </div>
  );
}
```

```javascript
// src/utils/common.js
export const handleKeyDown = (event, status, setStatus, timerRef) => {
  if (event.key === 'F5') {
    event.preventDefault();
    console.log('F5 被阻止');
  }

  const setupTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    timerRef.current = setInterval(() => {
      console.debug('auto next word.');
      setStatus((prev) => ({
        ...prev,
        currentWordIndex: prev.currentWordIndex + 1,
      }));
    }, 2000);
  };

  if (event.key === 'ArrowRight') {
    console.debug('next word');
    setStatus({
      ...status,
      currentWordIndex: status.currentWordIndex + 1,
    });
    if (status.isPlaying) {
      setupTimer();
    }
  }

  if (event.key === 'ArrowLeft') {
    console.debug('previous word');
    setStatus({
      ...status,
      currentWordIndex: Math.max(0, status.currentWordIndex - 1),
    });
    if (status.isPlaying) {
      setupTimer();
    }
  }

  if (event.key === ' ') {
    console.debug('play pronunciation');
    const newIsPlaying = !status.isPlaying;
    setStatus({
      ...status,
      isPlaying: newIsPlaying,
    });
    if (newIsPlaying) {
      setupTimer();
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }
};
```

---

### 4. 调试和验证
运行代码后，测试以下场景：
1. **按空格键开启播放**：
   - 确认每 2 秒打印 `auto next word.`，`currentWordIndex` 递增。
2. **按右箭头键**：
   - 确认 `currentWordIndex` 递增。
   - 如果 `isPlaying` 为 `true`，确认下一次自动切换延迟 2 秒（定时器重置）。
3. **按左箭头键**：
   - 确认 `currentWordIndex` 减少（不低于 0）。
   - 如果 `isPlaying` 为 `true`，确认定时器重置。
4. **按空格键暂停**：
   - 确认定时器停止，`auto next word.` 不再打印。

**调试步骤**：
- 添加日志：
  ```javascript
  const setupTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      console.debug('定时器清除:', timerRef.current);
    }
    timerRef.current = setInterval(() => {
      console.debug('auto next word.');
      setStatus((prev) => ({
        ...prev,
        currentWordIndex: prev.currentWordIndex + 1,
      }));
    }, 2000);
    console.debug('定时器设置:', timerRef.current);
  };
  ```
- 检查控制台，确保 `定时器清除` 和 `定时器设置` 在右箭头键按下时正确触发。
- 确认 Chrome 控制台无错误（之前提到的 `searchParams`/`params` 错误已解决）。

---

### 5. 与你的项目上下文
你的单词学习应用可能还涉及：
- **URL 参数**（`?wordIndex=5` 或 `/word/5`）：之前解决了 `searchParams` 和 `params` 错误，确保 `page.jsx` 使用 `React.use()` 或客户端组件。
- **单词列表**：建议添加单词数组，映射 `currentWordIndex`：
  ```javascript
  const words = [
    { word: 'apple', pronunciation: '/ˈæpəl/' },
    { word: 'banana', pronunciation: '/bəˈnænə/' },
  ];

  return (
    <div>
      <h1>单词: {words[status.currentWordIndex]?.word || '无'}</h1>
      <p>发音: {words[status.currentWordIndex]?.pronunciation || ''}</p>
      <p>播放状态: {status.isPlaying ? '播放中' : '暂停'}</p>
    </div>
  );
  ```
- **发音**：空格键可能触发音频播放：
  ```javascript
  if (event.key === ' ') {
    console.debug('play pronunciation');
    const audio = new Audio(
      `/audio/${words[status.currentWordIndex]?.word}.mp3`
    );
    audio.play();
    const newIsPlaying = !status.isPlaying;
    setStatus({ ...status, isPlaying: newIsPlaying });
    if (newIsPlaying) {
      setupTimer();
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }
  ```

---

### 6. 开源项目参考
你之前询问了类似功能的开源软件，以下是如何从推荐项目中学习定时器重置：

- **Qwerty Learner** (https://github.com/Kaiyiwing/qwerty-learner):
  - 虽然无自动播放，但其键盘交互（`src/hooks/useKeyboard.ts`）可参考。
  - 可添加定时器逻辑，模仿你的 `setInterval` 和重置：
    ```javascript
    // 伪代码
    useEffect(() => {
      let timer;
      if (isPlaying) {
        timer = setInterval(() => setWordIndex((prev) => prev + 1), 2000);
      }
      return () => clearInterval(timer);
    }, [isPlaying, resetTrigger]);
    ```

- **Anki** (https://github.com/ankitects/anki):
  - 卡片翻转使用键盘控制（`src/web`），可参考如何在按键时重置计时器。
  - 示例（伪代码）：
    ```javascript
    document.addEventListener('keydown', (e) => {
      if (e.key === 'ArrowRight') {
        nextCard();
        resetTimer();
      }
    });
    ```

---

### 7. 总结
- **需求**：
  - 按右箭头键时，递增 `currentWordIndex` 并重置 2 秒定时器。
- **解决方案**：
  - **方案 2（推荐）**：将定时器逻辑移到 `handleKeyDown`，使用 `useRef` 管理 `timer`，右箭头键调用 `setupTimer` 重置。
  - 方案 1：使用 `resetTimer` 状态触发重置。
  - 方案 3：使用自定义 Hook 封装定时器。
- **效果**：
  - 按右箭头键时，新单词显示完整的 2 秒。
  - 自动播放、暂停、键盘导航保持正常。
- **调试**：
  - 检查控制台日志，确认定时器重置。
  - 测试右箭头键和播放状态的交互。
- **建议**：
  - 实现方案 2，代码简洁且符合需求。
  - 添加单词列表和发音功能，提升应用完整性。
  - 参考 Qwerty Learner 的键盘交互逻辑。

如果仍有问题（例如定时器未重置、界面不更新），请提供：
1. 完整的 `page.jsx` 和 `common.js` 代码。
2. 控制台日志（右箭头键和定时器行为）。
3. 是否需要其他功能（例如发音、单词列表）。
我可以进一步优化或调试！

---

### 8. 代码工件
以下是方案 2 的完整代码，包裹在 `<xaiArtifact>` 标签中，符合你的要求：

```javascript
"use client";
import { useState, useEffect, useRef } from 'react';
import { handleKeyDown } from '../utils/common';

export default function Page() {
  const [status, setStatus] = useState({
    currentWordIndex: 0,
    isPlaying: false,
  });
  const timerRef = useRef(null);

  useEffect(() => {
    const keyDownCallback = (event) =>
      handleKeyDown(event, status, setStatus, timerRef);
    document.addEventListener('keydown', keyDownCallback);
    return () => {
      document.removeEventListener('keydown', keyDownCallback);
    };
  }, []);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, []);

  return (
    <div>
      <h1>当前单词索引: {status.currentWordIndex}</h1>
      <p>播放状态: {status.isPlaying ? '播放中' : '暂停'}</p>
      <p>按右箭头键前进，左箭头键后退，按空格键切换播放/暂停</p>
    </div>
  );
}
```

```javascript
export const handleKeyDown = (event, status, setStatus, timerRef) => {
  if (event.key === 'F5') {
    event.preventDefault();
    console.log('F5 被阻止');
  }

  const setupTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    timerRef.current = setInterval(() => {
      console.debug('auto next word.');
      setStatus((prev) => ({
        ...prev,
        currentWordIndex: prev.currentWordIndex + 1,
      }));
    }, 2000);
  };

  if (event.key === 'ArrowRight') {
    console.debug('next word');
    setStatus({
      ...status,
      currentWordIndex: status.currentWordIndex + 1,
    });
    if (status.isPlaying) {
      setupTimer();
    }
  }

  if (event.key === 'ArrowLeft') {
    console.debug('previous word');
    setStatus({
      ...status,
      currentWordIndex: Math.max(0, status.currentWordIndex - 1),
    });
    if (status.isPlaying) {
      setupTimer();
    }
  }

  if (event.key === ' ') {
    console.debug('play pronunciation');
    const newIsPlaying = !status.isPlaying;
    setStatus({
      ...status,
      isPlaying: newIsPlaying,
    });
    if (newIsPlaying) {
      setupTimer();
    } else if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }
};
```