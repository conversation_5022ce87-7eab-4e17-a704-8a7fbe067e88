感谢提供额外信息！以下是针对 MySQL 数据库和 `lexorank` 库的更新版 API 实现，优化了 `/api/notebook-words` 和 `/api/word-english-chinese` 以处理 3-4 万条数据，并使用 `lexorank` 管理 `notebook_words_english` 表的排序权重。代码将替换之前的 SQLite 实现，改用 MySQL，并确保性能适合较大数据集。

### 环境和假设
- **数据库**：MySQL，包含表 `words_english`, `words_english_chinese`, `notebook_words_english`，结构与之前 SQL 查询一致。
- **依赖**：
  - `mysql2`：MySQL 驱动。
  - `lexorank`：用于生成排序权重。
  - `uuid`：生成唯一 ID。
- **数据规模**：3-4 万条记录，需优化查询性能。
- **音频文件**：`voice_id_us` 和 `voice_id_uk` 映射到 `/audio/{voice_id}.mp3`。
- **目录**：API 路由位于 `pages/api/`，数据库配置在 `lib/db.js`。

### 安装依赖
```bash
npm install mysql2 lexorank uuid
```

### 数据库初始化
创建 `lib/db.js` 用于配置 MySQL 连接和初始化表结构。

```javascript
const mysql = require('mysql2/promise');

const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'your_password',
  database: process.env.DB_NAME || 'word_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

// 初始化表结构
async function initializeDatabase() {
  try {
    const connection = await pool.getConnection();
    
    await connection.query(`
      CREATE TABLE IF NOT EXISTS words_english (
        id INT AUTO_INCREMENT PRIMARY KEY,
        word VARCHAR(255) NOT NULL,
        accent VARCHAR(50),
        script TEXT,
        INDEX idx_word (word)
      );
    `);

    await connection.query(`
      CREATE TABLE IF NOT EXISTS words_english_chinese (
        id INT AUTO_INCREMENT PRIMARY KEY,
        english_id INT,
        part_of_speech VARCHAR(50),
        translation TEXT,
        phonetic_uk VARCHAR(100),
        phonetic_us VARCHAR(100),
        voice_id_uk VARCHAR(100),
        voice_id_us VARCHAR(100),
        script TEXT,
        voice_id_translation VARCHAR(100),
        FOREIGN KEY (english_id) REFERENCES words_english(id) ON DELETE CASCADE,
        INDEX idx_english_id (english_id)
      );
    `);

    await connection.query(`
      CREATE TABLE IF NOT EXISTS notebook_words_english (
        id INT AUTO_INCREMENT PRIMARY KEY,
        notebook_id INT,
        english_id INT,
        chinese_id INT,
        weight VARCHAR(50),
        note TEXT,
        note_explain TEXT,
        FOREIGN KEY (english_id) REFERENCES words_english(id) ON DELETE CASCADE,
        FOREIGN KEY (chinese_id) REFERENCES words_english_chinese(id) ON DELETE SET NULL,
        INDEX idx_notebook_id (notebook_id),
        INDEX idx_weight (weight)
      );
    `);

    // 插入示例数据（仅在开发时使用）
    const [rows] = await connection.query('SELECT COUNT(*) as count FROM words_english');
    if (rows[0].count === 0) {
      await connection.query(`
        INSERT INTO words_english (word, accent) VALUES
        ('apple', 'US'),
        ('banana', 'US');
      `);

      await connection.query(`
        INSERT INTO words_english_chinese (english_id, part_of_speech, translation, phonetic_uk, phonetic_us, voice_id_uk, voice_id_us) VALUES
        (1, 'noun', '苹果', '/ˈæp.l/', '/ˈæpəl/', 'apple_uk', 'apple_us'),
        (2, 'noun', '香蕉', '/bəˈnæn.ə/', '/bəˈnænə/', 'banana_uk', 'banana_us');
      `);

      await connection.query(`
        INSERT INTO notebook_words_english (notebook_id, english_id, chinese_id, weight, note, note_explain) VALUES
        (1, 1, 1, '0|aaaaaa:', 'Sample note', 'Sample explanation'),
        (1, 2, 2, '0|aaaaaa:zzzzzz', 'Sample note', 'Sample explanation');
      `);
    }

    connection.release();
  } catch (error) {
    console.error('Database initialization error:', error);
  }
}

initializeDatabase();

module.exports = pool;
```

**说明**：
- 使用 `mysql2/promise` 提供异步 API，适合现代 Node.js。
- 创建连接池以处理 3-4 万条数据的高并发。
- 添加索引（`idx_word`, `idx_english_id`, `idx_notebook_id`, `idx_weight`）优化查询性能。
- 示例数据使用简单的 LexoRank 值（`0|aaaaaa:` 等），实际排序由 `lexorank` 库管理。
- 环境变量（`DB_HOST`, `DB_USER`, `DB_PASSWORD`, `DB_NAME`）需在 `.env` 文件中配置。

### API 路由 1: `/api/notebook-words`
实现 GET（获取笔记本单词列表，按 LexoRank 排序）和 POST（添加新单词到笔记本，使用 LexoRank 生成权重）。

```javascript
const pool = require('../../lib/db');
const { LexoRank } = require('lexorank');

export default async function handler(req, res) {
  if (req.method === 'GET') {
    try {
      const [rows] = await pool.query(`
        SELECT notebook_words_english.id, notebook_words_english.notebook_id as nid, notebook_words_english.weight, 
               notebook_words_english.note, notebook_words_english.note_explain,
               words_english.id as eid, words_english.word, words_english.accent, words_english.script,
               words_english_chinese.id as cid, words_english_chinese.part_of_speech as pos,
               words_english_chinese.translation, words_english_chinese.phonetic_uk, 
               words_english_chinese.phonetic_us, words_english_chinese.voice_id_uk, 
               words_english_chinese.voice_id_us, words_english_chinese.script as translation_script,
               words_english_chinese.voice_id_translation
        FROM notebook_words_english
        JOIN words_english ON notebook_words_english.english_id = words_english.id
        LEFT JOIN words_english_chinese ON notebook_words_english.chinese_id = words_english_chinese.id
        WHERE notebook_words_english.notebook_id = ?
        ORDER BY notebook_words_english.weight
      `, [req.query.notebook_id || 1]);

      res.status(200).json(rows);
    } catch (error) {
      console.error('GET /api/notebook-words error:', error);
      res.status(500).json({ error: 'Failed to fetch words' });
    }
  } else if (req.method === 'POST') {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      const { word, phonetic_uk, phonetic_us, translations, notebook_id } = req.body;

      // 插入或获取 words_english
      let englishId;
      const [existingWord] = await connection.query('SELECT id FROM words_english WHERE word = ?', [word]);
      if (existingWord.length > 0) {
        englishId = existingWord[0].id;
      } else {
        const [result] = await connection.query(
          'INSERT INTO words_english (word, accent) VALUES (?, ?)',
          [word, 'US']
        );
        englishId = result.insertId;
      }

      // 插入 translations 到 words_english_chinese
      const insertChinese = 'INSERT INTO words_english_chinese (english_id, part_of_speech, translation, phonetic_uk, phonetic_us, voice_id_uk, voice_id_us) VALUES (?, ?, ?, ?, ?, ?, ?)';
      const chineseIds = [];
      for (const trans of translations) {
        if (trans.include) {
          const [result] = await connection.query(insertChinese, [
            englishId,
            trans.pos || null,
            trans.translation || null,
            phonetic_uk || null,
            phonetic_us || null,
            trans.voice_id_uk || `${word}_uk`,
            trans.voice_id_us || `${word}_us`,
          ]);
          chineseIds.push(result.insertId);
        }
      }

      // 获取最后一个 weight 用于 LexoRank
      const [lastWeight] = await connection.query(
        'SELECT weight FROM notebook_words_english WHERE notebook_id = ? ORDER BY weight DESC LIMIT 1',
        [notebook_id || 1]
      );
      const prevRank = lastWeight.length > 0 ? LexoRank.parse(lastWeight[0].weight) : LexoRank.middle();
      const newRank = prevRank.genNext();

      // 插入到 notebook_words_english
      const insertNotebook = 'INSERT INTO notebook_words_english (notebook_id, english_id, chinese_id, weight, note, note_explain) VALUES (?, ?, ?, ?, ?, ?)';
      for (const chineseId of chineseIds) {
        await connection.query(insertNotebook, [
          notebook_id || 1,
          englishId,
          chineseId,
          newRank.toString(),
          null,
          null,
        ]);
      }

      await connection.commit();
      res.status(201).json({ message: 'Word added successfully' });
    } catch (error) {
      await connection.rollback();
      console.error('POST /api/notebook-words error:', error);
      res.status(500).json({ error: 'Failed to add word' });
    } finally {
      connection.release();
    }
  } else {
    res.status(405).json({ error: 'Method not allowed' });
  }
}
```

**说明**：
- **GET**：支持通过 `notebook_id` 查询特定笔记本的单词列表，按 `weight`（LexoRank）排序。使用索引 `idx_notebook_id` 和 `idx_weight` 优化查询性能。
- **POST**：
  - 使用事务确保数据一致性。
  - 检查单词是否存在，复用或创建新 `words_english` 记录。
  - 插入 `include: true` 的翻译到 `words_english_chinese`。
  - 使用 `lexorank` 生成新权重，基于最后一个 `weight` 的 `genNext()`。
  - 插入 `notebook_words_english` 记录，关联单词和翻译。
- **性能**：为 3-4 万条数据，添加索引并使用连接池优化查询和写入性能。

### API 路由 2: `/api/word-english-chinese`
实现 GET（查询单词及其翻译）和 POST（更新单词和翻译）。

```javascript
const pool = require('../../lib/db');
const { v4: uuidv4 } = require('uuid');

export default async function handler(req, res) {
  if (req.method === 'GET') {
    try {
      const { word } = req.query;
      if (!word) {
        return res.status(400).json({ error: 'Word parameter is required' });
      }

      const [wordData] = await pool.query(
        'SELECT id, word, phonetic_uk, phonetic_us FROM words_english WHERE word = ?',
        [word]
      );
      if (!wordData.length) {
        return res.status(200).json({ exists: false });
      }

      const [translations] = await pool.query(
        'SELECT id, part_of_speech as pos, translation, phonetic_uk, phonetic_us, voice_id_uk, voice_id_us FROM words_english_chinese WHERE english_id = ?',
        [wordData[0].id]
      );

      res.status(200).json({
        exists: true,
        word: wordData[0].word,
        phonetic_uk: wordData[0].phonetic_uk || '',
        phonetic_us: wordData[0].phonetic_us || '',
        translations,
      });
    } catch (error) {
      console.error('GET /api/word-english-chinese error:', error);
      res.status(500).json({ error: 'Failed to fetch word' });
    }
  } else if (req.method === 'POST') {
    const connection = await pool.getConnection();
    try {
      await connection.beginTransaction();

      const { word_id, word, phonetic_uk, phonetic_us, translations } = req.body;

      if (!word_id || !word) {
        return res.status(400).json({ error: 'word_id and word are required' });
      }

      // 更新 words_english
      await connection.query(
        'UPDATE words_english SET word = ?, phonetic_uk = ?, phonetic_us = ? WHERE id = ?',
        [word, phonetic_uk || null, phonetic_us || null, word_id]
      );

      // 删除旧的翻译
      await connection.query('DELETE FROM words_english_chinese WHERE english_id = ?', [word_id]);

      // 插入新的翻译
      const insertChinese = 'INSERT INTO words_english_chinese (english_id, part_of_speech, translation, phonetic_uk, phonetic_us, voice_id_uk, voice_id_us) VALUES (?, ?, ?, ?, ?, ?, ?)';
      const chineseIds = [];
      for (const trans of translations) {
        if (trans.include) {
          const [result] = await connection.query(insertChinese, [
            word_id,
            trans.pos || null,
            trans.translation || null,
            phonetic_uk || null,
            phonetic_us || null,
            trans.voice_id_uk || `${word}_uk_${uuidv4().slice(0, 8)}`,
            trans.voice_id_us || `${word}_us_${uuidv4().slice(0, 8)}`,
          ]);
          chineseIds.push(result.insertId);
        }
      }

      // 更新 notebook_words_english 的 chinese_id（取第一条翻译）
      if (chineseIds.length > 0) {
        await connection.query(
          'UPDATE notebook_words_english SET chinese_id = ? WHERE english_id = ?',
          [chineseIds[0], word_id]
        );
      }

      await connection.commit();
      res.status(200).json({ message: 'Word updated successfully' });
    } catch (error) {
      await connection.rollback();
      console.error('POST /api/word-english-chinese error:', error);
      res.status(500).json({ error: 'Failed to update word' });
    } finally {
      connection.release();
    }
  } else {
    res.status(405).json({ error: 'Method not allowed' });
  }
}
```

**说明**：
- **GET**：查询单词及其翻译，使用 `idx_word` 和 `idx_english_id` 索引优化查询。
- **POST**：
  - 使用事务确保一致性。
  - 更新 `words_english` 的单词和音标。
  - 删除旧翻译，插入新翻译（`include: true`）。
  - 更新 `notebook_words_english` 的 `chinese_id`。
  - 使用 `uuid` 生成唯一 `voice_id_uk` 和 `voice_id_us`（若未提供）。
- **性能**：事务和索引优化适合 3-4 万条数据的操作。

### 使用说明
1. **环境变量**：
   - 在 `.env` 文件中配置 MySQL 连接：
     ```env
     DB_HOST=localhost
     DB_USER=root
     DB_PASSWORD=your_password
     DB_NAME=word_db
     ```

2. **文件结构**：
   - `lib/db.js`：MySQL 配置和表初始化。
   - `pages/api/notebook-words.js`：笔记本单词 API。
   - `pages/api/word-english-chinese.js`：单词和翻译 API。

3. **LexoRank**：
   - 使用 `lexorank` 库生成排序权重，基于最后一个 `weight` 的 `genNext()`。
   - 权重存储在 `notebook_words_english.weight`（VARCHAR），格式如 `0|aaaaaa:`。
   - 支持动态插入和排序，适合大规模数据。

4. **性能优化**：
   - 索引（`idx_word`, `idx_english_id`, `idx_notebook_id`, `idx_weight`）加速查询。
   - 连接池管理高并发请求。
   - 事务确保数据一致性，防止部分写入。

5. **与前端集成**：
   - 与之前的 `WordPlayer.jsx` 兼容。
   - 确保前端请求体格式正确（如 `translations` 包含 `include` 字段）。

6. **音频文件**：
   - 确保 `/public/audio/{voice_id}.mp3` 存在。
   - 实际实现中，需上传音频文件并更新 `voice_id_uk` 和 `voice_id_us`。

### 注意事项
- **数据规模**：3-4 万条数据需要索引和连接池支持，当前实现已优化，但建议监控数据库性能。
- **LexoRank**：
  - 如果需要调整排序（如拖放排序），可调用 `LexoRank.between(prev, next)` 生成中间权重。
  - LexoRank 值较长，`weight` 字段使用 VARCHAR(50) 足够。
- **音频管理**：`voice_id_uk` 和 `voice_id_us` 需对应实际音频文件，建议添加音频上传 API。
- **错误处理**：API 已包含基本错误处理，生产环境中建议添加日志（如 Winston）和用户提示。
- **安全性**：使用参数化查询防止 SQL 注入，建议进一步验证输入数据。

这些 API 适配了 MySQL 和 `lexorank`，能够高效处理 3-4 万条数据，并与前端单词播放器集成。如果需要进一步优化（如批量插入、分页查询）或添加排序调整功能，请提供更多细节！


