Key Principles

- **_Definitly_** use relative path to project folder when refer files or folders in markdown documents! For example, use `./src/app/examples/codemirror/basic-v2-pure`, `./docs/ai/qwen-coder/v1/setup-config.md`, etc.
- 默认服务器已经启动，不需要为用户启动服务器。默认服务器运行在 3000 端口。如果需要重启服务器，提示用户手动介入重启服务器；**_不要_**直接终止服务器进程后主动启动服务器，比如运行 `npm run dev`。
- **_坚持_**复用当前 terminal 环境，不建议每次都打开新的 terminal 窗口。有多轮对话，或者多个命令需要运行，**_耐心_**在同一个 terminal 窗口中**_依次_**完成需要执行的命令。
- 将用户需求确认到工作文件夹内的 docs 子目录的 requirements.md 中。比如用户给出在 src 下做一个文本编辑器的指令，将您的理解生成在 src 的 docs 子目录下的 requirements.md 文档中。
- 对于用户需求错误的理解，用户会手动用 markdown 的删除线标记错误的部分。
- 对于用户后续给出的修正建议、补充需求，理解后同样输出到 requirements.md 文档中。用 ## 补充需求 或 ## 修正建议 + 数字 001 002 等标记，追加在文档末尾。
- 每次生成代码，如果在工作目录的 docs 子目录中存在 requirements.md 文档，先读取文档内容，理解完整需求后，再生成代码。
- Generate or update markdown documents to trace what is planned and what is done for the requirements.md, file should be saved to sub folder 'docs' in working folder (where code is generated or updated) with "changelog-number" (like: changelog-001.md, changelog-002.md, etc) naming custom. For example: if `src/app/examples/codemirror/v1-gemini/page.tsx` need be updated, then `changelog-001.md` should be put in `src/app/examples/codemirror/v1-gemini/docs` folder.
- For further work, refer to `requirements.md` and `changelog-001.md` and `changelog-002.md` documents generated by above steps if existing for fully understand requirements and change history besides final code.
- Write concise, technical TypeScript code with accurate examples.
- Write code for Next.js 15 & App Router which this project bases on.
- Think harder to add enough comments in Simplicated Chinese to explain businese logic, introduce related technical knowledge and technical background so other maintainers needn't search and learn too much documents before their coding work even he/she is not familiar with current tech stack.
- Add links of reference documents, usages or examples as comments if available.
- Use functional and declarative programming patterns; avoid classes.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Structure files: exported component, subcomponents, helpers, static content, types.
- Use relative path to project root when generating markdown document or responsing requests.

Naming Conventions

- Use lowercase with dashes for directories (e.g., components/auth-wizard).
- Favor named exports for components.

TypeScript Usage

- Use TypeScript for all code; prefer interfaces over types.
- Avoid enums; use maps instead.
- Use functional components with TypeScript interfaces.

Syntax and Formatting

- Use the "function" keyword for pure functions.
- Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements.
- Use declarative JSX.

UI and Styling

- Use Shadcn UI, Radix, and Tailwind for components and styling.
- Implement responsive design with Tailwind CSS; use a mobile-first approach.

Performance Optimization

- Minimize 'use client', 'useEffect', and 'setState'; favor React Server Components (RSC).
- Wrap client components in Suspense with fallback.
- Use dynamic loading for non-critical components.
- Optimize images: use WebP format, include size data, implement lazy loading.

Key Conventions

- Use 'nuqs' for URL search parameter state management.
- Optimize Web Vitals (LCP, CLS, FID).
- Limit 'use client':
- Favor server components and Next.js SSR.
- Use only for Web API access in small components.
- Avoid for data fetching or state management.

Follow Next.js docs for Data Fetching, Rendering, and Routing.

Next.js 15 & App Router Best Practices

- Use App Router (app directory) for all new features and pages.
- Prefer Server Components by default; only use Client Components when necessary.
- Use TypeScript (.tsx/.ts) file extensions consistently throughout the project.
- Implement proper loading.tsx, error.tsx, and not-found.tsx files for better UX.
- Use parallel routes and intercepting routes for advanced routing patterns.
- Leverage route groups (folders with parentheses) for organization without affecting URL structure.
- Use generateMetadata for dynamic SEO optimization.
- Implement proper data fetching with async/await in Server Components.
- Use Suspense boundaries strategically for progressive loading.
- Prefer fetch() with proper caching strategies over external data fetching libraries.
- Use Server Actions for form submissions and mutations.
- Implement proper error boundaries and error handling.
- Use middleware.ts for authentication, redirects, and request/response manipulation.
- Optimize bundle size with dynamic imports and code splitting.
- Use Next.js Image component with proper sizing and optimization.
- Implement proper TypeScript strict mode configuration.
- Use App Router's built-in internationalization (i18n) features when needed.
- Leverage streaming and partial prerendering for better performance.
- Use proper caching strategies: force-cache, no-store, revalidate.
- Implement proper security headers and CSP policies.
