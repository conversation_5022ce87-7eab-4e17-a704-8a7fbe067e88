:root {
  /* shadcn UI colors for green-on-black theme */
  --primary: rgb(120, 210, 120);
  --primary-foreground: #000000;
  --muted: rgb(40, 40, 40);
  --muted-foreground: rgb(160, 160, 160);
  --input: rgb(60, 60, 60);
  --ring: rgb(120, 210, 120);
  --border: rgb(80, 80, 80);
}

div.nav {
    position: absolute;
}

span.phonetic {
    opacity: 0.65;
}

span.pos {
    opacity: 0.9;
}

.word-container {
    display: grid;
    place-items: center;
    font-size: 34px;
    margin: auto;
    width: 1280px;
    text-align: center;
    font-family: serif;
    height: 85vh;
}

.word-container .word {
    font-size: 170px;
    letter-spacing: 10px;
    height: 400px;
    line-height: 170px;
}

.word-container .translation {
    margin: 95px 0 0 0;
    font-size: 28px;
    opacity: 0.9;
}

.word-container .note {
    font-size: 18px;
    opacity: 1.0;
}

svg {
    display: inline;
    vertical-align: top;
}

div.operation {
    position: relative;
}

.search-form input {
    opacity: .5;
}

div.operation .search-form input:focus, 
div.operation .search-form input:active, 
div.operation:hover .search-form input {
    opacity: 1;
}

div.operation svg {
    opacity: 0.5;
    /* 添加过渡动画 */
    transition: opacity 0.3s ease, color 0.3s ease;
    cursor: pointer;
    font-size: 22px;
}


div.operation svg:hover {
    opacity: 0.7;

}

div.operation.button {
    position: relative;
    height: 30px;

}

div.operation.button .put_top,
div.operation.button .put_end {
    opacity: 0.6;

    /* 添加过渡动画 */
    transition: opacity 0.6s ease, color 0.3s ease;
}

div.operation.button .put_top:hover,
div.operation.button .put_end:hover {
    opacity: 1;
    color: lightgreen;
}

div.operation span.put_top {
    position: absolute;
    left: 50px;
    top: 0;
}

div.operation span.put_end {
    position: absolute;
    right: 50px;
    top: 0;
}

span.put_top svg {
    transform: rotate(-90deg);
    cursor: pointer;
    font-size: 26px;
}

span.put_end svg {
    transform: rotate(90deg);
    cursor: pointer;
    font-size: 26px;
}

.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5); /* 半透明背景 */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000; /* 确保遮罩在最上层 */
}

.loader {
    color: white;
    font-size: 24px;
    font-weight: bold;
    /*animation: pulse 1.5s infinite; !* 可选：加载动画 *!*/
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}
