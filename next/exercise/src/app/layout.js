import {JotaiProvider} from '@/app/lib/components/JotaiProvider';
import { NextThemesProvider } from '@/app/lib/theme-provider';
import AuthSessionProvider from '@/components/providers/session-provider';
import { ProcessingMask } from '@/app/lib/components/ProcessingMask';
import { ToastContainer } from 'react-toastify';

import './globals.css'
import 'react-toastify/dist/ReactToastify.css';

export const metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
}

export default function RootLayout ({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
    <head>
      <link rel="stylesheet" href="/fonts/fonts.css" />
    </head>
    <body
      className="antialiased font-sans"
      suppressHydrationWarning
    >
    <NextThemesProvider attribute="class"
                        defaultTheme="system"
                        enableSystem
                        disableTransitionOnChange
    >
      <AuthSessionProvider>
        <JotaiProvider>
          {children}
          <ProcessingMask />
        </JotaiProvider>
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="auto"
        />
      </AuthSessionProvider>
    </NextThemesProvider>
    </body>
    </html>
  )
}
