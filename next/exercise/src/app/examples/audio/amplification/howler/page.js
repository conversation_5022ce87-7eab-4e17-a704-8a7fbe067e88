'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Play, Pause, Square, Volume2, Info } from 'lucide-react';
import { Howl, Howler } from 'howler';

export default function HowlerAmplificationExample() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState([0.5]); // Howler基础音量
  const [gain, setGain] = useState([2]); // 放大倍数
  const [isConnected, setIsConnected] = useState(false);
  
  const howlRef = useRef(null);
  const gainNodeRef = useRef(null);
  const originalConnectionRef = useRef(null);
  const progressIntervalRef = useRef(null);

  const audioUrl = 'http://localhost:3000/refs/listening_dialog/03/20232Yangpu08.wav';

  // 初始化Howl实例
  const initHowl = () => {
    if (howlRef.current) {
      howlRef.current.unload();
    }

    howlRef.current = new Howl({
      src: [audioUrl],
      volume: volume[0],
      html5: false, // 强制使用Web Audio API
      onload: () => {
        setIsLoading(false);
        setDuration(howlRef.current.duration());
        console.log('Howl loaded, Web Audio:', howlRef.current._webAudio);
        
        // 检查是否使用Web Audio
        if (howlRef.current._webAudio) {
          setupGainConnection();
        } else {
          console.warn('Howl is not using Web Audio API, amplification may not work');
        }
      },
      onloaderror: (id, error) => {
        setIsLoading(false);
        console.error('Load error:', error);
        alert('加载音频失败，请检查音频文件是否存在');
      },
      onplay: () => {
        setIsPlaying(true);
        startProgressTracking();
      },
      onpause: () => {
        setIsPlaying(false);
        stopProgressTracking();
      },
      onstop: () => {
        setIsPlaying(false);
        setCurrentTime(0);
        stopProgressTracking();
      },
      onend: () => {
        setIsPlaying(false);
        setCurrentTime(0);
        stopProgressTracking();
      }
    });

    setIsLoading(true);
  };

  // 设置增益节点连接
  const setupGainConnection = () => {
    if (!howlRef.current || !howlRef.current._webAudio) {
      console.warn('Cannot setup gain connection: Howl is not using Web Audio');
      return;
    }

    try {
      // 创建自定义增益节点
      if (!gainNodeRef.current) {
        gainNodeRef.current = Howler.ctx.createGain();
        gainNodeRef.current.gain.value = gain[0];
      }

      // 获取第一个sound的节点
      const sounds = howlRef.current._sounds;
      if (sounds && sounds.length > 0) {
        const sound = sounds[0];
        if (sound._node) {
          // 断开原有连接并重新连接到我们的增益节点
          sound._node.disconnect();
          sound._node.connect(gainNodeRef.current);
          gainNodeRef.current.connect(Howler.masterGain);
          
          setIsConnected(true);
          console.log('Successfully connected Howl to custom gain node');
        }
      }
    } catch (error) {
      console.error('Error setting up gain connection:', error);
    }
  };

  // 重置连接到原始状态
  const resetConnection = () => {
    if (!howlRef.current || !howlRef.current._webAudio) return;

    try {
      const sounds = howlRef.current._sounds;
      if (sounds && sounds.length > 0) {
        const sound = sounds[0];
        if (sound._node && gainNodeRef.current) {
          // 断开自定义增益节点
          sound._node.disconnect();
          gainNodeRef.current.disconnect();
          
          // 重新连接到原始的masterGain
          sound._node.connect(Howler.masterGain);
          
          setIsConnected(false);
          console.log('Reset connection to original state');
        }
      }
    } catch (error) {
      console.error('Error resetting connection:', error);
    }
  };

  // 播放控制
  const handlePlay = () => {
    if (!howlRef.current) {
      initHowl();
      return;
    }

    if (isPlaying) {
      howlRef.current.pause();
    } else {
      howlRef.current.play();
    }
  };

  const handleStop = () => {
    if (howlRef.current) {
      howlRef.current.stop();
    }
  };

  // 进度跟踪
  const startProgressTracking = () => {
    progressIntervalRef.current = setInterval(() => {
      if (howlRef.current && isPlaying) {
        setCurrentTime(howlRef.current.seek() || 0);
      }
    }, 100);
  };

  const stopProgressTracking = () => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  };

  // 更新音量
  useEffect(() => {
    if (howlRef.current) {
      howlRef.current.volume(volume[0]);
    }
  }, [volume]);

  // 更新增益
  useEffect(() => {
    if (gainNodeRef.current && isConnected) {
      gainNodeRef.current.gain.value = gain[0];
    }
  }, [gain, isConnected]);

  // 组件初始化
  useEffect(() => {
    initHowl();
    
    return () => {
      if (howlRef.current) {
        howlRef.current.unload();
      }
      stopProgressTracking();
    };
  }, []);

  // 格式化时间
  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Volume2 className="h-6 w-6" />
            Howler.js 音频放大示例
          </CardTitle>
          <CardDescription>
            使用 Howler.js 连接到自定义 GainNode 实现音频放大
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 音频信息 */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-600 mb-2">音频文件:</p>
            <p className="text-xs font-mono break-all">{audioUrl}</p>
          </div>

          {/* 连接状态 */}
          <div className={`p-4 rounded-lg border-2 ${isConnected ? 'bg-green-50 border-green-200' : 'bg-orange-50 border-orange-200'}`}>
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4" />
              <span className="font-medium">
                连接状态: {isConnected ? '已连接到自定义增益节点' : '使用默认连接'}
              </span>
            </div>
            <p className="text-xs mt-1 text-gray-600">
              {isConnected 
                ? 'Howl → 自定义GainNode → MasterGain → 输出'
                : 'Howl → MasterGain → 输出'
              }
            </p>
          </div>

          {/* 连接控制 */}
          <div className="flex gap-2">
            <Button
              onClick={setupGainConnection}
              disabled={!howlRef.current || isConnected || isLoading}
              variant="outline"
              size="sm"
            >
              连接增益节点
            </Button>
            <Button
              onClick={resetConnection}
              disabled={!howlRef.current || !isConnected || isLoading}
              variant="outline"
              size="sm"
            >
              重置连接
            </Button>
          </div>

          {/* 播放控制 */}
          <div className="flex items-center gap-4">
            <Button
              onClick={handlePlay}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : isPlaying ? (
                <Pause className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              {isLoading ? '加载中...' : isPlaying ? '暂停' : '播放'}
            </Button>
            
            <Button
              onClick={handleStop}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Square className="h-4 w-4" />
              停止
            </Button>
            
            <div className="text-sm text-gray-600">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>

          {/* 进度条 */}
          <div className="space-y-2">
            <Label>播放进度</Label>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-100"
                style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
              />
            </div>
          </div>

          {/* Howler音量控制 */}
          <div className="space-y-2">
            <Label>Howler 音量: {volume[0].toFixed(2)}</Label>
            <Slider
              value={volume}
              onValueChange={setVolume}
              max={1}
              min={0}
              step={0.01}
              className="w-full"
            />
          </div>

          {/* 增益控制 */}
          <div className="space-y-2">
            <Label>
              增益放大: {gain[0].toFixed(1)}x 
              {!isConnected && <span className="text-orange-600 text-sm">(未连接)</span>}
            </Label>
            <Slider
              value={gain}
              onValueChange={setGain}
              max={5}
              min={0.1}
              step={0.1}
              className="w-full"
              disabled={!isConnected}
            />
          </div>

          {/* 最终音量显示 */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-sm font-medium text-blue-800">
              理论最终音量: {isConnected ? (volume[0] * gain[0]).toFixed(2) : volume[0].toFixed(2)}
              {isConnected && gain[0] > 1 && (
                <span className="text-orange-600 ml-2">
                  (放大 {((gain[0] - 1) * 100).toFixed(0)}%)
                </span>
              )}
            </p>
            <p className="text-xs text-blue-600 mt-1">
              {isConnected 
                ? '最终音量 = Howler音量 × 增益放大'
                : '仅使用 Howler 内置音量控制'
              }
            </p>
          </div>

          {/* 技术说明 */}
          <div className="bg-gray-50 p-4 rounded-lg text-sm text-gray-600">
            <h4 className="font-medium mb-2">技术实现:</h4>
            <ul className="space-y-1 text-xs">
              <li>• 使用 Howler.js 的 Web Audio API 模式</li>
              <li>• 访问 Howl 内部的 _node (GainNode)</li>
              <li>• 断开原有连接，插入自定义 GainNode</li>
              <li>• 支持动态连接/断开增益节点</li>
              <li>• 实现超过 Howler 限制的音量放大</li>
            </ul>
            
            <h4 className="font-medium mb-2 mt-4">注意事项:</h4>
            <ul className="space-y-1 text-xs">
              <li>• 需要音频文件支持 Web Audio API</li>
              <li>• 某些浏览器可能需要用户交互才能播放</li>
              <li>• 过度放大可能导致音频失真</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}