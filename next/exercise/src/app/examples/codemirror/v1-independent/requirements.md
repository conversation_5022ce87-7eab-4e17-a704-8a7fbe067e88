生成一个独立的 example，使用 codemirror 6 支持 html 编辑。在同一个页面支持实时预览。需要完全利用页面宽度。支持通过工具栏将常用 html tag 快捷插入。编辑区高度适配 html code 长短。

## 追加功能：001
- 工具条需要能够在滚动超出可见范围的时候置顶
- 滚动时，能将预览区内容同步显示在对应代码高度，方便观察修改代码后的实时效果

### 以上需求常见问题：
-工具栏背景色现在是透明的，工具栏内容与页面内容重叠，不方便使用。
-工具栏置顶后，通过滚动，应该重现工具栏时，工具栏依旧置顶。
- 点击工具栏，内容插入在html最后位置。需要改成在光标位置。
- 预览内容，并没有随滚动对其代码位置。代码里有很多非可见元素，比如html，head。页面滚动时，预览内容其实应该飘动到 body tag 开始的代码，随着滚动对齐 html 代码及预览内容





