# WaveSurfer 音频演示页面

## 功能概述

这是一个基于 WaveSurfer.js 的音频播放演示页面，展示了多个音频文件的加载、切换、选中部分和循环播放功能。

## 主要功能

### 1. 多音频文件支持
- 支持加载和切换 5 个不同的音频文件
- 每个音频文件都有独立的波形显示
- 可以随时切换到不同的音频文件

### 2. 波形可视化
- 实时显示音频波形
- 支持点击波形跳转到指定位置
- 显示当前播放进度和总时长

### 3. 区域选择功能
- 通过拖拽波形可以选择音频片段
- 支持播放选中的音频区域
- 可以删除选中的区域
- 显示选中区域的时间范围

### 4. 播放控制
- **播放/暂停**: 控制音频的播放和暂停
- **停止**: 停止播放并回到开始位置
- **循环播放**: 支持整个音频或选中区域的循环播放

### 5. 循环模式
- **无循环**: 播放完毕后停止
- **区域循环**: 如果有选中区域，循环播放该区域
- **全文循环**: 如果没有选中区域，循环播放整个音频

## 使用方法

### 基本操作
1. **选择音频**: 点击页面上方的音频文件按钮切换不同音频
2. **播放控制**: 使用播放/暂停、停止按钮控制播放
3. **跳转位置**: 直接点击波形图跳转到指定位置

### 区域选择
1. **创建区域**: 在波形图上按住鼠标左键并拖拽来选择区域
2. **播放区域**: 点击"播放选中区域"按钮播放选中的音频片段
3. **删除区域**: 点击"删除选中区域"按钮移除选中的区域

### 循环播放
1. **开启循环**: 点击"循环关闭"按钮开启循环模式
2. **区域循环**: 选中区域后开启循环，将循环播放选中区域
3. **全文循环**: 没有选中区域时开启循环，将循环播放整个音频

## 技术实现

### 核心技术栈
- **WaveSurfer.js**: 音频波形可视化和播放控制
- **RegionsPlugin**: 音频区域选择功能
- **React**: 用户界面框架
- **TypeScript**: 类型安全的开发
- **Tailwind CSS**: 样式设计

### 主要组件
- **WaveSurfer 实例**: 负责音频加载、播放和波形显示
- **RegionsPlugin**: 处理音频区域的创建、编辑和删除
- **状态管理**: 管理当前音频、播放状态、循环模式等

## 音频文件

演示页面使用了以下 5 个音频文件：
1. `0ac5f21d-d87d-4c47-a11d-e3b1f649e9d8.wav`
2. `06e91889-a572-4913-bff0-e49cec988ecf.wav`
3. `070c4aa3-320a-403a-8464-d429f2fa456a.wav`
4. `0380e9ab-8ca8-4fe0-978f-49315efe4219.wav`
5. `0829d25d-010f-41c6-8e4d-1471f2855d27.wav`

所有音频文件位于 `/public/refs/notes/chinese-compositions/zh-CN-XiaochenMultilingualNeural/0/` 目录下。

## 访问地址

开发环境: `http://localhost:3000/examples/wavesurfer/v1`

## 注意事项

1. 确保音频文件存在于指定路径
2. 浏览器需要支持 Web Audio API
3. 某些浏览器可能需要用户交互后才能播放音频
4. 建议使用现代浏览器以获得最佳体验