# Wavesurfer V2 音乐播放器需求文档

## 项目概述
基于 Wavesurfer 最佳实践开发一个功能完整的音乐播放器，支持多音频文件管理、循环播放、波形可视化等核心功能。

## 核心需求

### 1. 音频文件管理
- **音频源**: 复用项目中已有的音频文件
  - `/public/audio/sample1.mp3`
  - `/public/audio/sample2.mp3` 
  - `/public/audio/sample3.mp3`
  - 其他可用音频文件
- **播放列表**: 支持多个音频文件的播放列表管理
- **文件切换**: 支持上一首/下一首切换
- **文件信息**: 显示当前播放的音频文件名称和时长

### 2. 播放控制功能
- **基础控制**: 播放/暂停、停止、上一首、下一首
- **进度控制**: 支持拖拽进度条跳转到指定位置
- **音量控制**: 音量调节滑块
- **播放速度**: 支持播放速度调节（0.5x - 2.0x）

### 3. 循环播放模式
- **单曲循环**: 当前音频文件循环播放
- **列表循环**: 播放列表循环播放
- **随机播放**: 随机选择播放列表中的音频
- **顺序播放**: 按顺序播放，播放完最后一首后停止

### 4. 波形可视化
- **波形显示**: 使用 Wavesurfer 显示音频波形
- **进度指示**: 实时显示播放进度
- **交互操作**: 点击波形跳转到对应位置
- **视觉效果**: 美观的波形颜色和样式

### 5. 用户界面设计
- **现代化设计**: 使用 Shadcn UI 组件库
- **响应式布局**: 适配不同屏幕尺寸
- **主题支持**: 支持明暗主题切换
- **直观操作**: 清晰的按钮和控制元素

## 技术实现要求

### 1. 核心技术栈
- **Next.js 15**: App Router 架构
- **TypeScript**: 类型安全开发
- **Wavesurfer.js**: 音频波形可视化和播放控制
- **React Hooks**: 状态管理和副作用处理

### 2. UI 组件库
- **Shadcn UI**: 按钮、滑块、选择器等组件
- **Tailwind CSS**: 样式系统
- **Lucide React**: 图标库
- **React Hot Toast**: 消息提示

### 3. 音频处理
- **Wavesurfer.js**: 主要的音频处理库
- **Web Audio API**: 底层音频控制
- **音频格式**: 支持 MP3、WAV 等常见格式

### 4. 状态管理
- **播放状态**: 播放/暂停/停止状态管理
- **播放列表**: 当前播放列表和索引
- **播放模式**: 循环模式状态
- **音频信息**: 当前音频的元数据

## 功能特性设计

### 1. 播放器核心功能

#### 播放控制
- **播放/暂停按钮**: 切换播放状态
- **停止按钮**: 停止播放并重置进度
- **上一首/下一首**: 切换播放列表中的音频
- **进度条**: 显示和控制播放进度
- **时间显示**: 当前时间/总时长

#### 音量和速度控制
- **音量滑块**: 0-100% 音量调节
- **静音按钮**: 快速静音/取消静音
- **速度选择**: 0.5x, 0.75x, 1.0x, 1.25x, 1.5x, 2.0x

### 2. 播放模式

#### 循环模式切换
- **顺序播放**: 按列表顺序播放
- **单曲循环**: 重复播放当前音频
- **列表循环**: 播放完列表后重新开始
- **随机播放**: 随机选择下一首

#### 模式指示
- **图标显示**: 不同模式对应不同图标
- **状态提示**: 切换模式时显示提示信息

### 3. 播放列表管理

#### 列表显示
- **音频列表**: 显示所有可播放的音频文件
- **当前播放**: 高亮显示正在播放的音频
- **文件信息**: 显示文件名和时长

#### 列表操作
- **点击播放**: 点击列表项直接播放
- **拖拽排序**: 支持拖拽调整播放顺序
- **删除功能**: 从播放列表中移除音频

### 4. 波形可视化

#### 波形显示
- **实时波形**: 加载音频时生成波形图
- **进度指示**: 播放进度的可视化显示
- **交互操作**: 点击波形跳转播放位置

#### 视觉效果
- **颜色主题**: 适配明暗主题的波形颜色
- **动画效果**: 平滑的进度动画
- **响应式**: 适配不同屏幕尺寸

## 用户体验设计

### 1. 界面布局

#### 主要区域
- **顶部**: 标题和主题切换
- **中央**: 波形显示区域
- **底部**: 播放控制面板
- **侧边**: 播放列表（可折叠）

#### 控制面板
- **播放控制**: 上一首、播放/暂停、停止、下一首
- **进度信息**: 当前时间、进度条、总时长
- **音量控制**: 静音按钮、音量滑块
- **播放模式**: 循环模式切换按钮
- **速度控制**: 播放速度选择器

### 2. 交互体验

#### 键盘快捷键
- **空格键**: 播放/暂停
- **左右箭头**: 快进/快退
- **上下箭头**: 音量调节
- **Enter**: 播放选中的音频

#### 鼠标操作
- **点击波形**: 跳转到对应位置
- **拖拽进度条**: 调整播放进度
- **滚轮**: 音量调节（在音量区域）

### 3. 状态反馈

#### 视觉反馈
- **播放状态**: 播放按钮图标变化
- **加载状态**: 音频加载时的进度指示
- **错误状态**: 音频加载失败的提示

#### 消息提示
- **模式切换**: 显示当前播放模式
- **音频切换**: 显示当前播放的音频名称
- **操作反馈**: 音量调节、速度变化等提示

## 技术实现细节

### 1. Wavesurfer 配置

#### 基础配置
```typescript
const wavesurferOptions = {
  container: containerRef.current,
  waveColor: '#4F46E5',
  progressColor: '#7C3AED',
  cursorColor: '#EF4444',
  barWidth: 2,
  barRadius: 3,
  responsive: true,
  height: 80,
  normalize: true,
  backend: 'WebAudio',
  mediaControls: false
};
```

#### 插件使用
- **Regions Plugin**: 如需要区域选择功能
- **Timeline Plugin**: 时间轴显示
- **Minimap Plugin**: 缩略图导航

### 2. 状态管理结构

#### 播放器状态
```typescript
interface PlayerState {
  isPlaying: boolean;
  isPaused: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  playbackRate: number;
  isMuted: boolean;
}
```

#### 播放列表状态
```typescript
interface PlaylistState {
  tracks: AudioTrack[];
  currentIndex: number;
  playMode: 'sequential' | 'loop' | 'single' | 'random';
}
```

### 3. 音频文件结构
```typescript
interface AudioTrack {
  id: string;
  title: string;
  url: string;
  duration?: number;
  artist?: string;
  album?: string;
}
```

## 性能优化

### 1. 音频加载优化
- **预加载**: 预加载下一首音频
- **缓存策略**: 合理的音频文件缓存
- **懒加载**: 按需加载波形数据

### 2. 渲染优化
- **虚拟化**: 大播放列表的虚拟滚动
- **防抖**: 进度更新的防抖处理
- **内存管理**: 及时清理不用的 Wavesurfer 实例

### 3. 用户体验优化
- **加载状态**: 音频加载时的友好提示
- **错误处理**: 音频加载失败的降级处理
- **离线支持**: 基础的离线播放功能

## 测试要点

### 1. 功能测试
- [ ] 音频播放/暂停/停止功能
- [ ] 上一首/下一首切换功能
- [ ] 循环模式切换功能
- [ ] 音量和速度控制功能
- [ ] 进度条拖拽功能
- [ ] 播放列表管理功能

### 2. 兼容性测试
- [ ] 不同浏览器的音频播放兼容性
- [ ] 移动端触摸操作
- [ ] 不同音频格式的支持

### 3. 性能测试
- [ ] 大文件音频的加载性能
- [ ] 长时间播放的内存使用
- [ ] 快速切换音频的响应性能

## 后续扩展方向

### 1. 高级功能
- **均衡器**: 音频均衡器控制
- **可视化效果**: 更丰富的音频可视化
- **歌词显示**: 同步歌词功能
- **播放历史**: 播放历史记录

### 2. 社交功能
- **播放列表分享**: 分享播放列表
- **收藏功能**: 收藏喜欢的音频
- **评论系统**: 音频评论功能

### 3. 技术升级
- **PWA 支持**: 渐进式 Web 应用
- **离线播放**: 完整的离线播放支持
- **云同步**: 播放列表云同步

## 文件结构规划

```
src/app/examples/wavesurfer/v2/
├── docs/
│   ├── requirements.md (本文档)
│   └── changelog-001.md (开发日志)
├── components/
│   ├── AudioPlayer.tsx (主播放器组件)
│   ├── WaveformDisplay.tsx (波形显示组件)
│   ├── PlayControls.tsx (播放控制组件)
│   ├── VolumeControl.tsx (音量控制组件)
│   ├── PlaylistPanel.tsx (播放列表组件)
│   └── PlayModeSelector.tsx (播放模式选择组件)
├── hooks/
│   ├── useWavesurfer.ts (Wavesurfer hook)
│   ├── useAudioPlayer.ts (播放器状态 hook)
│   └── usePlaylist.ts (播放列表 hook)
├── types/
│   └── audio.ts (音频相关类型定义)
├── utils/
│   ├── audioUtils.ts (音频工具函数)
│   └── formatTime.ts (时间格式化工具)
└── page.tsx (主页面组件)
```

## 开发优先级

### 第一阶段 (核心功能)
1. 基础播放器组件和 Wavesurfer 集成
2. 播放/暂停/停止控制
3. 音频文件加载和波形显示
4. 基础的播放列表功能

### 第二阶段 (增强功能)
1. 循环模式实现
2. 音量和速度控制
3. 进度条交互
4. 键盘快捷键支持

### 第三阶段 (优化完善)
1. UI/UX 优化
2. 响应式设计
3. 性能优化
4. 错误处理完善

## 总结

本项目旨在创建一个功能完整、用户体验优秀的音乐播放器，基于 Wavesurfer.js 的强大音频处理能力，结合现代 Web 技术栈，为用户提供专业级的音频播放体验。通过模块化的设计和渐进式的开发方式，确保项目的可维护性和可扩展性。