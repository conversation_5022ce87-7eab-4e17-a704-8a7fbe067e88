# Sentence Splitter 功能需求文档

## 项目背景

原有的 `preprocessText` 函数在处理英文文章时表现良好，但无法正确处理中文文章的句子分割。需要开发一个新的函数来支持中英文文本的智能句子分割。

## 功能需求

### 核心需求

1. **中文句子分割支持**
   - 能够正确识别中文句子的结束标点：。！？
   - 处理中文文本中的引号、括号等标点符号
   - 保持句子的完整性和语义连贯性

2. **英文句子分割支持**
   - 继续支持英文句子的分割：. ! ?
   - 处理缩写词和特殊情况
   - 保持与原有功能的兼容性

3. **混合语言支持**
   - 支持中英文混合文本的处理
   - 正确识别不同语言的句子边界

### 输入输出格式

**输入示例：**
```
那为何一波三折能成为我们认识世界的一剂良药呢？我认为，不"出"就无法知大方，无法扩大境界；不再次"入"便无法将方法化为己用。为自己的认识体系添砖加瓦，应当是个人认知探索的最终目的，所以才有此兜兜转转。无怪乎如今的高等教育，多提倡"通识教育"，在专注进行某一行业的学习之前，要先从"百家之长"中充分汲取营养。这样的认知才是扎根于广阔的大地之上的，这样的人才也是会自我成长的。
```

**期望输出：**
```html
<p>
  <span aria-label="那为何一波三折能成为我们认识世界的一剂良药呢？" speaker="" data-voice-id="">那为何一波三折能成为我们认识世界的一剂良药呢？</span>
  <span aria-label="我认为，不"出"就无法知大方，无法扩大境界；不再次"入"便无法将方法化为己用。" speaker="" data-voice-id="">我认为，不"出"就无法知大方，无法扩大境界；不再次"入"便无法将方法化为己用。</span>
  <span aria-label="为自己的认识体系添砖加瓦，应当是个人认知探索的最终目的，所以才有此兜兜转转。" speaker="" data-voice-id="">为自己的认识体系添砖加瓦，应当是个人认知探索的最终目的，所以才有此兜兜转转。</span>
  <span aria-label="无怪乎如今的高等教育，多提倡"通识教育"，在专注进行某一行业的学习之前，要先从"百家之长"中充分汲取营养。" speaker="" data-voice-id="">无怪乎如今的高等教育，多提倡"通识教育"，在专注进行某一行业的学习之前，要先从"百家之长"中充分汲取营养。</span>
  <span aria-label="这样的认知才是扎根于广阔的大地之上的，这样的人才也是会自我成长的。" speaker="" data-voice-id="">这样的认知才是扎根于广阔的大地之上的，这样的人才也是会自我成长的。</span>
</p>
```

### 技术要求

1. **使用 sentence-splitter 库**
   - 项目已集成 `sentence-splitter` 库
   - 利用该库的多语言句子分割能力
   - 提供备用分割方案以确保稳定性

2. **函数位置**
   - 新函数放置在 `src/app/lib/utils.ts` 文件中
   - 函数名：`preprocessTextWithSentenceSplitter`
   - 不修改或删除原有的 `preprocessText` 函数

3. **兼容性要求**
   - 保持与现有 HTML 结构的兼容性
   - 支持已处理文本的重新处理
   - 错误处理和降级方案

### 辅助功能

1. **前缀处理**
   - 去除常见的对话前缀：M:, W:, Q: 等
   - 保持 aria-label 内容的清洁

2. **语言检测**
   - 提供 `isChineseText` 函数检测文本语言
   - 基于中文字符占比进行判断

3. **测试页面**
   - 创建测试页面验证功能
   - 提供多种测试用例
   - 实时预览处理结果

## 补充需求 001

### 混合处理支持

**问题描述：**
如果输入的文字是多段，有一段处理了，其余几段是新添加的，现在会全部原样返回。我们期望没有处理的部分完成同样处理，已处理部分则原样返回。

**具体需求：**
1. **智能段落识别**
   - 能够识别哪些段落已经被处理过（包含完整的 `<p>` 和 `<span>` 标签结构）
   - 能够识别哪些段落是新添加的未处理文本

2. **混合处理逻辑**
   - 已处理的段落：保持原样，但检查是否需要优化（如拆分多句子的span）
   - 未处理的段落：进行完整的句子分割和HTML包装处理
   - 支持多种段落分割方式：HTML `<p>` 标签、双换行符、单换行符

3. **测试用例**
   - 混合处理测试（部分已处理）
   - 需要拆分的已处理段落测试
   - 验证已处理段落保持不变
   - 验证未处理段落正确处理

**实现方案：**
- 重构 `preprocessTextWithSentenceSplitter` 函数
- 添加 `splitTextIntoParagraphs` 函数支持多种段落分割
- 添加 `isParagraphProcessed` 函数检测段落处理状态
- 添加 `refineParagraphSpans` 函数优化已处理段落
- 添加 `processUnprocessedParagraph` 函数处理未处理段落

## 实现方案

### 核心算法

1. **句子分割流程**
   ```
   输入文本 → 段落分割 → 段落状态检测 → 分别处理 → 输出结果
   ```

2. **错误处理**
   ```
   sentence-splitter 失败 → 备用分割方法 → 基于标点符号分割
   ```

3. **HTML 结构生成**
   ```
   段落分割 → 句子分割 → span 标签包装 → p 标签包装
   ```

### 文件结构

```
src/app/lib/utils.ts                    # 核心函数实现
src/app/test-sentence-splitter/         # 测试页面
├── page.tsx                            # 测试页面组件
└── docs/
    ├── requirements.md                 # 需求文档（本文件）
    └── changelog-002.md               # 变更日志
```

## 验收标准

1. **功能验收**
   - [x] 正确分割中文句子
   - [x] 正确分割英文句子
   - [x] 支持中英文混合文本
   - [x] 生成正确的 HTML 结构
   - [x] 处理已有 HTML 标签的文本
   - [x] 混合处理：已处理段落保持原样
   - [x] 混合处理：未处理段落正确处理
   - [x] 优化已处理段落中的多句子span

2. **性能验收**
   - [x] 处理大段文本时性能良好
   - [x] 错误处理机制有效
   - [x] 内存使用合理

3. **兼容性验收**
   - [x] 与现有系统集成无问题
   - [x] 不影响原有功能
   - [x] 支持 TypeScript 类型检查

## 修正建议 002

### 属性名称和格式调整

**问题描述：**
用户提出两个小问题需要修正：
1. speaker 属性改为 data-speaker
2. 英文句子最后保留一个半角空格，除非那一句英文是某段的最后一句

**具体修正：**
1. **属性名称修正**
   - 将所有 `speaker=""` 改为 `data-speaker=""`
   - 保持与其他 data-* 属性的命名一致性

2. **英文句子空格处理**
   - 检测句子是否为英文句子（基于字符占比判断）
   - 为非最后一句的英文句子添加半角空格
   - 段落最后一句不添加空格

**实现方案：**
- 添加 `isEnglishSentence` 函数检测英文句子
- 修改 `refineParagraphSpans` 和 `processUnprocessedParagraph` 函数
- 更新测试用例以验证新功能
- 添加英文句子空格测试用例

## 修正建议 003

### 段落分割逻辑修正

**问题描述：**
用户反馈段落处理存在问题：函数没有正确识别自然段，导致多个自然段被合并到一个p标签中，而已处理的段落反而丢失了p标签。

**具体问题：**
1. **段落合并问题：** 第一、二段被错误地合并到一个 `<p>` 标签中
2. **段落丢失问题：** 第三段（已处理的段落）的 `<p>` 标签被意外移除
3. **自然段识别失败：** 无法正确识别由空行分隔的自然段落

**期望行为：**
- 第一段：单独处理为一个 `<p>` 标签
- 第二段：单独处理为一个 `<p>` 标签  
- 第三段：保持原有的 `<p>` 标签结构不变

**实现方案：**
1. **重构 `splitTextIntoParagraphs` 函数**
   - 实现智能段落分割，支持混合内容（已处理和未处理的段落）
   - 按行分析文本，正确识别空行作为段落分隔符
   - 检测HTML段落标签的开始和结束，保持其完整性
   - 区分HTML标签内容和普通文本内容的处理方式

2. **段落状态跟踪**
   - 使用状态变量跟踪当前是否在HTML标签内部
   - 使用深度计数器处理嵌套的HTML标签
   - 正确处理HTML标签的开始、内容和结束

3. **文本连接逻辑**
   - HTML标签内容：使用换行符连接多行
   - 普通文本内容：使用空格连接多行（形成自然段落）
   - 空行处理：作为段落分隔符，重置状态

4. **测试用例更新**
   - 添加用户反馈的具体测试用例
   - 验证三段文本的正确处理
   - 确保已处理段落保持原样
   - 确保未处理段落正确分割

**技术细节：**
```typescript
// 新的段落分割算法
- 按行分割文本
- 检测空行作为段落边界
- 识别HTML标签的开始和结束
- 维护HTML标签深度计数
- 根据上下文选择合适的文本连接方式
```

## 修正建议 004

### 智能段落边界识别

**问题描述：**
用户反馈了两个新的段落分割问题：
1. **五段文本问题：** 一共5段文本，中间已处理的段落可以原样返回，但前面两段和后面两段没有被正确处理
2. **中英文混合问题：** 中英文混在一起的三段文本被错误地处理成一个 `<p>` 标签

**具体问题：**
1. **段落识别不完整：** 无法识别所有的自然段落边界
2. **语言变化未识别：** 中英文语言变化时未能正确分段
3. **单换行符处理：** 只依赖空行分段，忽略了单换行符的段落边界

**期望行为：**
- 五段文本：每段独立处理为单独的 `<p>` 标签
- 中英文混合：根据语言变化自动分段
- 智能识别：结合标点符号、语言变化、特殊标记等多种因素判断段落边界

**实现方案：**
1. **增强段落分割逻辑**
   - 添加 `isNewParagraphStart` 函数智能判断段落边界
   - 检测语言变化（中文到英文或英文到中文）
   - 检测句号结尾和特殊标记开始的组合
   - 支持单换行符的段落分割

2. **语言变化检测**
   - 使用现有的 `isChineseText` 函数检测文本语言
   - 比较当前行和已有段落的语言类型
   - 语言变化时自动开始新段落

3. **多因素判断**
   - 句号结尾检测：`/[。.!?！？]\s*$/`
   - 特殊标记检测：`/^[——\-\*\+]/`
   - 语言类型变化检测
   - 综合判断是否开始新段落

4. **测试用例扩展**
   - 添加五段文本测试用例
   - 添加中英文混合三段测试用例
   - 验证语言变化时的自动分段
   - 验证特殊标记的段落识别

**技术细节：**
```typescript
// 新的段落边界判断逻辑
if (!insideHtmlTag && currentParagraph.trim() && isNewParagraphStart(line, currentParagraph)) {
  // 保存当前段落，开始新段落
  paragraphs.push(currentParagraph.trim());
  currentParagraph = line;
}

// 语言变化检测
const isCurrentChinese = isChineseText(currentLine);
const isLastChinese = isChineseText(lastPart);
if (isCurrentChinese !== isLastChinese) {
  return true;
}
```

## 修正建议 005

### 四个自然段分割问题修复

**问题描述：**
用户反馈了一个新的段落分割问题：
- **四个自然段问题：** 两个英文段落和两个中文段落（共四个自然段）被错误地处理成一个 `<p>` 标签
- **期望行为：** 每个自然段应该独立处理为单独的 `<p>` 标签

**具体测试数据：**
```
This is a test sentence. Another sentence follows here! And here is a question? Finally, this is the last sentence.
我们往往自信站得足够近，占尽了先天的优势，但其实也不过"看山是山"罢了。
This is a test sentence. Another sentence follows here! And here is a question? Finally, this is the last sentence.
我们往往自信站得足够近，占尽了先天的优势，但其实也不过"看山是山"罢了。
```

**问题分析：**
1. **错误的行分割逻辑：** `isNewParagraphStart` 函数中使用了 `split(' ')` 来获取最后一行，应该使用 `split('\\n')` 按换行符分割
2. **句号结尾判断不够智能：** 只有在特殊标记开头时才认为是新段落，忽略了普通的句号结尾情况
3. **语言变化检测基础错误：** 由于行分割错误，语言变化检测也失效

**期望结果：**
- 第1段：英文段落（独立 `<p>` 标签）
- 第2段：中文段落（独立 `<p>` 标签）
- 第3段：英文段落（独立 `<p>` 标签）
- 第4段：中文段落（独立 `<p>` 标签）

**实现方案：**
1. **修复行分割逻辑**
   - 将 `split(' ')` 改为 `split(/[\\n\\r]+/)` 按换行符分割
   - 正确获取段落的最后一行内容
   - 确保语言检测基于正确的行内容

2. **增强句号结尾判断**
   - 保留语言变化的自动分段逻辑
   - 保留句号结尾 + 特殊标记的分段逻辑
   - 新增：句号结尾 + 新内容开始的分段逻辑
   - 避免以空格开头的行被误判为新段落

3. **优化判断条件**
   ```typescript
   // 语言变化时自动分段（优先级最高）
   if (isCurrentChinese !== isLastChinese) {
     return true;
   }
   
   // 句号结尾 + 特殊标记开头
   if (endsWithPeriod && startsWithMarker) {
     return true;
   }
   
   // 句号结尾 + 新内容开始（非空格开头）
   if (endsWithPeriod && currentLine.trim() && !currentLine.startsWith(' ')) {
     return true;
   }
   ```

4. **测试用例扩展**
   - 添加四个自然段测试用例
   - 验证中英文交替的段落分割
   - 验证句号结尾的自动分段
   - 确保不破坏现有功能

**技术细节：**
```typescript
// 修复前（错误）
const existingLines = existingParagraph.trim().split(' ');
const lastPart = existingLines[existingLines.length - 1];

// 修复后（正确）
const existingLines = existingParagraph.trim().split(/[\n\r]+/);
const lastLine = existingLines[existingLines.length - 1].trim();

// 新增判断逻辑
if (endsWithPeriod && currentLine.trim() && !currentLine.startsWith(' ')) {
  return true;
}
```

## 修正建议 006

### HTML段落后续文本处理问题修复

**问题描述：**
用户反馈了一个新的段落处理问题：
- **混合内容处理缺陷：** 当文本中包含已处理的HTML段落（`<p>`标签）时，紧跟在HTML段落后面的未处理文本没有被正确分割和处理
- **具体表现：** 第4第5段与第3段（已处理段落）之间没有空行，导致第4第5段原样返回，没有进行句子分割处理

**具体测试数据：**
```
—— 我们往往自信站得足够近，占尽了先天的优势，但其实也不过"看山是山"罢了。
人们认识事物，须经过多个不同阶段，这些阶段如台阶般层层递升，将我们逐渐带往更高的认识层次。在此过程中，先跳脱出事物本身，将眼光拓展、放远，能使我们更好地回归，形成更深的认识。如音乐或文学，在最初接触时，我们都处于一种熟悉的"本土"环境中，因此容易被环境所局限，产生"身在山中"而不能知其真面目的情况。我们往往自信站得足够近，占尽了先天的优势，但其实也不过"看山是山"罢了。
<p><span aria-label="那为何一波三折能成为我们认识世界的一剂良药呢？" data-speaker="" data-voice-id="">那为何一波三折能成为我们认识世界的一剂良药呢？</span>...</p>
This is a test sentence. Another sentence follows here! And here is a question? Finally, this is the last sentence.
我们往往自信站得足够近，占尽了先天的优势，但其实也不过"看山是山"罢了。
```

**问题分析：**
1. **HTML模式状态混乱：** 当HTML段落结束后，`insideHtmlTag` 状态被重置，但后续文本行的处理逻辑有缺陷
2. **段落边界识别失效：** HTML段落结束后，紧跟的文本行没有被识别为新段落的开始
3. **文本连接错误：** 后续文本被错误地连接到前面的内容中，而不是作为独立段落处理

**期望结果：**
- 第1段：`—— 我们往往自信站得足够近...` （未处理，需要分割）
- 第2段：`人们认识事物，须经过多个不同阶段...` （未处理，需要分割）
- 第3段：`<p><span>...</span></p>` （已处理，保持原样）
- 第4段：`This is a test sentence...` （未处理，需要分割）
- 第5段：`我们往往自信站得足够近...` （未处理，需要分割）

**实际问题：**
- 第1、2段：正确处理 ✅
- 第3段：保持原样 ✅
- 第4、5段：原样返回，未处理 ❌

**根本原因：**
在 `splitTextIntoParagraphs` 函数中，当处理普通文本行时，缺少对HTML标签内文本的明确处理分支，导致HTML段落结束后的文本行被错误处理。

**实现方案：**
1. **增加HTML内文本处理分支**
   - 在 `else if (line.includes('</p>'))` 后增加 `else if (insideHtmlTag)` 分支
   - 明确处理HTML标签内的文本行
   - 确保HTML内容正确连接

2. **优化处理逻辑顺序**
   ```typescript
   if (line.includes('<p>') || line.includes('<p ')) {
     // HTML段落开始
   } else if (line.includes('</p>')) {
     // HTML段落结束
   } else if (insideHtmlTag) {
     // HTML标签内的文本（新增）
     currentParagraph += (currentParagraph ? '\n' : '') + line;
   } else {
     // 普通文本行
   }
   ```

3. **确保状态一致性**
   - HTML段落结束后正确重置状态
   - 后续文本行能够正确进入普通文本处理逻辑
   - 智能段落边界识别正常工作

4. **测试用例扩展**
   - 添加混合已处理和未处理段落测试用例
   - 验证HTML段落后的文本正确处理
   - 确保不破坏现有功能

**技术细节：**
```typescript
// 修复前（缺少HTML内文本处理）
} else if (line.includes('</p>')) {
  // HTML段落结束
  // ...
} else {
  // 普通文本行
  // ...
}

// 修复后（增加HTML内文本处理）
} else if (line.includes('</p>')) {
  // HTML段落结束
  // ...
} else if (insideHtmlTag) {
  // 在HTML标签内的文本
  currentParagraph += (currentParagraph ? '\n' : '') + line;
} else {
  // 普通文本行
  // ...
}
```

## 修正建议 007

### HTML段落后紧跟文本处理问题修复

**问题描述：**
用户反馈 `preprocessTextWithSentenceSplitter` 函数在处理HTML段落后紧跟的文本时仍存在问题：
- **HTML段落后紧跟文本未处理：** 当在已处理内容后添加新的自然段，如果紧跟已处理部分，新段落不会被处理
- **空行依赖问题：** 只有当新添加的自然段和已处理的部分额外空开一行，新段落才会被处理
- **段落分割逻辑缺陷：** HTML段落结束后的文本被错误地合并而不是独立处理

**问题分析：**
1. **段落分割逻辑不够智能：** `splitTextIntoParagraphs` 函数在处理普通文本时，过度依赖 `isNewParagraphStart` 判断
2. **HTML段落后处理缺失：** 缺少对HTML段落结束后紧跟文本的特殊处理逻辑
3. **条件判断不完整：** `isNewParagraphStart` 函数未考虑HTML段落结束的特殊情况

**实现方案：**
1. **优化段落分割逻辑**
   - 重构 `splitTextIntoParagraphs` 函数中的普通文本处理逻辑
   - 简化条件判断，优先处理空段落情况
   - 确保逻辑清晰和高效

2. **增强HTML段落后处理**
   - 在 `isNewParagraphStart` 函数中增加HTML段落结束检测
   - 当已有段落以 `</p>` 结尾时，任何非空文本都视为新段落
   - 确保HTML段落后的文本能被正确分割

3. **优化判断优先级**
   - HTML段落结束检测优先级最高
   - 其次是语言变化检测
   - 最后是句号结尾和特殊标记检测

4. **扩展测试用例**
   - 新增"HTML段落后紧跟文本测试（用户最新反馈）"测试用例
   - 验证HTML段落后紧跟文本的正确处理

**技术细节：**
```typescript
// 在 isNewParagraphStart 函数中增加HTML段落结束检测
if (existingParagraph.trim().endsWith('</p>')) {
  return true;
}

// 在 splitTextIntoParagraphs 函数中优化普通文本处理
if (!currentParagraph.trim()) {
  currentParagraph = line;
} else if (isNewParagraphStart(line, currentParagraph)) {
  paragraphs.push(currentParagraph.trim());
  currentParagraph = line;
} else {
  currentParagraph += ' ' + line;
}
```

## 后续优化

1. **性能优化**
   - 缓存分割结果
   - 优化大文本处理
   - 减少内存占用

2. **功能扩展**
   - 支持更多语言
   - 自定义分割规则
   - 更智能的语言检测

3. **用户体验**
   - 提供配置选项
   - 更好的错误提示
   - 处理进度显示