# MathLive 示例页面需求

## 用户需求

用户要求在 `/src/app/examples/mathlive/v1` 目录下生成一个 example 页面，演示 MathLive 的基本用法。

## 功能需求

1. **基本数学输入演示**

   - 创建一个可交互的数学公式输入框
   - 显示用户输入的 LaTeX 格式
   - 实时渲染数学公式

2. **MathLive 核心功能展示**

   - 数学公式编辑器
   - 虚拟键盘支持
   - 多种数学符号输入
   - 公式求值功能

3. **用户界面要求**
   - 现代化的 UI 设计
   - 响应式布局
   - 清晰的功能说明
   - 示例公式展示

## 技术实现

- 使用 Next.js 15 App Router
- 集成 MathLive 库
- TypeScript 支持
- Tailwind CSS 样式
- 客户端组件实现

## 预期效果

- 用户可以在输入框中输入数学公式
- 实时显示 LaTeX 代码
- 渲染的数学公式清晰可见
- 提供常用数学公式示例
- 支持公式计算和求值
