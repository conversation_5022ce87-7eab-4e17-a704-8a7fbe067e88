<div class="math-type-doc" style="height: 100%; position: absolute; inset: 0px;">
    <div class="doc-toolbar-container role-toolbar-wrapper"><tool-bar class=""
            style="padding-left: 0px;"><too-bar-container><tool-bar-item><tool-bar-item-separator></tool-bar-item-separator><line-setting>
                        <detail><align-options class="setting-group-options">
                                <div title="Align Left (Ctrl+Shift+L)"
                                    class="toolbar_svg-icon-wrapper role-align-left-item disabled selected"><svg
                                        viewBox="0 0 448 512" style="width: 13px; height: 13px;">
                                        <path
                                            d="M288 44v40c0 8.837-7.163 16-16 16H16c-8.837 0-16-7.163-16-16V44c0-8.837 7.163-16 16-16h256c8.837 0 16 7.163 16 16zM0 172v40c0 8.837 7.163 16 16 16h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16zm16 312h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm256-200H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16h256c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16z">
                                        </path>
                                    </svg></div>
                                <div title="Align Middle (Ctrl+Shift+E)"
                                    class="toolbar_svg-icon-wrapper role-align-center-item disabled"><svg
                                        viewBox="0 0 448 512" style="width: 13px; height: 13px;">
                                        <path
                                            d="M352 44v40c0 8.837-7.163 16-16 16H112c-8.837 0-16-7.163-16-16V44c0-8.837 7.163-16 16-16h224c8.837 0 16 7.163 16 16zM16 228h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 256h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm320-200H112c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16h224c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16z">
                                        </path>
                                    </svg></div>
                                <div title="Align Right (Ctrl+Shift+R)"
                                    class="toolbar_svg-icon-wrapper  role-align-right-item disabled"><svg
                                        viewBox="0 0 448 512" style="width: 13px; height: 13px;">
                                        <path
                                            d="M160 84V44c0-8.837 7.163-16 16-16h256c8.837 0 16 7.163 16 16v40c0 8.837-7.163 16-16 16H176c-8.837 0-16-7.163-16-16zM16 228h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 256h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm160-128h256c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H176c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z">
                                        </path>
                                    </svg></div>
                                <div title="Align Justify (Ctrl+Shift+J)"
                                    class="toolbar_svg-icon-wrapper  role-align-justify-item toolbar-container disabled">
                                    <svg viewBox="0 0 448 512" style="width: 13px; height: 13px;">
                                        <path
                                            d="M0 84V44c0-8.837 7.163-16 16-16h416c8.837 0 16 7.163 16 16v40c0 8.837-7.163 16-16 16H16c-8.837 0-16-7.163-16-16zm16 144h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 256h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0-128h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z">
                                        </path>
                                    </svg></div>
                            </align-options><v-separator></v-separator><expandable-component classname="" tabindex="-1"
                                class="button-action test-line-more-options"
                                style="margin-left: 4px; color: gray; fill: gray;"><label-display
                                    style="padding-top: 0px; line-height: 1.5em; padding-bottom: 1px;"><text>...</text></label-display></expandable-component>
                        </detail><disabled-layer></disabled-layer>
                    </line-setting></tool-bar-item><tool-bar-item><tool-bar-item-separator></tool-bar-item-separator><selection-settings
                        class="math mt-common-dialog"><selection-content><styled-select>
                                <div class="select-box-container" title="Math Font" tabindex="998"
                                    style="color: rgb(117, 117, 117); width: 100px;">
                                    <div data-for="select-box" class="input-container"><input-like readonly="true">(font
                                            default)</input-like>
                                        <div class="arrow-down-icon "><i class="fa fa-caret-down"
                                                aria-hidden="true"></i></div>
                                    </div>
                                </div>
                            </styled-select><separate-hbar
                                style="margin-left: 5px; margin-top: 2px; margin-bottom: 1px;"></separate-hbar><font-select
                                class="displaystyle"
                                title="\displaystyle mode"><font-select-container><small-symbol>∑</small-symbol><arrow-inside>→</arrow-inside><big-symbol>∑</big-symbol></font-select-container></font-select><separate-hbar
                                style="margin-left: 5px; margin-top: 2px; margin-bottom: 1px;"></separate-hbar><font-select
                                class="textstyle"
                                title="\textstyle mode"><font-select-container><big-symbol>∑</big-symbol><arrow-inside>→</arrow-inside><small-symbol>∑</small-symbol></font-select-container></font-select><separate-hbar
                                style="margin-left: 5px; margin-top: 2px; margin-bottom: 1px;"></separate-hbar><expandable-component
                                classname="" tabindex="-1" class="color-picker" style="margin-top: -2px; z-index: 0;">
                                <item class="setting" title="Text Color"
                                    style="width: 19px; height: 19px; padding: 0px; margin-top: 4px;"><svg
                                        style="width: 100%; height: 100%; transform: scale(0.9);"><text x="5" y="14"
                                            style="font-size: 15px; font-family: Asana-Math, Asana;">A</text>
                                        <line x1="0" y1="18" x2="20" y2="18"
                                            style="stroke-width: 3; stroke: rgb(0, 0, 0);"></line>
                                    </svg></item>
                            </expandable-component><expandable-component classname="" tabindex="-1" class="color-picker"
                                style="z-index: 0; margin-left: 2px; margin-top: -2px; margin-right: -2px;">
                                <item class="setting" title="Text Background Color"
                                    style="width: 19px; height: 20px; padding: 0px; margin-top: 4px;"><svg
                                        style="width: 100%; height: 100%; transform: scale(0.9); overflow: visible;">
                                        <rect x="0" y="0" width="19" height="19"
                                            style="fill: rgb(255, 255, 255); stroke: rgb(202, 199, 199);"></rect><text
                                            x="4" y="15"
                                            style="font-size: 15px; font-family: Asana-Math, Asana; fill: black;">A</text>
                                    </svg></item>
                            </expandable-component><separate-hbar
                                style="margin-left: 5px; margin-right: 5px;"></separate-hbar><expandable-component
                                classname="" tabindex="-1" class="">
                                <div class="toolbar__item-with-option test-copy-style" title="Copy Style"
                                    style="margin-top: 3px;"><svg viewBox="0 0 512 512"
                                        style="width: 13px; height: 13px; padding: 3px; fill: gray; border: 1px solid transparent; margin: -1px;">
                                        <path
                                            d="M416 128V32c0-17.67-14.33-32-32-32H32C14.33 0 0 14.33 0 32v96c0 17.67 14.33 32 32 32h352c17.67 0 32-14.33 32-32zm32-64v128c0 17.67-14.33 32-32 32H256c-35.35 0-64 28.65-64 64v32c-17.67 0-32 14.33-32 32v128c0 17.67 14.33 32 32 32h64c17.67 0 32-14.33 32-32V352c0-17.67-14.33-32-32-32v-32h160c53.02 0 96-42.98 96-96v-64c0-35.35-28.65-64-64-64z">
                                        </path>
                                    </svg><i class="fa fa-caret-down toolbar__item-with-option__caret"
                                        aria-hidden="true"
                                        style="padding: 2px; font-size: 11px; line-height: 15px;"></i></div>
                            </expandable-component><separate-hbar
                                style="margin-left: 5px; margin-top: 2px; margin-bottom: 1px;"></separate-hbar><span
                                style="margin-top: 6px; color: gray; font-size: 12px; margin-left: 3px;">Export:
                            </span><span style="display: inline-block;"><expandable-component classname="" tabindex="-1"
                                    class="button-action"
                                    style="margin-top: 2px; margin-left: 4px; font-size: 12px; z-index: 501; width: 90px;">
                                    <div style="display: flex; justify-content: center;"><button title="Export Image"
                                            class="btn-normal save-as-svg" style="width: 70px;">Image</button><button
                                            class="btn-normal" style="width: 20px; margin-left: -1px;"><i
                                                class="fa fa-caret-down" style="vertical-align: -1px;"></i></button>
                                    </div>
                                </expandable-component><span class="hidden-cp-image"
                                    style="visibility: hidden;"></span><span class="hidden-cp-svg"
                                    style="visibility: hidden;"></span></span><span
                                style="display: inline-block;"><expandable-component classname="" tabindex="-1"
                                    class="button-action"
                                    style="margin-top: 2px; margin-left: 4px; width: 90px; height: 22px; font-size: 12px; z-index: 501;">
                                    <div style="display: flex; justify-content: center;"><button title="Export Latex"
                                            class="btn-normal" style="width: 70px;">Latex</button><button
                                            class="btn-normal" style="width: 20px; margin-left: -1px;"><i
                                                class="fa fa-caret-down" style="vertical-align: -1px;"></i></button>
                                    </div>
                                </expandable-component><span class="hidden-cp-latex"
                                    style="visibility: hidden;"></span></span></selection-content>
                        <div class="hotkey-show-container"
                            style="font-family: &quot;Open Sans&quot;, sans-serif; position: absolute; left: 0px; top: 0px; z-index: 10;">
                        </div>
                    </selection-settings></tool-bar-item>
                <div style="flex-grow: 1;"><tool-bar-item-separator
                        style="float: right; height: 22px; margin-left: 0px;"></tool-bar-item-separator>
                    <div style="float: right; padding-top: 11px; padding-right: 4px;"><span title="Undo"
                            class="undo-redo-span" style=""><svg viewBox="0 0 512 512"
                                style="width: 13px; height: 13px; vertical-align: top;">
                                <path
                                    d="M255.545 8c-66.269.119-126.438 26.233-170.86 68.685L48.971 40.971C33.851 25.851 8 36.559 8 57.941V192c0 13.255 10.745 24 24 24h134.059c21.382 0 32.09-25.851 16.971-40.971l-41.75-41.75c30.864-28.899 70.801-44.907 113.23-45.273 92.398-.798 170.283 73.977 169.484 169.442C423.236 348.009 349.816 424 256 424c-41.127 0-79.997-14.678-110.63-41.556-4.743-4.161-11.906-3.908-16.368.553L89.34 422.659c-4.872 4.872-4.631 12.815.482 17.433C133.798 479.813 192.074 504 256 504c136.966 0 247.999-111.033 248-247.998C504.001 119.193 392.354 7.755 255.545 8z">
                                </path>
                            </svg></span><span title="Redo" class="undo-redo-span"
                            style="fill: lightgray; border: 1px solid transparent; cursor: default;"><svg
                                viewBox="0 0 512 512" style="width: 13px; height: 13px; vertical-align: top;">
                                <path
                                    d="M256.455 8c66.269.119 126.437 26.233 170.859 68.685l35.715-35.715C478.149 25.851 504 36.559 504 57.941V192c0 13.255-10.745 24-24 24H345.941c-21.382 0-32.09-25.851-16.971-40.971l41.75-41.75c-30.864-28.899-70.801-44.907-113.23-45.273-92.398-.798-170.283 73.977-169.484 169.442C88.764 348.009 162.184 424 256 424c41.127 0 79.997-14.678 110.629-41.556 4.743-4.161 11.906-3.908 16.368.553l39.662 39.662c4.872 4.872 4.631 12.815-.482 17.433C378.202 479.813 319.926 504 256 504 119.034 504 8.001 392.967 8 256.002 7.999 119.193 119.646 7.755 256.455 8z">
                                </path>
                            </svg></span></div><tool-bar-item-separator
                        style="float: right; height: 22px;"></tool-bar-item-separator>
                </div>
            </too-bar-container></tool-bar></div>
    <div class="doc-scroll-container" style="overflow: auto; height: calc(100% - 37px);"><content-area class=""
            style="min-width: 950px; width: 100%; text-align: center;"><editor-container
                style="display: inline-block;"><math-type aria-label="Editor Content"
                    class="math-type-no-print paragraph-mark enabled primary" tabindex="-1"
                    style="padding: 15px 20px 20px; width: 660px;">
                    <div class="mt-sa"></div><hidden-input-wrapper style="top: 1199.47px; left: 213.25px;"><input
                            autocorrect="off" autocapitalize="off"></hidden-input-wrapper><hidden-input-wrapper
                        aria-hidden="true" style="top: 1199.47px; left: 213.25px;"><textarea spellcheck="false"
                            autocorrect="off"
                            autocapitalize="off"></textarea></hidden-input-wrapper><hidden-input-wrapper
                        aria-hidden="true" style="top: 1199.47px; left: 213.25px;"><input spellcheck="false"
                            autocorrect="off" autocapitalize="off" class="focus-element"
                            readonly=""></hidden-input-wrapper><math-edit-container>
                        <editarea class="root-editor"
                            style="font-family: Arial, Helvetica, sans-serif; font-size: 15px;"><area-baseline
                                aria-hidden="true">a</area-baseline><area-container>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <block style="font-size: 1.2em; font-weight: bold;">Powerful Mathematics Editor
                                        </block>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <block style="text-decoration: underline; font-size: 0.8em;">for inputing and
                                            sharing your formulas with people! </block>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <emptyblock aria-label="empty line"> </emptyblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <block>Input your mathematics formula inline: </block>
                                        <compositeblock dir="ltr"
                                            class="math-container-symbol role-mathmode-area inline"
                                            style="font-size: 17px;">
                                            <editarea class="math-mode-font lazyable no-area-container"
                                                style="font-family: Asana-Math, Asana;">
                                                <line class="" style="line-height: 1.2;">
                                                    <baselineblock></baselineblock>
                                                    <compositeblock dir="ltr" class="over-arrow-symbol ig-ex-br inline">
                                                        <arrow-symbol
                                                            style="height: 0.588235em; margin-bottom: -0.235294em;"><svg
                                                                style="position: absolute; left: 0px; top: 0px; width: 100%; height: 100%;">
                                                                <path
                                                                    d=" M 10.29 4.85 l -3.15 -3.32 l -0.49 0.52 l 1.93 2.33 h -0.15 v 0.90 h 0.15 l -1.93 2.33 l 0.49 0.52 z  M 1.68 5.23 h 8.15 v -0.90 h -8.15 z"
                                                                    stroke="none"></path>
                                                            </svg></arrow-symbol><editarea-block class=""
                                                            style="line-height: 1.2;">F</editarea-block>
                                                    </compositeblock>
                                                    <block class="Normal"> </block>
                                                    <block class="Relation">=</block>
                                                    <block class="Normal"> m</block>
                                                    <compositeblock dir="ltr" class="over-arrow-symbol ig-ex-br inline">
                                                        <arrow-symbol
                                                            style="height: 0.588235em; margin-bottom: -0.470588em;"><svg
                                                                style="position: absolute; left: 0px; top: 0px; width: 100%; height: 100%;">
                                                                <path
                                                                    d=" M 9.35 4.85 l -3.15 -3.32 l -0.49 0.52 l 1.93 2.33 h -0.15 v 0.90 h 0.15 l -1.93 2.33 l 0.49 0.52 z  M 1.68 5.23 h 7.21 v -0.90 h -7.21 z"
                                                                    stroke="none"></path>
                                                            </svg></arrow-symbol><editarea-block class=""
                                                            style="line-height: 1.2;">a</editarea-block>
                                                    </compositeblock>
                                                </line>
                                            </editarea>
                                        </compositeblock>
                                        <block> or new line</block>
                                    </blocks>
                                </line>
                                <line class="root text-mode full-line-block-inside" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <compositeblock dir="ltr"
                                            class="math-container-symbol role-mathmode-area display"
                                            style="font-size: 17px;">
                                            <editarea class="math-mode-font lazyable no-area-container"
                                                style="font-family: Asana-Math, Asana;">
                                                <line class="taggable" style="line-height: 1.2;">
                                                    <baselineblock></baselineblock>
                                                    <compositeblock dir="ltr" class="fraction-symbol smaller">
                                                        <editarea-line class=" frac-edit-area enumerator"
                                                            style="margin-top: -0.235294em; line-height: 1.2; margin-bottom: -0.470588em;">
                                                            <baselineblock></baselineblock>
                                                            <block class="Normal">a</block>
                                                        </editarea-line>
                                                        <div class="frac-line">
                                                            <inline style="border-bottom: 1px solid;"></inline>
                                                        </div><editarea-line class=" frac-edit-area denominator"
                                                            style="margin-bottom: -0.176471em; line-height: 1.2; margin-top: -0.411765em;">
                                                            <baselineblock></baselineblock>
                                                            <block class="Normal">b</block>
                                                        </editarea-line>
                                                    </compositeblock>
                                                    <block class="Binary">+</block>
                                                    <compositeblock dir="ltr" class="sqrt-symbol"
                                                        style="padding-top: 0.235294em;"><sqrt-top
                                                            style="margin-right: -0.660294em; min-width: 0.660294em;">
                                                            <editarea class="no-area-container">
                                                                <line class=""
                                                                    style="margin-bottom: -0.168067em; line-height: 1.2;">
                                                                    <baselineblock></baselineblock>
                                                                    <block>3</block>
                                                                </line>
                                                            </editarea>
                                                        </sqrt-top><sqrt-edit><editarea-line class=" edit-area"
                                                                style="line-height: 1.2; margin-top: 0.117647em;">
                                                                <baselineblock></baselineblock>
                                                                <block class="Normal">a</block>
                                                                <block class="Binary">+</block>
                                                                <block class="Normal">b</block>
                                                            </editarea-line><sqrt-symbol-line><svg
                                                                    style="stroke: none;">
                                                                    <polygon
                                                                        points="0.35,10.29 4.79,7.92 9.66,18.24 16.36,0.00 50.56,0.00 50.56,1.05 17.09,1.05 9.18,22.59 3.10,9.85 0.64,10.77">
                                                                    </polygon>
                                                                </svg></sqrt-symbol-line></sqrt-edit></compositeblock>
                                                    <block class="Binary">+</block>
                                                    <compositeblock dir="ltr"
                                                        class="integral-like-symbol limit-type non-limit-kind">
                                                        <editarea-block class="from from-int "
                                                            style="line-height: 1.2; justify-content: flex-start; margin-left: 1.71em; margin-bottom: -1.0084em; min-height: 1.11765em; margin-top: 0em;">a</editarea-block>
                                                        <symbol class=""
                                                            style="font-family: Asana-Math, Asana; font-style: normal; font-weight: normal;">
                                                            <span>∫</span></symbol><editarea-block class="to to-int"
                                                            style="line-height: 1.2; display: flex; justify-content: flex-end; flex-direction: column; margin-left: 1em; margin-top: -0.588235em; min-height: 1em; margin-bottom: 0em;">b</editarea-block>
                                                    </compositeblock>
                                                    <block class="Normal">dx</block>
                                                    <block class="Binary">+</block>
                                                    <compositeblock dir="ltr" class="over-brace-symbol">
                                                        <editarea class="overValue center no-area-container">
                                                            <line class=""
                                                                style="margin-bottom: -0.168067em; line-height: 1.2;">
                                                                <baselineblock></baselineblock>
                                                                <block>n+1</block>
                                                            </line>
                                                        </editarea><top-brace
                                                            style="height: 0.461765em;"><brace-top-wrapper
                                                                style="height: 0.65em; font-family: Asana-Math, Asana; font-size: 0.710625em; width: 2.64887em; font-style: normal;">
                                                                <first>⏜</first><middle-wrapper>
                                                                    <fixed>
                                                                        <middle-inside>⏟</middle-inside><middle-inside>⏟</middle-inside>
                                                                    </fixed>
                                                                </middle-wrapper>
                                                                <middle>⏝</middle><middle-wrapper>
                                                                    <fixed>
                                                                        <middle-inside>⏟</middle-inside><middle-inside>⏟</middle-inside>
                                                                    </fixed>
                                                                </middle-wrapper>
                                                                <last>⏞</last>
                                                            </brace-top-wrapper></top-brace>
                                                        <div style="clear: both;">
                                                            <editarea class="center value no-area-container">
                                                                <line class="" style="line-height: 1.2;">
                                                                    <baselineblock></baselineblock>
                                                                    <block class="Normal">a</block>
                                                                    <block class="Binary">-</block>
                                                                    <block class="Normal">b</block>
                                                                </line>
                                                            </editarea>
                                                        </div>
                                                    </compositeblock>
                                                    <block class="Binary">+</block>
                                                    <compositeblock dir="ltr" class="over-arc-symbol ig-ex-br">
                                                        <symbol
                                                            style="display: block; width: 100%; position: relative; height: 0.529412em; margin-bottom: -0.176471em;">
                                                            <svg
                                                                style="position: absolute; left: 0px; top: 0px; display: block; width: 100%; height: 100%;">
                                                                <path
                                                                    d="M0,9.126262626262626 C 8.706250190734863 0.9444444444444444, 26.11875057220459 0.9444444444444444, 34.82500076293945 9.126262626262626  C 26.11875057220459 2.1587301587301586, 8.706250190734863 2.1587301587301586, 0 9.126262626262626 Z"
                                                                    stroke-width="0.03em"></path>
                                                            </svg></symbol>
                                                        <editarea class="no-area-container">
                                                            <line class="" style="line-height: 1.2;">
                                                                <baselineblock></baselineblock>
                                                                <block class="Normal">ABC</block>
                                                            </line>
                                                        </editarea>
                                                    </compositeblock>
                                                    <block class="Binary">+</block>
                                                    <compositeblock dir="ltr" class="wide-hat-symbol ig-ex-br">
                                                        <symbol
                                                            style="display: block; width: 100%; position: relative; height: 0.529412em; margin-bottom: -0.176471em;">
                                                            <svg
                                                                style="position: absolute; left: 0px; top: 0px; display: block; width: 100%; height: 100%; overflow: visible;">
                                                                <polyline points="0.00,8.06 17.41,0.94 34.83,8.06"
                                                                    stroke-width="1" fill="none"
                                                                    stroke-linejoin="bevel"></polyline>
                                                            </svg></symbol>
                                                        <editarea class="no-area-container"
                                                            style="display: inline-flex;">
                                                            <line class="" style="line-height: 1.2;">
                                                                <baselineblock></baselineblock>
                                                                <block class="Normal">ABC</block>
                                                            </line>
                                                        </editarea>
                                                    </compositeblock>
                                                    <block class="Binary">+</block>
                                                    <opensymbolblock class="normal" type="Uparrow">
                                                        <hidden-span>⇑</hidden-span><bracket-span>⇑</bracket-span>
                                                    </opensymbolblock>
                                                    <block class="Normal">H</block>
                                                    <compositeblock dir="ltr" class="power-index-symbol-container">
                                                        <middle-base>
                                                            <inline></inline>
                                                        </middle-base><editarea-block class="index-value"
                                                            style="line-height: 1.2; margin-top: -0.747899em;">2</editarea-block>
                                                    </compositeblock>
                                                    <block class="Normal">0</block>
                                                    <closesymbolblock class="normal" type="Uparrow">
                                                        <hidden-span>⇑</hidden-span><bracket-span>⇑</bracket-span>
                                                    </closesymbolblock>
                                                </line>
                                                <line class="taggable" style="line-height: 1.2;">
                                                    <baselineblock></baselineblock>
                                                    <emptyblock aria-label="empty line"> </emptyblock>
                                                </line>
                                            </editarea>
                                        </compositeblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <block>Matrices, cases, layouts</block>
                                    </blocks>
                                </line>
                                <line class="root text-mode full-line-block-inside" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <compositeblock dir="ltr"
                                            class="math-container-symbol role-mathmode-area display"
                                            style="font-size: 17px;">
                                            <editarea class="math-mode-font lazyable no-area-container"
                                                style="font-family: Asana-Math, Asana;">
                                                <line class="taggable" style="line-height: 1.2;">
                                                    <baselineblock></baselineblock>
                                                    <compositeblock dir="ltr"
                                                        class="matrix-symbol matrix-like matrix-math-mode role-tabular">
                                                        <base-line-indicator>a</base-line-indicator>
                                                        <matrix class="matrix"><svg
                                                                style="width: 0.5em; z-index: 1; height: 47.175px;">
                                                                <path
                                                                    d=" M 6.27 1.70 c 0.00 -0.06 -0.04 -0.08 -0.08 -0.08 c -0.03 0.00 -0.06 0.01 -0.08 0.02 c -2.55 1.34 -4.65 3.73 -4.65 9.40 v 0.60 h 1.53 v -0.60 c 0.00 -3.23 0.64 -7.03 3.31 -9.06 c 0.12 -0.09 0.13 -0.19 0.13 -0.28 z   M 1.44 11.64 v 23.90 h 1.53 v -23.90 z"
                                                                    stroke="none"></path>
                                                                <path
                                                                    d=" M 6.27 45.47 c 0.00 0.06 -0.04 0.08 -0.08 0.08 c -0.03 0.00 -0.06 -0.01 -0.08 -0.02 c -2.55 -1.34 -4.65 -3.73 -4.65 -9.40 v -0.60 h 1.53 v 0.60 c 0.00 3.23 0.64 7.03 3.31 9.06 c 0.12 0.09 0.13 0.19 0.13 0.28 z"
                                                                    stroke="none"></path>
                                                            </svg>
                                                            <table>
                                                                <tbody>
                                                                    <tr class="math-row">
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Normal">a</block>
                                                                                <compositeblock dir="ltr"
                                                                                    class="power-index-symbol-container">
                                                                                    <middle-base>
                                                                                        <inline></inline>
                                                                                    </middle-base><editarea-block
                                                                                        class="index-value"
                                                                                        style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                                </compositeblock>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Normal">b</block>
                                                                                <compositeblock dir="ltr"
                                                                                    class="power-index-symbol-container">
                                                                                    <middle-base>
                                                                                        <inline></inline>
                                                                                    </middle-base><editarea-block
                                                                                        class="index-value"
                                                                                        style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                                </compositeblock>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print sbd-lc"></div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr class="math-row">
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Normal">c</block>
                                                                                <compositeblock dir="ltr"
                                                                                    class="power-index-symbol-container">
                                                                                    <middle-base>
                                                                                        <inline></inline>
                                                                                    </middle-base><editarea-block
                                                                                        class="index-value"
                                                                                        style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                                </compositeblock>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print sbd-lr"></div>
                                                                        </td>
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Normal">d</block>
                                                                                <compositeblock dir="ltr"
                                                                                    class="power-index-symbol-container">
                                                                                    <middle-base>
                                                                                        <inline></inline>
                                                                                    </middle-base><editarea-block
                                                                                        class="index-value"
                                                                                        style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                                </compositeblock>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print sbd-lr sbd-lc">
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table><svg style="width: 0.5em; height: 47.175px;">
                                                                <path
                                                                    d=" M 1.75 1.70 c 0.00 -0.06 0.04 -0.08 0.08 -0.08 c 0.03 0.00 0.06 0.01 0.08 0.02 c 2.55 1.34 4.65 3.73 4.65 9.40 v 0.60 h -1.53 v -0.60 c 0.00 -3.23 -0.64 -7.03 -3.31 -9.06 c -0.12 -0.09 -0.13 -0.19 -0.13 -0.28 z  M 6.58 11.64 v 23.90 h -1.53 v -23.90 z"
                                                                    stroke="none"></path>
                                                                <path
                                                                    d=" M 1.75 45.47 c 0.00 0.06 0.04 0.08 0.08 0.08 c 0.03 0.00 0.06 -0.01 0.08 -0.02 c 2.55 -1.34 4.65 -3.73 4.65 -9.40 v -0.60 h -1.53 v 0.60 c 0.00 3.23 -0.64 7.03 -3.31 9.06 c -0.12 0.09 -0.13 0.19 -0.13 0.28 z"
                                                                    stroke="none"></path>
                                                            </svg>
                                                        </matrix>
                                                    </compositeblock>
                                                    <block class="Normal"> </block>
                                                    <compositeblock dir="ltr"
                                                        class="matrix-symbol matrix-like matrix-math-mode role-tabular">
                                                        <base-line-indicator>a</base-line-indicator>
                                                        <matrix class="matrix"><svg
                                                                style="width: 0.45em; z-index: 1; height: 64.4px;">
                                                                <path
                                                                    d=" M 5.98 1.70 l -0.10 -0.10 q -1.09 0.03 -2.16 0.03 q -1.02 0.00 -2.14 -0.03 l -0.15 0.22 q 0.15 0.58 0.15 3.15 v 0.85 h 1.43 v -0.85 q 0.00 -1.31 0.07 -2.14 q 0.05 -0.60 1.19 -0.60 h 1.65 l 0.07 -0.09 v -0.44 z  M 1.58 5.63 v 53.15 h 1.43 v -53.15 z "
                                                                    stroke="none"></path>
                                                                <path
                                                                    d=" M 5.98 62.70 l -0.10 0.10 q -1.09 -0.03 -2.16 -0.03 q -1.02 0.00 -2.14 0.03 l -0.15 -0.22 q 0.15 -0.58 0.15 -3.15 v -0.85 h 1.43 v 0.85 q 0.00 1.31 0.07 2.14 q 0.05 0.60 1.19 0.60 h 1.65 l 0.07 0.09 v 0.44 z"
                                                                    stroke="none"></path>
                                                            </svg>
                                                            <table>
                                                                <tbody>
                                                                    <tr class="math-row">
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Normal">1</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Relation">⋯</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Normal">1</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print sbd-lc"></div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr class="math-row">
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Relation">⋮</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Relation">⋰</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Relation">⋮</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print sbd-lc"></div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr class="math-row">
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Normal">1</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print sbd-lr"></div>
                                                                        </td>
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Relation">⋯</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print sbd-lr"></div>
                                                                        </td>
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Normal">1</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print sbd-lr sbd-lc">
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table><svg style="width: 0.45em; height: 64.4px;">
                                                                <path
                                                                    d=" M 1.57 1.70 l 0.10 -0.10 q 1.09 0.03 2.16 0.03 q 1.02 0.00 2.14 -0.03 l 0.15 0.22 q -0.15 0.58 -0.15 3.15 v 0.85 h -1.43 v -0.85 q 0.00 -1.31 -0.07 -2.14 q -0.05 -0.60 -1.19 -0.60 h -1.65 l -0.07 -0.09 v -0.44 z  M 5.97 5.63 v 53.15 h -1.43 v -53.15 z"
                                                                    stroke="none"></path>
                                                                <path
                                                                    d=" M 1.57 62.70 l 0.10 0.10 q 1.09 -0.03 2.16 -0.03 q 1.02 0.00 2.14 0.03 l 0.15 -0.22 q -0.15 -0.58 -0.15 -3.15 v -0.85 h -1.43 v 0.85 q 0.00 1.31 -0.07 2.14 q -0.05 0.60 -1.19 0.60 h -1.65 l -0.07 0.09 v 0.44 z"
                                                                    stroke="none"></path>
                                                            </svg>
                                                        </matrix>
                                                    </compositeblock>
                                                    <block class="Normal"> </block>
                                                    <compositeblock dir="ltr"
                                                        class="matrix-symbol matrix-like matrix-math-mode role-tabular">
                                                        <base-line-indicator>a</base-line-indicator>
                                                        <matrix class="array">
                                                            <table>
                                                                <tbody>
                                                                    <tr class="">
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Normal">fx</block>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class=" vline">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Normal">a</block>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="power-index-symbol-container inline">
                                                                                            <middle-base>
                                                                                                <inline></inline>
                                                                                            </middle-base><editarea-block
                                                                                                class="index-value"
                                                                                                style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                                        </compositeblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class=" vline">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Relation">↗
                                                                                        </block>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class=" vline">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Normal">a</block>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="power-index-symbol-container inline">
                                                                                            <middle-base>
                                                                                                <inline></inline>
                                                                                            </middle-base><editarea-block
                                                                                                class="index-value"
                                                                                                style="line-height: 1.2; margin-top: -0.747899em;">2</editarea-block>
                                                                                        </compositeblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print sbd-lc"></div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr class=" hline">
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <emptyblock
                                                                                            aria-label="empty line">
                                                                                        </emptyblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print sbd-lr"></div>
                                                                        </td>
                                                                        <td class=" vline">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Normal">b</block>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="power-index-symbol-container inline">
                                                                                            <middle-base>
                                                                                                <inline></inline>
                                                                                            </middle-base><editarea-block
                                                                                                class="index-value"
                                                                                                style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                                        </compositeblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print sbd-lr"></div>
                                                                        </td>
                                                                        <td class=" vline">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Relation">↙
                                                                                        </block>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print sbd-lr"></div>
                                                                        </td>
                                                                        <td class=" vline">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Normal">b</block>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="power-index-symbol-container inline">
                                                                                            <middle-base>
                                                                                                <inline></inline>
                                                                                            </middle-base><editarea-block
                                                                                                class="index-value"
                                                                                                style="line-height: 1.2; margin-top: -0.747899em;">2</editarea-block>
                                                                                        </compositeblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print sbd-lr sbd-lc">
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </matrix>
                                                    </compositeblock>
                                                    <block class="Normal"> fx</block>
                                                    <block class="Relation">=</block>
                                                    <compositeblock dir="ltr"
                                                        class="matrix-symbol case-symbol role-tabular">
                                                        <base-line-indicator>a</base-line-indicator>
                                                        <matrix class="cases"><svg
                                                                style="width: 0.5em; height: 43.2px;">
                                                                <path
                                                                    d=" M 8.35 1.70 c 0.00 -0.03 -0.05 -0.06 -0.12 -0.06 c -0.15 0.00 -0.44 0.09 -0.51 0.11 c -3.03 1.08 -3.59 3.33 -3.59 7.41 v 0.60 h 1.62 v -1.28 c 0.00 -2.20 0.12 -5.16 2.13 -6.48 c 0.15 -0.10 0.48 -0.22 0.48 -0.31 z  M 4.13 9.17 v 8.58 h 1.62 v -8.58 z  M 5.75 17.75 h -1.62 v 0.59 c 0.00 2.97 -2.74 3.66 -2.75 3.66 v 0.02 c 0.02 0.00 2.75 0.75 2.75 3.71 h 1.62 c 0.00 -1.05 -0.09 -2.87 -2.96 -3.69 c 2.87 -0.80 2.96 -2.63 2.96 -3.69 v -0.59 z  M 4.13 25.45 v 8.58 h 1.62 v -8.58 z"
                                                                    stroke="none"></path>
                                                                <path
                                                                    d=" M 8.35 41.50 c 0.00 0.03 -0.05 0.06 -0.12 0.06 c -0.15 0.00 -0.44 -0.09 -0.51 -0.11 c -3.03 -1.08 -3.59 -3.33 -3.59 -7.41 v -0.60 h 1.62 v 1.28 c 0.00 2.20 0.12 5.16 2.13 6.48 c 0.15 0.10 0.48 0.22 0.48 0.31 z"
                                                                    stroke="none"></path>
                                                            </svg>
                                                            <table>
                                                                <tbody>
                                                                    <tr class="math-row">
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Normal">𝛼</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print sbd-lc"></div>
                                                                        </td>
                                                                        <td class="non-select"></td>
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Normal"
                                                                                    style="font-family: Asana-Mathrm, Asana-Math, Asana;">
                                                                                    if</block>
                                                                                <block class="Punctuation"
                                                                                    style="font-family: Asana-Mathrm, Asana-Math, Asana;">
                                                                                    ,</block>
                                                                                <block class="Normal"> a</block>
                                                                                <block class="Relation">=</block>
                                                                                <block class="Normal">b</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print sbd-lc"></div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr class="math-row">
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Normal">𝛽</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print sbd-lc sbd-lr">
                                                                            </div>
                                                                        </td>
                                                                        <td class="non-select"></td>
                                                                        <td class=""><editarea-line class=" editor-cell"
                                                                                style="line-height: 1.2;">
                                                                                <baselineblock></baselineblock>
                                                                                <block class="Normal"
                                                                                    style="font-family: Asana-Mathrm, Asana-Math, Asana;">
                                                                                    if</block>
                                                                                <block class="Punctuation"
                                                                                    style="font-family: Asana-Mathrm, Asana-Math, Asana;">
                                                                                    ,</block>
                                                                                <block class="Normal"> a</block>
                                                                                <block class="Relation">≠</block>
                                                                                <block class="Normal">b</block>
                                                                            </editarea-line>
                                                                            <div class="sbd no-print sbd-lc sbd-lr">
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </matrix>
                                                    </compositeblock>
                                                </line>
                                            </editarea>
                                        </compositeblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <emptyblock aria-label="empty line"> </emptyblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <block>Fonts</block>
                                    </blocks>
                                </line>
                                <line class="root text-mode full-line-block-inside" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <compositeblock dir="ltr"
                                            class="math-container-symbol role-mathmode-area display"
                                            style="font-size: 17px;">
                                            <editarea class="math-mode-font lazyable no-area-container"
                                                style="font-family: Asana-Math, Asana;">
                                                <line class="taggable" style="line-height: 1.2;">
                                                    <baselineblock></baselineblock>
                                                    <block class="Normal"
                                                        style="font-family: Asana-Mathcal, Asana-Math, Asana;">L</block>
                                                    <block class="Punctuation">,</block>
                                                    <block class="Normal"
                                                        style="font-family: Asana-Math, Asana; font-weight: bold;">a
                                                    </block>
                                                    <block class="Punctuation">,</block>
                                                    <block class="Normal">n</block>
                                                    <block class="Relation">⊂</block>
                                                    <opensymbolblock class="normal" type="parenthesis">
                                                        <bracket-span>(</bracket-span></opensymbolblock>
                                                    <block class="Normal"
                                                        style="font-family: Asana-Mathbb, Asana-Math, Asana;">R</block>
                                                    <block class="Binary"
                                                        style="font-family: Asana-Mathbb, Asana-Math, Asana;">∩</block>
                                                    <block class="Normal"
                                                        style="font-family: Asana-Mathbb, Asana-Math, Asana;">N</block>
                                                    <closesymbolblock class="normal" type="parenthesis">
                                                        <bracket-span>)</bracket-span></closesymbolblock>
                                                    <block class="Normal"> </block>
                                                    <opensymbolblock class="sm-2" type="angle"
                                                        style="height: 1.17647em;"><hidden-span
                                                            style="margin-top: -0.2em;">&lt;</hidden-span><svg
                                                            style="overflow: visible;">
                                                            <polyline points="6.08,1.89 1.58,10.00 6.08,18.11"
                                                                stroke-width="1" fill="none"></polyline>
                                                        </svg></opensymbolblock>
                                                    <block class="Normal"
                                                        style="font-family: Asana-Mathrm, Asana-Math, Asana;">gen
                                                    </block>
                                                    <block class="Punctuation"
                                                        style="font-family: Asana-Mathrm, Asana-Math, Asana;">,</block>
                                                    <block class="Normal"
                                                        style="font-family: Asana-Mathrm, Asana-Math, Asana;">diff
                                                    </block>
                                                    <block class="Punctuation"
                                                        style="font-family: Asana-Mathrm, Asana-Math, Asana;">,</block>
                                                    <block class="Normal"
                                                        style="font-family: Asana-Mathrm, Asana-Math, Asana;">min
                                                    </block>
                                                    <closesymbolblock class="sm-2" type="angle"
                                                        style="height: 1.17647em;"><hidden-span
                                                            style="margin-top: -0.2em;">&gt;</hidden-span><svg
                                                            style="overflow: visible;">
                                                            <polyline points="1.58,1.89 6.08,10.00 1.58,18.11"
                                                                stroke-width="1" fill="none"></polyline>
                                                        </svg></closesymbolblock>
                                                </line>
                                            </editarea>
                                        </compositeblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <emptyblock aria-label="empty line"> </emptyblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <block>Diagram </block>
                                    </blocks>
                                </line>
                                <line class="root text-mode full-line-block-inside" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <compositeblock dir="ltr" class="math-diagram inline" aria-label="Diagram"
                                            role="img"><math-diagram aria-hidden="true" data-amt="diagram"
                                                style="height: 235px; z-index: 1;"><clip-region
                                                    style="width: 100%; height: 100%; display: block; overflow: hidden; position: relative;"><zoom-region><svg>
                                                            <g>
                                                                <defs>
                                                                    <pattern id=".8964459728885833" width="10"
                                                                        height="10" patternUnits="userSpaceOnUse">
                                                                        <path d="M 10 0 L 0 0 0 10" fill="none"
                                                                            stroke="lightgray" stroke-width="0.5">
                                                                        </path>
                                                                    </pattern>
                                                                </defs>
                                                                <rect width="100%" height="100%"
                                                                    fill="url(#.8964459728885833)" stroke="lightgray"
                                                                    stroke-width="0.5"></rect>
                                                            </g>
                                                        </svg>
                                                        <div class="diagram-plotly-layer"
                                                            style="position: absolute; inset: 0px; pointer-events: none; fill: none; stroke: none;">
                                                            <div></div>
                                                        </div><svg class="role-diagram-draw-area">
                                                            <g class="shapes-region" style="stroke: black; fill: none;">
                                                                <g class="arrow-line">
                                                                    <path class="connection real" stroke-dasharray=""
                                                                        d="  M274,30 L274,103.29"
                                                                        style="stroke: rgb(74, 144, 226); stroke-opacity: 1; stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                    </path>
                                                                    <g stroke="rgb(74,144,226)" stroke-opacity="1"
                                                                        fill="rgb(74,144,226)" fill-opacity="1"
                                                                        transform="matrix(6.123233995736766e-17,1,-1,6.123233995736766e-17,274,103.2888122430702)"
                                                                        style="stroke: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke-width: 1;">
                                                                        <circle cx="0" cy="0" r="3.35"></circle>
                                                                    </g>
                                                                    <g stroke="rgb(74,144,226)" stroke-opacity="1"
                                                                        fill="rgb(74,144,226)" fill-opacity="1"
                                                                        transform="matrix(6.123233995736766e-17,1,-1,6.123233995736766e-17,274,30)"
                                                                        style="stroke: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke-width: 1;">
                                                                        <circle cx="0" cy="0" r="3.35"></circle>
                                                                    </g>
                                                                    <path class="connection transparent no-print"
                                                                        stroke-dasharray="" d="  M274,30 L274,103.29">
                                                                    </path>
                                                                </g>
                                                                <g class="arrow-line">
                                                                    <path class="connection real" stroke-dasharray=""
                                                                        d="  M427.58,100 L269.5,100"
                                                                        style="stroke: rgb(74, 144, 226); stroke-opacity: 1; stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                    </path>
                                                                    <g stroke="rgb(74,144,226)" stroke-opacity="1"
                                                                        fill="rgb(74,144,226)" fill-opacity="1"
                                                                        transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,269.5,100)"
                                                                        style="stroke: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke-width: 1;">
                                                                        <circle cx="0" cy="0" r="3.35"></circle>
                                                                    </g>
                                                                    <g stroke="rgb(74,144,226)" stroke-opacity="1"
                                                                        fill="rgb(74,144,226)" fill-opacity="1"
                                                                        transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,427.57909412695915,100)"
                                                                        style="stroke: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke-width: 1;">
                                                                        <circle cx="0" cy="0" r="3.35"></circle>
                                                                    </g>
                                                                    <path class="connection transparent no-print"
                                                                        stroke-dasharray=""
                                                                        d="  M427.58,100 L269.5,100"></path>
                                                                </g>
                                                                <g class="arrow-line">
                                                                    <path class="connection real" stroke-dasharray=""
                                                                        d="  M270.5,32 L427.5,100"
                                                                        style="stroke: rgb(74, 144, 226); stroke-opacity: 1; stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                    </path>
                                                                    <g stroke="rgb(74,144,226)" stroke-opacity="1"
                                                                        fill="rgb(74,144,226)" fill-opacity="1"
                                                                        transform="matrix(0.9176159390078092,0.3974682232315139,-0.3974682232315139,0.9176159390078092,427.5,100)"
                                                                        style="stroke: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke-width: 1;">
                                                                        <circle cx="0" cy="0" r="3.35"></circle>
                                                                    </g>
                                                                    <g stroke="rgb(74,144,226)" stroke-opacity="1"
                                                                        fill="rgb(74,144,226)" fill-opacity="1"
                                                                        transform="matrix(0.9176159390078092,0.3974682232315139,-0.3974682232315139,0.9176159390078092,270.5,32)"
                                                                        style="stroke: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke-width: 1;">
                                                                        <circle cx="0" cy="0" r="3.35"></circle>
                                                                    </g>
                                                                    <path class="connection transparent no-print"
                                                                        stroke-dasharray="" d="  M270.5,32 L427.5,100">
                                                                    </path>
                                                                </g>
                                                                <g class="arrow-line">
                                                                    <path class="connection real" stroke-dasharray=""
                                                                        d="  M271.5,31 L321.5,103"
                                                                        style="stroke: rgb(74, 144, 226); stroke-opacity: 1; stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                    </path>
                                                                    <g stroke="rgb(74,144,226)" stroke-opacity="1"
                                                                        fill="rgb(74,144,226)" fill-opacity="1"
                                                                        transform="matrix(0.5704268977734036,0.821348375719226,-0.821348375719226,0.5704268977734036,321.5,103)"
                                                                        style="stroke: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke-width: 1;">
                                                                        <circle cx="0" cy="0" r="3.35"></circle>
                                                                    </g>
                                                                    <g stroke="rgb(74,144,226)" stroke-opacity="1"
                                                                        fill="rgb(74,144,226)" fill-opacity="1"
                                                                        transform="matrix(0.5704268977734036,0.821348375719226,-0.821348375719226,0.5704268977734036,271.5,31)"
                                                                        style="stroke: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke-width: 1;">
                                                                        <circle cx="0" cy="0" r="3.35"></circle>
                                                                    </g>
                                                                    <path class="connection transparent no-print"
                                                                        stroke-dasharray="" d="  M271.5,31 L321.5,103">
                                                                    </path>
                                                                </g>
                                                                <g class="composite-shape">
                                                                    <path class="transparent no-print"
                                                                        d=" M311.81,87.37 C313.42,87.12 315.12,86.99 316.87,87 C327.75,87.07 336.54,92.5 336.5,99.12 C336.5,99.34 336.49,99.56 336.47,99.78">
                                                                    </path>
                                                                    <path class="real"
                                                                        d=" M311.81,87.37 C313.42,87.12 315.12,86.99 316.87,87 C327.75,87.07 336.54,92.5 336.5,99.12 C336.5,99.34 336.49,99.56 336.47,99.78 L316.8,99 Z"
                                                                        style="stroke-width: 1; stroke: none; stroke-opacity: 1; fill: rgb(74, 144, 226); fill-opacity: 0.22;">
                                                                    </path>
                                                                    <path class="real"
                                                                        d=" M311.81,87.37 C313.42,87.12 315.12,86.99 316.87,87 C327.75,87.07 336.54,92.5 336.5,99.12 C336.5,99.34 336.49,99.56 336.47,99.78"
                                                                        style="stroke-width: 1; stroke: rgb(74, 144, 226); stroke-opacity: 1; fill: none; fill-opacity: 0.22;">
                                                                    </path>
                                                                </g>
                                                                <g class="arrow-line">
                                                                    <path class="connection real" stroke-dasharray=""
                                                                        d="  M274,90 L287.04,90 L287.04,100.78"
                                                                        style="stroke: rgb(74, 144, 226); stroke-opacity: 1; stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                    </path>
                                                                    <path class="connection transparent no-print"
                                                                        stroke-dasharray=""
                                                                        d="  M274,90 L287.04,90 L287.04,100.78"></path>
                                                                </g>
                                                                <g class="composite-shape axis2d"
                                                                    style="stroke-width: 2; stroke: rgb(73, 135, 206); stroke-opacity: 1;">
                                                                    <path class="real"
                                                                        d=" M41,177 L280,177 M61,16 L61,199"></path>
                                                                    <path class="transparent"
                                                                        d=" M41,177 L280,177 M61,16 L61,199"></path>
                                                                    <path d=" M273,172 L280,177 L273,182"></path>
                                                                    <path d=" M56,23 L61,16 L66,23"></path>
                                                                    <path
                                                                        d=" M92,172 L92,182 M123,172 L123,182 M154,172 L154,182 M185,172 L185,182 M216,172 L216,182 M247,172 L247,182 M56,146 L66,146 M56,115 L66,115 M56,84 L66,84 M56,53 L66,53">
                                                                    </path>
                                                                    <g
                                                                        style="fill: rgb(0, 0, 0); fill-opacity: 1; font-size: 11px; font-weight: normal; text-anchor: end; stroke: none; font-family: Times;">
                                                                    </g>
                                                                </g>
                                                                <g class="composite-shape">
                                                                    <path class="transparent no-print"
                                                                        d=" M42,193 C93.67,-47 145.33,333 197,93">
                                                                    </path>
                                                                    <path class="real"
                                                                        d=" M42,193 C93.67,-47 145.33,333 197,93"
                                                                        style="stroke-width: 2; stroke: rgb(70, 155, 36); stroke-opacity: 1; fill: none; fill-opacity: 1;">
                                                                    </path>
                                                                </g>
                                                                <g class="composite-shape">
                                                                    <path class="transparent no-print"
                                                                        d=" M123,35 C164.67,221.67 206.33,221.67 248,35">
                                                                    </path>
                                                                    <path class="real"
                                                                        d=" M123,35 C164.67,221.67 206.33,221.67 248,35"
                                                                        style="stroke-width: 2; stroke: rgb(209, 53, 53); stroke-opacity: 1; fill: none; fill-opacity: 1;">
                                                                    </path>
                                                                </g>
                                                                <g class="arrow-line">
                                                                    <path class="connection real" stroke-dasharray=""
                                                                        d="  M16,34 L300,196"
                                                                        style="stroke: rgb(141, 34, 137); stroke-opacity: 1; stroke-width: 2; fill: none; fill-opacity: 1;">
                                                                    </path>
                                                                    <path class="connection transparent no-print"
                                                                        stroke-dasharray="" d="  M16,34 L300,196">
                                                                    </path>
                                                                </g>
                                                                <g class="composite-shape">
                                                                    <path class="transparent no-print"
                                                                        d=" M190,125 Q193.31,118.83 187.14,115.52 L181.62,112.56 Q172.81,107.83 176.12,101.66 Q172.81,107.83 164,103.1 M166.64,104.52 L158.48,100.14 Q152.31,96.83 149,103">
                                                                    </path>
                                                                    <path class="real"
                                                                        d=" M190,125 Q193.31,118.83 187.14,115.52 L181.62,112.56 Q172.81,107.83 176.12,101.66 Q172.81,107.83 164,103.1 M166.64,104.52 L158.48,100.14 Q152.31,96.83 149,103"
                                                                        style="stroke-width: 1; stroke: rgb(0, 0, 0); fill: none; fill-opacity: 1; stroke-dasharray: 1.125, 3.35;">
                                                                    </path>
                                                                </g>
                                                                <g></g>
                                                            </g>
                                                            <g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray="" d="  M515,21 L566.5,21"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,568.5,21)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray="" d="  M515,21 L566.5,21">
                                                                        </path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M593.5,21 L645.5,21"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,647.5,21)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M593.5,21 L645.5,21"></path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M464,58 L486.59,35.41"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-0.7071067811865479,0.7071067811865471,-0.7071067811865471,-0.7071067811865479,488,34)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M464,58 L486.59,35.41"></path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray="" d="  M465,71 L526.5,71"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,528.5,71.00000000000001)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray="" d="  M465,71 L526.5,71">
                                                                        </path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M553.5,71 L605.5,71"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,607.5,71.00000000000001)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M553.5,71 L605.5,71"></path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M551.4,58 L569.35,35.56"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-0.6246976608044968,0.7808667188358009,-0.7808667188358009,-0.6246976608044968,570.6,34)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M551.4,58 L569.35,35.56"></path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M631.4,58 L649.35,35.56"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-0.6246976608044968,0.7808667188358009,-0.7808667188358009,-0.6246976608044968,650.6000000000001,34)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M631.4,58 L649.35,35.56"></path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray="" d="  M451,84 L451,156"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1.8369701987210297e-16,-1,1,-1.8369701987210297e-16,451,157.99999999999994)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray="" d="  M451,84 L451,156">
                                                                        </path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M465,171 L526.5,171"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,528.5,171)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M465,171 L526.5,171"></path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M553.5,171 L605.5,171"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,607.5,170.99999999999997)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M553.5,171 L605.5,171"></path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M629.67,158 L651.22,125.66"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-0.554699215611405,0.8320509480795584,-0.8320509480795584,-0.554699215611405,652.3333333333333,124)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M629.67,158 L651.22,125.66"></path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M549.67,158 L571.22,125.66"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-0.554699215611405,0.8320509480795584,-0.8320509480795584,-0.554699215611405,572.3333333333333,124)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M549.67,158 L571.22,125.66"></path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M461.83,158 L488.89,125.54"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-0.6402437805056017,0.7681717916741637,-0.7681717916741637,-0.6402437805056017,490.1666666666667,124)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M461.83,158 L488.89,125.54"></path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M515,111 L534.18,111 M542.18,111 L566.5,111"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,568.5,111.00000000000001)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M515,111 L534.18,111 M542.18,111 L566.5,111">
                                                                        </path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M593.5,111 L615,111 M624,111 L645.5,111"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,647.5,111)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M593.5,111 L615,111 M624,111 L645.5,111">
                                                                        </path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M501,34 L501,64.72 M501,72.72 L501,96"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1.8369701987210297e-16,-1,1,-1.8369701987210297e-16,501,98)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M501,34 L501,64.72 M501,72.72 L501,96">
                                                                        </path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M581,34 L581,64.72 M581,72.72 L581,96"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1.8369701987210297e-16,-1,1,-1.8369701987210297e-16,581,98)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M581,34 L581,64.72 M581,72.72 L581,96">
                                                                        </path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray=""
                                                                            d="  M661,34 L661,64.22 M661,73.22 L661,96"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1.8369701987210297e-16,-1,1,-1.8369701987210297e-16,661,98)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray=""
                                                                            d="  M661,34 L661,64.22 M661,73.22 L661,96">
                                                                        </path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray="" d="  M541,84 L541,156"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1.8369701987210297e-16,-1,1,-1.8369701987210297e-16,541,158)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray="" d="  M541,84 L541,156">
                                                                        </path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray="" d="  M621,84 L621,156"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1.8369701987210297e-16,-1,1,-1.8369701987210297e-16,621,158)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray="" d="  M621,84 L621,156">
                                                                        </path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray="" d="  M674.5,21 L710,21"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,712,21.000000000000004)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray="" d="  M674.5,21 L710,21">
                                                                        </path>
                                                                    </g>
                                                                </g>
                                                                <g class="connection-group">
                                                                    <g class="arrow-line">
                                                                        <path class="connection real"
                                                                            stroke-dasharray="" d="  M634.5,71 L690,71"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1; fill: none; fill-opacity: 1;">
                                                                        </path>
                                                                        <g stroke="#000"
                                                                            transform="matrix(-1,1.2246467991473532e-16,-1.2246467991473532e-16,-1,692,71)"
                                                                            style="stroke: rgb(0, 0, 0); stroke-width: 1;">
                                                                            <path
                                                                                d=" M10.93,-3.29 Q4.96,-0.45 0,0 Q4.96,0.45 10.93,3.29">
                                                                            </path>
                                                                        </g>
                                                                        <path class="connection transparent no-print"
                                                                            stroke-dasharray="" d="  M634.5,71 L690,71">
                                                                        </path>
                                                                    </g>
                                                                </g>
                                                            </g>
                                                            <g></g>
                                                            <g></g>
                                                        </svg><diagram-editors><position-container
                                                                data-rotation="333.43" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 339px; top: 84px; transform: rotate(333.43deg);"><dg-editor-container
                                                                    data-editor-id="de40753336178939925"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="color: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke: rgb(74, 144, 226); border-color: rgb(74, 144, 226); font-size: 0.8em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">𝛼</block>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 264px; top: 19px;"><dg-editor-container
                                                                    data-editor-id="de4940768439831391"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="color: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke: rgb(74, 144, 226); border-color: rgb(74, 144, 226); font-size: 0.8em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">C</block>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 273px; top: 114px;"><dg-editor-container
                                                                    data-editor-id="de48180420194480833"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="color: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke: rgb(74, 144, 226); border-color: rgb(74, 144, 226); font-size: 0.8em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">D</block>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 320px; top: 114px;"><dg-editor-container
                                                                    data-editor-id="de5807521818501633"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="color: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke: rgb(74, 144, 226); border-color: rgb(74, 144, 226); font-size: 0.8em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">A</block>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 429px; top: 112px;"><dg-editor-container
                                                                    data-editor-id="de7146263565938351"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="color: rgb(74, 144, 226); fill: rgb(74, 144, 226); stroke: rgb(74, 144, 226); border-color: rgb(74, 144, 226); font-size: 0.8em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">B</block>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 451px; top: 71px;"><dg-editor-container
                                                                    data-editor-id="de7544254819417469"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">A</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container inline">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 501px; top: 21px;"><dg-editor-container
                                                                    data-editor-id="de2903282963872742"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">A</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container inline">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">2</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 451px; top: 171px;"><dg-editor-container
                                                                    data-editor-id="de2802837802260174"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">A'</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container left-overlaped inline"
                                                                                style="margin-left: -0.25em; min-width: 0.35em;">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 581px; top: 21px;"><dg-editor-container
                                                                    data-editor-id="de9638003082365285"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">B</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container inline">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">2</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 661px; top: 21px;"><dg-editor-container
                                                                    data-editor-id="de5159081343516227"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">C</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container inline">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">2</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 541px; top: 71px;"><dg-editor-container
                                                                    data-editor-id="de36919225102402753"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">B</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container inline">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 621px; top: 71px;"><dg-editor-container
                                                                    data-editor-id="de8713598212670945"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">C</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container inline">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 501px; top: 111px;"><dg-editor-container
                                                                    data-editor-id="de3038709971746514"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">A'</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container left-overlaped inline"
                                                                                style="margin-left: -0.25em; min-width: 0.35em;">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">2</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 581px; top: 111px;"><dg-editor-container
                                                                    data-editor-id="de7756384657811661"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">B'</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container left-overlaped inline"
                                                                                style="margin-left: -0.25em; min-width: 0.35em;">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">2</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 661px; top: 111px;"><dg-editor-container
                                                                    data-editor-id="de12189963952249894"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">C'</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container left-overlaped inline"
                                                                                style="margin-left: -0.25em; min-width: 0.35em;">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">2</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 541px; top: 171px;"><dg-editor-container
                                                                    data-editor-id="de6425366086475355"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">B'</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container left-overlaped inline"
                                                                                style="margin-left: -0.25em; min-width: 0.35em;">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 621px; top: 171px;"><dg-editor-container
                                                                    data-editor-id="de8876514283583856"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">C'</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container left-overlaped inline"
                                                                                style="margin-left: -0.25em; min-width: 0.35em;">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 721px; top: 21px;"><dg-editor-container
                                                                    data-editor-id="de9110694616210202"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">0</block>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="0" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 701px; top: 71px;"><dg-editor-container
                                                                    data-editor-id="de44980983766622673"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">0</block>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="30.96" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 81px; top: 53px; transform: rotate(30.96deg);"><dg-editor-container
                                                                    data-editor-id="de5336883053607893"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="color: rgb(146, 29, 130); fill: rgb(146, 29, 130); stroke: rgb(146, 29, 130); border-color: rgb(146, 29, 130); font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">A</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container inline">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">1</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="30.96" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 264px; top: 155px; transform: rotate(30.96deg);"><dg-editor-container
                                                                    data-editor-id="de044096105157812815"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="color: rgb(145, 25, 123); fill: rgb(145, 25, 123); stroke: rgb(145, 25, 123); border-color: rgb(145, 25, 123); font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">A</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container inline">
                                                                                <middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base><editarea-block
                                                                                    class="index-value"
                                                                                    style="line-height: 1.2; margin-top: -0.747899em;">2</editarea-block>
                                                                            </compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="14.47" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 29px; top: 197px; transform: rotate(14.47deg);"><dg-editor-container
                                                                    data-editor-id="de37325629701925944"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="color: rgb(36, 114, 18); fill: rgb(36, 114, 18); stroke: rgb(36, 114, 18); border-color: rgb(36, 114, 18); font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">y</block>
                                                                            <block class="Relation">=</block>
                                                                            <block class="Normal">x</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container has-power inline">
                                                                                <editarea-block class="power-value"
                                                                                    style="line-height: 1.2; margin-bottom: -1.06571em;">3</editarea-block><middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base></compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="344.74" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 160px; top: 46px; transform: rotate(344.74deg);"><dg-editor-container
                                                                    data-editor-id="de8490012245987402"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="color: rgb(179, 35, 24); fill: rgb(179, 35, 24); stroke: rgb(179, 35, 24); border-color: rgb(179, 35, 24); font-size: 1em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">y</block>
                                                                            <block class="Relation">=</block>
                                                                            <block class="Normal">x</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="power-index-symbol-container has-power inline">
                                                                                <editarea-block class="power-value"
                                                                                    style="line-height: 1.2; margin-bottom: -1.06571em;">2</editarea-block><middle-base>
                                                                                    <inline></inline>
                                                                                </middle-base></compositeblock>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container><position-container
                                                                data-rotation="22.93" data-skewx="0" data-flipx="false"
                                                                data-anchor="center"
                                                                style="left: 181px; top: 92px; transform: rotate(22.93deg);"><dg-editor-container
                                                                    data-editor-id="de029998639934706972"
                                                                    class="no-border-on-print math-diagram-editor"
                                                                    style="border: 1px solid transparent; transform: translate(-50%, -50%); font-size: 17px; font-family: Asana-Math, Asana;">
                                                                    <editarea
                                                                        class="diagram-editor non-wrap lazyable no-area-container"
                                                                        style="font-size: 0.8em;">
                                                                        <line class="" style="line-height: 1.2;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">d</block>
                                                                        </line>
                                                                    </editarea>
                                                                    <coverlayer class="no-print"></coverlayer>
                                                                </dg-editor-container></position-container></diagram-editors>
                                                    </zoom-region><svg
                                                        style="position: absolute; width: 100%; height: 100%; overflow: visible; left: 0px; top: 0px; pointer-events: none;"></svg></clip-region><svg
                                                    class="no-print"
                                                    style="pointer-events: none; position: absolute; top: 0px; left: 0px; overflow: visible; width: 100%; height: 100%;">
                                                    <g class="controls-drawing no-print" style="pointer-events: none;">
                                                    </g>
                                                    <g></g>
                                                </svg></math-diagram></compositeblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <emptyblock aria-label="empty line"> </emptyblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <block>Brackets</block>
                                    </blocks>
                                </line>
                                <line class="root text-mode full-line-block-inside" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <compositeblock dir="ltr"
                                            class="math-container-symbol role-mathmode-area display"
                                            style="font-size: 17px;">
                                            <editarea class="math-mode-font lazyable no-area-container"
                                                style="font-family: Asana-Math, Asana;">
                                                <line class="taggable" style="line-height: 1.2;">
                                                    <baselineblock></baselineblock><ref-tag data-tag-position="line"
                                                        class="line-tag automatic" data-tag-id="tid0.4860300158435781"
                                                        style="font-family: Arial, Helvetica, sans-serif; font-size: 15px;"><label>(0.1)</label></ref-tag>
                                                    <opensymbolblock class="" type="parenthesis"
                                                        style="height: 2.94118em;"><hidden-span
                                                            style="margin-top: 0.764706em; align-self: flex-start;">(</hidden-span><svg
                                                            style="width: 0.5em;">
                                                            <path
                                                                d=" M 6.27 1.70 c 0.00 -0.06 -0.04 -0.08 -0.08 -0.08 c -0.03 0.00 -0.06 0.01 -0.08 0.02 c -2.55 1.34 -4.65 3.73 -4.65 9.40 v 0.60 h 1.53 v -0.60 c 0.00 -3.23 0.64 -7.03 3.31 -9.06 c 0.12 -0.09 0.13 -0.19 0.13 -0.28 z   M 1.44 11.64 v 26.72 h 1.53 v -26.72 z"
                                                                stroke="none"></path>
                                                            <path
                                                                d=" M 6.27 48.30 c 0.00 0.06 -0.04 0.08 -0.08 0.08 c -0.03 0.00 -0.06 -0.01 -0.08 -0.02 c -2.55 -1.34 -4.65 -3.73 -4.65 -9.40 v -0.60 h 1.53 v 0.60 c 0.00 3.23 0.64 7.03 3.31 9.06 c 0.12 0.09 0.13 0.19 0.13 0.28 z"
                                                                stroke="none"></path>
                                                        </svg></opensymbolblock>
                                                    <compositeblock dir="ltr" class="fraction-symbol smaller">
                                                        <editarea-line class=" frac-edit-area enumerator"
                                                            style="line-height: 1.2; margin-bottom: -0.470588em;">
                                                            <baselineblock></baselineblock>
                                                            <block class="Normal">xdx</block>
                                                        </editarea-line>
                                                        <div class="frac-line">
                                                            <inline style="border-bottom: 1px solid;"></inline>
                                                        </div><editarea-line class=" frac-edit-area denominator"
                                                            style="line-height: 1.2; margin-top: -0.411765em;">
                                                            <baselineblock></baselineblock>
                                                            <block class="Normal">dy</block>
                                                        </editarea-line>
                                                    </compositeblock>
                                                    <block class="Binary">-</block>
                                                    <compositeblock dir="ltr" class="fraction-symbol smaller">
                                                        <editarea-line class=" frac-edit-area enumerator"
                                                            style="line-height: 1.2; margin-bottom: -0.470588em;">
                                                            <baselineblock></baselineblock>
                                                            <block class="Normal">ydy</block>
                                                        </editarea-line>
                                                        <div class="frac-line">
                                                            <inline style="border-bottom: 1px solid;"></inline>
                                                        </div><editarea-line class=" frac-edit-area denominator"
                                                            style="margin-bottom: -0.176471em; line-height: 1.2; margin-top: -0.411765em;">
                                                            <baselineblock></baselineblock>
                                                            <block class="Normal">dx</block>
                                                        </editarea-line>
                                                    </compositeblock>
                                                    <closesymbolblock class="" type="parenthesis"
                                                        style="height: 2.94118em;"><hidden-span
                                                            style="margin-top: 0.764706em; align-self: flex-start;">)</hidden-span><svg
                                                            style="width: 0.5em;">
                                                            <path
                                                                d=" M 1.75 1.70 c 0.00 -0.06 0.04 -0.08 0.08 -0.08 c 0.03 0.00 0.06 0.01 0.08 0.02 c 2.55 1.34 4.65 3.73 4.65 9.40 v 0.60 h -1.53 v -0.60 c 0.00 -3.23 -0.64 -7.03 -3.31 -9.06 c -0.12 -0.09 -0.13 -0.19 -0.13 -0.28 z  M 6.58 11.64 v 26.72 h -1.53 v -26.72 z"
                                                                stroke="none"></path>
                                                            <path
                                                                d=" M 1.75 48.30 c 0.00 0.06 0.04 0.08 0.08 0.08 c 0.03 0.00 0.06 -0.01 0.08 -0.02 c 2.55 -1.34 4.65 -3.73 4.65 -9.40 v -0.60 h -1.53 v 0.60 c 0.00 3.23 -0.64 7.03 -3.31 9.06 c -0.12 0.09 -0.13 0.19 -0.13 0.28 z"
                                                                stroke="none"></path>
                                                        </svg></closesymbolblock>
                                                    <compositeblock dir="ltr"
                                                        class="power-index-symbol-container has-power"
                                                        style="margin-left: -0.1em;"><editarea-block class="power-value"
                                                            style="line-height: 1.2; margin-bottom: 0.439497em;">2</editarea-block><middle-base>
                                                            <inline></inline>
                                                        </middle-base></compositeblock>
                                                    <block class="Normal"> </block>
                                                    <block class="Punctuation">,</block>
                                                    <block class="Normal"> </block>
                                                    <opensymbolblock class="normal" type="bracket">
                                                        <bracket-span>[</bracket-span></opensymbolblock>
                                                    <compositeblock dir="ltr" class="over-arrow-symbol ig-ex-br">
                                                        <arrow-symbol
                                                            style="height: 0.588235em; margin-bottom: -0.235294em;"><svg
                                                                style="position: absolute; left: 0px; top: 0px; width: 100%; height: 100%;">
                                                                <path
                                                                    d=" M 10.29 4.85 l -3.15 -3.32 l -0.49 0.52 l 1.93 2.33 h -0.15 v 0.90 h 0.15 l -1.93 2.33 l 0.49 0.52 z  M 1.68 5.23 h 8.15 v -0.90 h -8.15 z"
                                                                    stroke="none"></path>
                                                            </svg></arrow-symbol><editarea-block class=""
                                                            style="line-height: 1.2;">F</editarea-block>
                                                    </compositeblock>
                                                    <block class="Relation">=</block>
                                                    <block class="Normal">m</block>
                                                    <compositeblock dir="ltr" class="over-arrow-symbol ig-ex-br">
                                                        <arrow-symbol
                                                            style="height: 0.588235em; margin-bottom: -0.470588em;"><svg
                                                                style="position: absolute; left: 0px; top: 0px; width: 100%; height: 100%;">
                                                                <path
                                                                    d=" M 9.35 4.85 l -3.15 -3.32 l -0.49 0.52 l 1.93 2.33 h -0.15 v 0.90 h 0.15 l -1.93 2.33 l 0.49 0.52 z  M 1.68 5.23 h 7.21 v -0.90 h -7.21 z"
                                                                    stroke="none"></path>
                                                            </svg></arrow-symbol><editarea-block class=""
                                                            style="line-height: 1.2;">a</editarea-block>
                                                    </compositeblock>
                                                    <closesymbolblock class="normal" type="bracket">
                                                        <bracket-span>]</bracket-span></closesymbolblock>
                                                    <block class="Normal"> </block>
                                                    <block class="Punctuation">,</block>
                                                    <block class="Normal"> </block>
                                                    <opensymbolblock class="" type="vert" style="height: 2.52941em;">
                                                        <hidden-span
                                                            style="margin-top: 0.529412em; align-self: flex-start;">|</hidden-span><svg>
                                                            <rect x="2.90" y="1.89" width="1" height="39.22"
                                                                stroke="none"></rect>
                                                        </svg></opensymbolblock>
                                                    <compositeblock dir="ltr" class="fraction-symbol smaller">
                                                        <editarea-line class=" frac-edit-area enumerator"
                                                            style="margin-top: -0.235294em; line-height: 1.2; margin-bottom: -0.470588em;">
                                                            <baselineblock></baselineblock>
                                                            <block class="Normal">a</block>
                                                        </editarea-line>
                                                        <div class="frac-line">
                                                            <inline style="border-bottom: 1px solid;"></inline>
                                                        </div><editarea-line class=" frac-edit-area denominator"
                                                            style="margin-bottom: -0.176471em; line-height: 1.2; margin-top: -0.411765em;">
                                                            <baselineblock></baselineblock>
                                                            <block class="Normal">b</block>
                                                        </editarea-line>
                                                    </compositeblock>
                                                    <closesymbolblock class="" type="vert" style="height: 2.52941em;">
                                                        <hidden-span
                                                            style="margin-top: 0.529412em; align-self: flex-start;">|</hidden-span><svg>
                                                            <rect x="2.90" y="1.89" width="1" height="39.22"
                                                                stroke="none"></rect>
                                                        </svg></closesymbolblock>
                                                    <block class="Normal"> </block>
                                                    <opensymbolblock class="" type="Vert" style="height: 2.52941em;">
                                                        <hidden-span
                                                            style="margin-top: 0.529412em; align-self: flex-start;">‖</hidden-span><vcomposed-symbol
                                                            class="open-Vert non-safari"
                                                            style="font-family: Asana-Math, Asana; font-style: normal; font-weight: normal; line-height: 1.2; font-size: 1em;">
                                                            <middle>
                                                                <fixed>
                                                                    <inside>⏰</inside>
                                                                    <inside>⏰</inside>
                                                                    <inside>⏰</inside>
                                                                </fixed>
                                                            </middle>
                                                        </vcomposed-symbol></opensymbolblock>
                                                    <compositeblock dir="ltr" class="fraction-symbol smaller">
                                                        <editarea-line class=" frac-edit-area enumerator"
                                                            style="margin-top: -0.235294em; line-height: 1.2; margin-bottom: -0.470588em;">
                                                            <baselineblock></baselineblock>
                                                            <block class="Normal">a</block>
                                                        </editarea-line>
                                                        <div class="frac-line">
                                                            <inline style="border-bottom: 1px solid;"></inline>
                                                        </div><editarea-line class=" frac-edit-area denominator"
                                                            style="margin-bottom: -0.176471em; line-height: 1.2; margin-top: -0.411765em;">
                                                            <baselineblock></baselineblock>
                                                            <block class="Normal">b</block>
                                                        </editarea-line>
                                                    </compositeblock>
                                                    <closesymbolblock class="" type="Vert" style="height: 2.52941em;">
                                                        <hidden-span
                                                            style="margin-top: 0.529412em; align-self: flex-start;">‖</hidden-span><vcomposed-symbol
                                                            class="close-Vert non-safari"
                                                            style="font-family: Asana-Math, Asana; font-style: normal; font-weight: normal; line-height: 1.2; font-size: 1em;">
                                                            <middle>
                                                                <fixed>
                                                                    <inside>⏰</inside>
                                                                    <inside>⏰</inside>
                                                                    <inside>⏰</inside>
                                                                </fixed>
                                                            </middle>
                                                        </vcomposed-symbol></closesymbolblock>
                                                    <block class="Normal"> </block>
                                                    <opensymbolblock class="" type="angle" style="height: 2.52941em;">
                                                        <hidden-span
                                                            style="margin-top: 0.529412em; align-self: flex-start;">&lt;</hidden-span><svg
                                                            style="overflow: visible;">
                                                            <polyline points="10.20,1.89 0.94,21.50 10.20,41.11"
                                                                stroke-width="1" fill="none"></polyline>
                                                        </svg></opensymbolblock>
                                                    <compositeblock dir="ltr" class="fraction-symbol smaller">
                                                        <editarea-line class=" frac-edit-area enumerator"
                                                            style="margin-top: -0.235294em; line-height: 1.2; margin-bottom: -0.470588em;">
                                                            <baselineblock></baselineblock>
                                                            <block class="Normal">a</block>
                                                        </editarea-line>
                                                        <div class="frac-line">
                                                            <inline style="border-bottom: 1px solid;"></inline>
                                                        </div><editarea-line class=" frac-edit-area denominator"
                                                            style="margin-bottom: -0.176471em; line-height: 1.2; margin-top: -0.411765em;">
                                                            <baselineblock></baselineblock>
                                                            <block class="Normal">b</block>
                                                        </editarea-line>
                                                    </compositeblock>
                                                    <closesymbolblock class="" type="angle" style="height: 2.52941em;">
                                                        <hidden-span
                                                            style="margin-top: 0.529412em; align-self: flex-start;">&gt;</hidden-span><svg
                                                            style="overflow: visible;">
                                                            <polyline points="0.00,1.89 9.26,21.50 0.00,41.11"
                                                                stroke-width="1" fill="none"></polyline>
                                                        </svg></closesymbolblock>
                                                    <opensymbolblock class="" type="brace" style="height: 3.58824em;">
                                                        <hidden-span style="margin-top: -0.2em;">{</hidden-span><svg
                                                            style="width: 0.5em;">
                                                            <path
                                                                d=" M 8.35 1.70 c 0.00 -0.03 -0.05 -0.06 -0.12 -0.06 c -0.15 0.00 -0.44 0.09 -0.51 0.11 c -3.03 1.08 -3.59 3.33 -3.59 7.41 v 0.60 h 1.62 v -1.28 c 0.00 -2.20 0.12 -5.16 2.13 -6.48 c 0.15 -0.10 0.48 -0.22 0.48 -0.31 z  M 4.13 9.17 v 17.48 h 1.62 v -17.48 z  M 5.75 26.65 h -1.62 v 0.59 c 0.00 2.97 -2.74 3.66 -2.75 3.66 v 0.02 c 0.02 0.00 2.75 0.75 2.75 3.71 h 1.62 c 0.00 -1.05 -0.09 -2.87 -2.96 -3.69 c 2.87 -0.80 2.96 -2.63 2.96 -3.69 v -0.59 z  M 4.13 34.35 v 17.48 h 1.62 v -17.48 z"
                                                                stroke="none"></path>
                                                            <path
                                                                d=" M 8.35 59.30 c 0.00 0.03 -0.05 0.06 -0.12 0.06 c -0.15 0.00 -0.44 -0.09 -0.51 -0.11 c -3.03 -1.08 -3.59 -3.33 -3.59 -7.41 v -0.60 h 1.62 v 1.28 c 0.00 2.20 0.12 5.16 2.13 6.48 c 0.15 0.10 0.48 0.22 0.48 0.31 z"
                                                                stroke="none"></path>
                                                        </svg></opensymbolblock>
                                                    <compositeblock dir="ltr" class="sqrt-symbol"
                                                        style="padding-top: 0.235294em;"><sqrt-edit><editarea-line
                                                                class=" edit-area"
                                                                style="line-height: 1.2; margin-top: 0.117647em;">
                                                                <baselineblock></baselineblock>
                                                                <block class="Normal">a</block>
                                                                <block class="Binary">+</block>
                                                                <compositeblock dir="ltr" class="sqrt-symbol inline"
                                                                    style="padding-top: 0.235294em;">
                                                                    <sqrt-edit><editarea-line class=" edit-area"
                                                                            style="line-height: 1.2; margin-top: 0.117647em;">
                                                                            <baselineblock></baselineblock>
                                                                            <block class="Normal">a</block>
                                                                            <block class="Binary">+</block>
                                                                            <compositeblock dir="ltr"
                                                                                class="sqrt-symbol inline"
                                                                                style="padding-top: 0.235294em;">
                                                                                <sqrt-edit><editarea-line
                                                                                        class=" edit-area"
                                                                                        style="line-height: 1.2; margin-top: 0.117647em;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Normal">a</block>
                                                                                    </editarea-line><sqrt-symbol-line><svg
                                                                                            style="stroke: none;">
                                                                                            <polygon
                                                                                                points="0.35,10.29 4.79,7.92 9.66,18.24 16.36,0.00 25.50,0.00 25.50,1.05 17.09,1.05 9.18,22.59 3.10,9.85 0.64,10.77">
                                                                                            </polygon>
                                                                                        </svg></sqrt-symbol-line></sqrt-edit>
                                                                            </compositeblock>
                                                                        </editarea-line><sqrt-symbol-line><svg
                                                                                style="stroke: none;">
                                                                                <polygon
                                                                                    points="0.35,16.29 4.79,13.92 9.46,23.82 16.36,0.00 68.20,0.00 68.20,1.05 17.11,1.05 9.18,28.59 3.10,15.85 0.64,16.77">
                                                                                </polygon>
                                                                            </svg></sqrt-symbol-line></sqrt-edit>
                                                                </compositeblock>
                                                            </editarea-line><sqrt-symbol-line><svg
                                                                    style="stroke: none;">
                                                                    <polygon
                                                                        points="0.35,22.29 4.79,19.92 9.31,29.50 16.36,0.00 110.90,0.00 110.90,1.05 17.13,1.05 9.18,34.59 3.10,21.85 0.64,22.77">
                                                                    </polygon>
                                                                </svg></sqrt-symbol-line></sqrt-edit></compositeblock>
                                                    <compositeblock dir="ltr" class="arrow-like-symbol">
                                                        <arrow-like-simple><span>→</span></arrow-like-simple>
                                                    </compositeblock>
                                                    <block class="Normal">∞</block>
                                                    <closesymbolblock class="" type="brace" style="height: 3.58824em;">
                                                        <hidden-span style="margin-top: -0.2em;">}</hidden-span><svg
                                                            style="width: 0.5em;">
                                                            <path
                                                                d=" M 0.15 1.70 c 0.00 -0.03 0.05 -0.06 0.12 -0.06 c 0.15 0.00 0.44 0.09 0.51 0.11 c 3.03 1.08 3.59 3.33 3.59 7.41 v 0.60 h -1.62 v -1.28 c 0.00 -2.20 -0.12 -5.16 -2.13 -6.48 c -0.15 -0.10 -0.48 -0.22 -0.48 -0.31 z   M 4.37 9.17 v 17.48 h -1.62 v -17.48 z  M 2.75 26.65 h 1.62 v 0.59 c 0.00 2.97 2.74 3.66 2.75 3.66 v 0.02 c -0.02 0.00 -2.75 0.75 -2.75 3.71 h -1.62 c 0.00 -1.05 0.09 -2.87 2.96 -3.69 c -2.87 -0.80 -2.96 -2.63 -2.96 -3.69 v -0.59 z  M 4.37 34.35 v 17.48 h -1.62 v -17.48 z"
                                                                stroke="none"></path>
                                                            <path
                                                                d=" M 0.15 59.30 c 0.00 0.03 0.05 0.06 0.12 0.06 c 0.15 0.00 0.44 -0.09 0.51 -0.11 c 3.03 -1.08 3.59 -3.33 3.59 -7.41 v -0.60 h -1.62 v 1.28 c 0.00 2.20 -0.12 5.16 -2.13 6.48 c -0.15 0.10 -0.48 0.22 -0.48 0.31 z"
                                                                stroke="none"></path>
                                                        </svg></closesymbolblock>
                                                </line>
                                            </editarea>
                                        </compositeblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <emptyblock aria-label="empty line"> </emptyblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <block>Complex display</block>
                                    </blocks>
                                </line>
                                <line class="root text-mode align-center" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <emptyblock aria-label="empty line"> </emptyblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode full-line-block-inside" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <compositeblock dir="ltr"
                                            class="math-container-symbol role-mathmode-area display"
                                            style="font-size: 17px;">
                                            <editarea class="math-mode-font lazyable no-area-container"
                                                style="font-family: Asana-Math, Asana;">
                                                <line class="taggable" style="line-height: 1.2;">
                                                    <baselineblock></baselineblock>
                                                    <compositeblock dir="ltr"
                                                        class="matrix-symbol matrix-like matrix-math-mode role-tabular">
                                                        <base-line-indicator>a</base-line-indicator>
                                                        <matrix class="array">
                                                            <table>
                                                                <tbody>
                                                                    <tr class="">
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Normal">!</block>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="integral-like-symbol limit-type non-limit-kind inline">
                                                                                            <editarea-block
                                                                                                class="from from-int "
                                                                                                style="line-height: 1.2; justify-content: flex-start; margin-left: 1.16em; margin-bottom: -0.756303em; min-height: 1em; margin-top: 0em;">a</editarea-block>
                                                                                            <symbol class=""
                                                                                                style="font-family: Asana-Math, Asana; font-style: normal; font-weight: normal;">
                                                                                                <span>∫</span></symbol>
                                                                                            <editarea-block
                                                                                                class="to to-int"
                                                                                                style="line-height: 1.2; display: flex; justify-content: flex-end; flex-direction: column; margin-left: 0.69em; margin-top: -0.420168em; margin-bottom: 0em;">b</editarea-block>
                                                                                        </compositeblock>
                                                                                        <block class="Normal">f'</block>
                                                                                        <opensymbolblock class="normal"
                                                                                            type="parenthesis">
                                                                                            <bracket-span>(</bracket-span>
                                                                                        </opensymbolblock>
                                                                                        <block class="Normal">x</block>
                                                                                        <closesymbolblock class="normal"
                                                                                            type="parenthesis">
                                                                                            <bracket-span>)</bracket-span>
                                                                                        </closesymbolblock>
                                                                                        <block class="Normal">dx</block>
                                                                                        <block class="Relation">=
                                                                                        </block>
                                                                                        <block class="Normal">f</block>
                                                                                        <opensymbolblock class="normal"
                                                                                            type="parenthesis">
                                                                                            <bracket-span>(</bracket-span>
                                                                                        </opensymbolblock>
                                                                                        <block class="Normal">b</block>
                                                                                        <closesymbolblock class="normal"
                                                                                            type="parenthesis">
                                                                                            <bracket-span>)</bracket-span>
                                                                                        </closesymbolblock>
                                                                                        <block class="Binary">-</block>
                                                                                        <block class="Normal">f</block>
                                                                                        <opensymbolblock class="normal"
                                                                                            type="parenthesis">
                                                                                            <bracket-span>(</bracket-span>
                                                                                        </opensymbolblock>
                                                                                        <block class="Normal">a</block>
                                                                                        <closesymbolblock class="normal"
                                                                                            type="parenthesis">
                                                                                            <bracket-span>)</bracket-span>
                                                                                        </closesymbolblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="under-brace-symbol inline">
                                                                                            <editarea
                                                                                                class="value center no-area-container">
                                                                                                <line class=""
                                                                                                    style="line-height: 1.2;">
                                                                                                    <baselineblock>
                                                                                                    </baselineblock>
                                                                                                    <compositeblock
                                                                                                        dir="ltr"
                                                                                                        class="fraction-symbol smaller inline">
                                                                                                        <editarea-line
                                                                                                            class=" frac-edit-area enumerator"
                                                                                                            style="margin-bottom: -0.470588em; line-height: 1.2;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block
                                                                                                                class="Normal">
                                                                                                                1
                                                                                                            </block>
                                                                                                        </editarea-line>
                                                                                                        <div
                                                                                                            class="frac-line">
                                                                                                            <inline
                                                                                                                style="border-bottom: 1px solid;">
                                                                                                            </inline>
                                                                                                        </div>
                                                                                                        <editarea-line
                                                                                                            class=" frac-edit-area denominator"
                                                                                                            style="margin-bottom: -0.176471em; line-height: 1.2; margin-top: -0.411765em;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block
                                                                                                                class="Normal">
                                                                                                                4
                                                                                                            </block>
                                                                                                        </editarea-line>
                                                                                                    </compositeblock>
                                                                                                    <block
                                                                                                        class="Normal">W
                                                                                                    </block>
                                                                                                    <compositeblock
                                                                                                        dir="ltr"
                                                                                                        class="power-index-symbol-container inline">
                                                                                                        <middle-base>
                                                                                                            <inline>
                                                                                                            </inline>
                                                                                                        </middle-base><editarea-line
                                                                                                            class=" index-value"
                                                                                                            style="line-height: 1.2; margin-top: -0.747899em;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block>𝜇𝜈
                                                                                                            </block>
                                                                                                        </editarea-line>
                                                                                                    </compositeblock>
                                                                                                    <block
                                                                                                        class="Binary">⋅
                                                                                                    </block>
                                                                                                    <block
                                                                                                        class="Normal">W
                                                                                                    </block>
                                                                                                    <compositeblock
                                                                                                        dir="ltr"
                                                                                                        class="power-index-symbol-container has-power inline">
                                                                                                        <editarea-line
                                                                                                            class=" power-value"
                                                                                                            style="line-height: 1.2; margin-bottom: -1.06571em;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block>𝜇𝜈
                                                                                                            </block>
                                                                                                        </editarea-line><middle-base>
                                                                                                            <inline>
                                                                                                            </inline>
                                                                                                        </middle-base>
                                                                                                    </compositeblock>
                                                                                                    <block
                                                                                                        class="Binary">-
                                                                                                    </block>
                                                                                                    <compositeblock
                                                                                                        dir="ltr"
                                                                                                        class="fraction-symbol smaller inline">
                                                                                                        <editarea-line
                                                                                                            class=" frac-edit-area enumerator"
                                                                                                            style="margin-bottom: -0.470588em; line-height: 1.2;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block
                                                                                                                class="Normal">
                                                                                                                1
                                                                                                            </block>
                                                                                                        </editarea-line>
                                                                                                        <div
                                                                                                            class="frac-line">
                                                                                                            <inline
                                                                                                                style="border-bottom: 1px solid;">
                                                                                                            </inline>
                                                                                                        </div>
                                                                                                        <editarea-line
                                                                                                            class=" frac-edit-area denominator"
                                                                                                            style="margin-bottom: -0.176471em; line-height: 1.2; margin-top: -0.411765em;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block
                                                                                                                class="Normal">
                                                                                                                4
                                                                                                            </block>
                                                                                                        </editarea-line>
                                                                                                    </compositeblock>
                                                                                                    <block
                                                                                                        class="Normal">B
                                                                                                    </block>
                                                                                                    <compositeblock
                                                                                                        dir="ltr"
                                                                                                        class="power-index-symbol-container inline">
                                                                                                        <middle-base>
                                                                                                            <inline>
                                                                                                            </inline>
                                                                                                        </middle-base><editarea-line
                                                                                                            class=" index-value"
                                                                                                            style="line-height: 1.2; margin-top: -0.747899em;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block>𝜇𝜈
                                                                                                            </block>
                                                                                                        </editarea-line>
                                                                                                    </compositeblock>
                                                                                                    <block
                                                                                                        class="Normal">B
                                                                                                    </block>
                                                                                                    <compositeblock
                                                                                                        dir="ltr"
                                                                                                        class="power-index-symbol-container has-power inline">
                                                                                                        <editarea-line
                                                                                                            class=" power-value"
                                                                                                            style="line-height: 1.2; margin-bottom: -1.06571em;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block>𝜇𝜈
                                                                                                            </block>
                                                                                                        </editarea-line><middle-base>
                                                                                                            <inline>
                                                                                                            </inline>
                                                                                                        </middle-base>
                                                                                                    </compositeblock>
                                                                                                    <block
                                                                                                        class="Binary">-
                                                                                                    </block>
                                                                                                    <compositeblock
                                                                                                        dir="ltr"
                                                                                                        class="fraction-symbol smaller inline">
                                                                                                        <editarea-line
                                                                                                            class=" frac-edit-area enumerator"
                                                                                                            style="margin-bottom: -0.470588em; line-height: 1.2;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block
                                                                                                                class="Normal">
                                                                                                                1
                                                                                                            </block>
                                                                                                        </editarea-line>
                                                                                                        <div
                                                                                                            class="frac-line">
                                                                                                            <inline
                                                                                                                style="border-bottom: 1px solid;">
                                                                                                            </inline>
                                                                                                        </div>
                                                                                                        <editarea-line
                                                                                                            class=" frac-edit-area denominator"
                                                                                                            style="margin-bottom: -0.176471em; line-height: 1.2; margin-top: -0.411765em;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block
                                                                                                                class="Normal">
                                                                                                                4
                                                                                                            </block>
                                                                                                        </editarea-line>
                                                                                                    </compositeblock>
                                                                                                    <block
                                                                                                        class="Normal">G
                                                                                                    </block>
                                                                                                    <compositeblock
                                                                                                        dir="ltr"
                                                                                                        class="power-index-symbol-container has-power inline">
                                                                                                        <editarea-block
                                                                                                            class="power-value"
                                                                                                            style="line-height: 1.2; margin-top: 0em; margin-bottom: -1.05378em;">a</editarea-block><middle-base>
                                                                                                            <inline>
                                                                                                            </inline>
                                                                                                        </middle-base><editarea-line
                                                                                                            class=" index-value"
                                                                                                            style="margin-top: -0.693024em; line-height: 1.2;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block>𝜇𝜈
                                                                                                            </block>
                                                                                                        </editarea-line>
                                                                                                    </compositeblock>
                                                                                                    <block
                                                                                                        class="Normal">G
                                                                                                    </block>
                                                                                                    <compositeblock
                                                                                                        dir="ltr"
                                                                                                        class="power-index-symbol-container has-power inline">
                                                                                                        <editarea-line
                                                                                                            class=" power-value"
                                                                                                            style="line-height: 1.2; margin-bottom: -0.885714em;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block>𝜇𝜈
                                                                                                            </block>
                                                                                                        </editarea-line><middle-base>
                                                                                                            <inline>
                                                                                                            </inline>
                                                                                                        </middle-base><editarea-block
                                                                                                            class="index-value"
                                                                                                            style="line-height: 1.2; margin-top: -0.693024em; margin-bottom: -0.168067em;">a</editarea-block>
                                                                                                    </compositeblock>
                                                                                                </line>
                                                                                            </editarea><bottom-brace
                                                                                                style="height: 0.714706em;"><brace-top-wrapper
                                                                                                    style="height: 0.65em; font-family: Asana-Math, Asana; font-size: 1.1em; width: 13.5294em; font-style: normal;">
                                                                                                    <first>⏠</first>
                                                                                                    <middle-wrapper>
                                                                                                        <fixed>
                                                                                                            <middle-inside>⏣</middle-inside><middle-inside>⏣</middle-inside><middle-inside>⏣</middle-inside><middle-inside>⏣</middle-inside><middle-inside>⏣</middle-inside><middle-inside>⏣</middle-inside><middle-inside>⏣</middle-inside>
                                                                                                        </fixed>
                                                                                                    </middle-wrapper>
                                                                                                    <middle>⏡</middle>
                                                                                                    <middle-wrapper>
                                                                                                        <fixed>
                                                                                                            <middle-inside>⏣</middle-inside><middle-inside>⏣</middle-inside><middle-inside>⏣</middle-inside><middle-inside>⏣</middle-inside><middle-inside>⏣</middle-inside><middle-inside>⏣</middle-inside><middle-inside>⏣</middle-inside>
                                                                                                        </fixed>
                                                                                                    </middle-wrapper>
                                                                                                    <last>⏢</last>
                                                                                                </brace-top-wrapper></bottom-brace>
                                                                                            <editarea
                                                                                                class="underValue center no-area-container">
                                                                                                <line class=""
                                                                                                    style="line-height: 1.2;">
                                                                                                    <baselineblock>
                                                                                                    </baselineblock>
                                                                                                    <block
                                                                                                        style="font-family: Asana-Mathrm, Asana-Math, Asana;">
                                                                                                        kinetic energies
                                                                                                        and
                                                                                                        self-interactions
                                                                                                        of the gauge
                                                                                                        bosons</block>
                                                                                                </line>
                                                                                            </editarea>
                                                                                        </compositeblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print sbd-lc"></div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr class="">
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <emptyblock
                                                                                            aria-label="empty line">
                                                                                        </emptyblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <opensymbolblock class="normal"
                                                                                            type="Vert">
                                                                                            <hidden-span>‖</hidden-span><bracket-span>‖</bracket-span>
                                                                                        </opensymbolblock>
                                                                                        <block class="Normal">x</block>
                                                                                        <block class="Binary">+</block>
                                                                                        <block class="Normal">y</block>
                                                                                        <closesymbolblock class="normal"
                                                                                            type="Vert">
                                                                                            <hidden-span>‖</hidden-span><bracket-span>‖</bracket-span>
                                                                                        </closesymbolblock>
                                                                                        <block class="Relation">≥
                                                                                        </block>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="big-delimiter-symbol inline">
                                                                                            <base-line-indicator
                                                                                                style="margin-top: -0.2em;">a</base-line-indicator><big-delimiter
                                                                                                class="big-open"
                                                                                                style="height: 1.5em; width: 5px; font-family: Asana-Mathrm, Asana-Math, Asana; font-style: normal; font-weight: normal;"><svg>
                                                                                                    <rect x="2.00"
                                                                                                        y="1.89"
                                                                                                        width="1"
                                                                                                        height="21.72"
                                                                                                        stroke="none">
                                                                                                    </rect>
                                                                                                </svg></big-delimiter>
                                                                                        </compositeblock>
                                                                                        <opensymbolblock class="normal"
                                                                                            type="Vert">
                                                                                            <hidden-span>‖</hidden-span><bracket-span>‖</bracket-span>
                                                                                        </opensymbolblock>
                                                                                        <block class="Normal">x</block>
                                                                                        <closesymbolblock class="normal"
                                                                                            type="Vert">
                                                                                            <hidden-span>‖</hidden-span><bracket-span>‖</bracket-span>
                                                                                        </closesymbolblock>
                                                                                        <block class="Binary">-</block>
                                                                                        <opensymbolblock class="normal"
                                                                                            type="Vert">
                                                                                            <hidden-span>‖</hidden-span><bracket-span>‖</bracket-span>
                                                                                        </opensymbolblock>
                                                                                        <block class="Normal">y</block>
                                                                                        <closesymbolblock class="normal"
                                                                                            type="Vert">
                                                                                            <hidden-span>‖</hidden-span><bracket-span>‖</bracket-span>
                                                                                        </closesymbolblock>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="big-delimiter-symbol inline">
                                                                                            <base-line-indicator
                                                                                                style="margin-top: -0.2em;">a</base-line-indicator><big-delimiter
                                                                                                class="big-close"
                                                                                                style="height: 1.5em; width: 5px; font-family: Asana-Mathrm, Asana-Math, Asana; font-style: normal; font-weight: normal;"><svg>
                                                                                                    <rect x="2.00"
                                                                                                        y="1.89"
                                                                                                        width="1"
                                                                                                        height="21.72"
                                                                                                        stroke="none">
                                                                                                    </rect>
                                                                                                </svg></big-delimiter>
                                                                                        </compositeblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Unary">∇</block>
                                                                                        <block class="Binary">⋅</block>
                                                                                        <block class="Normal"
                                                                                            style="font-family: Asana-Mathrm, Asana-Math, Asana; font-weight: bold;">
                                                                                            D</block>
                                                                                        <block class="Relation">=
                                                                                        </block>
                                                                                        <block class="Normal">𝜌
                                                                                        </block>
                                                                                        <block class="Normal"
                                                                                            style="font-family: Asana-Mathrm, Asana-Math, Asana;">
                                                                                            and</block>
                                                                                        <block class="Normal"> </block>
                                                                                        <block class="Unary">∇</block>
                                                                                        <block class="Binary">⋅</block>
                                                                                        <block class="Normal"
                                                                                            style="font-family: Asana-Mathrm, Asana-Math, Asana; font-weight: bold;">
                                                                                            B</block>
                                                                                        <block class="Relation">=
                                                                                        </block>
                                                                                        <block class="Normal">0 </block>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print sbd-lc"></div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr class="">
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <emptyblock
                                                                                            aria-label="empty line">
                                                                                        </emptyblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <emptyblock
                                                                                            aria-label="empty line">
                                                                                        </emptyblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Unary">∇</block>
                                                                                        <block class="Binary">×</block>
                                                                                        <block class="Normal"
                                                                                            style="font-family: Asana-Mathrm, Asana-Math, Asana; font-weight: bold;">
                                                                                            E</block>
                                                                                        <block class="Relation">=
                                                                                        </block>
                                                                                        <block class="Binary">-</block>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="fraction-symbol smaller inline">
                                                                                            <editarea-line
                                                                                                class=" frac-edit-area enumerator"
                                                                                                style="line-height: 1.2; margin-bottom: -0.470588em;">
                                                                                                <baselineblock>
                                                                                                </baselineblock>
                                                                                                <block class="Normal">∂
                                                                                                </block>
                                                                                                <block class="Normal"
                                                                                                    style="font-family: Asana-Mathrm, Asana-Math, Asana; font-weight: bold;">
                                                                                                    B</block>
                                                                                            </editarea-line>
                                                                                            <div class="frac-line">
                                                                                                <inline
                                                                                                    style="border-bottom: 1px solid;">
                                                                                                </inline>
                                                                                            </div><editarea-line
                                                                                                class=" frac-edit-area denominator"
                                                                                                style="margin-bottom: -0.176471em; line-height: 1.2; margin-top: -0.411765em;">
                                                                                                <baselineblock>
                                                                                                </baselineblock>
                                                                                                <block class="Normal">∂t
                                                                                                </block>
                                                                                            </editarea-line>
                                                                                        </compositeblock>
                                                                                        <block class="Normal"> </block>
                                                                                        <block class="Normal"
                                                                                            style="font-family: Asana-Mathrm, Asana-Math, Asana;">
                                                                                            and</block>
                                                                                        <block class="Normal"> </block>
                                                                                        <block class="Unary">∇</block>
                                                                                        <block class="Binary">×</block>
                                                                                        <block class="Normal"
                                                                                            style="font-family: Asana-Mathrm, Asana-Math, Asana; font-weight: bold;">
                                                                                            H</block>
                                                                                        <block class="Relation">=
                                                                                        </block>
                                                                                        <block class="Normal"
                                                                                            style="font-family: Asana-Mathrm, Asana-Math, Asana; font-weight: bold;">
                                                                                            J</block>
                                                                                        <block class="Binary">+</block>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="fraction-symbol smaller inline">
                                                                                            <editarea-line
                                                                                                class=" frac-edit-area enumerator"
                                                                                                style="line-height: 1.2; margin-bottom: -0.470588em;">
                                                                                                <baselineblock>
                                                                                                </baselineblock>
                                                                                                <block class="Normal">∂
                                                                                                </block>
                                                                                                <block class="Normal"
                                                                                                    style="font-family: Asana-Mathrm, Asana-Math, Asana; font-weight: bold;">
                                                                                                    D</block>
                                                                                            </editarea-line>
                                                                                            <div class="frac-line">
                                                                                                <inline
                                                                                                    style="border-bottom: 1px solid;">
                                                                                                </inline>
                                                                                            </div><editarea-line
                                                                                                class=" frac-edit-area denominator"
                                                                                                style="margin-bottom: -0.176471em; line-height: 1.2; margin-top: -0.411765em;">
                                                                                                <baselineblock>
                                                                                                </baselineblock>
                                                                                                <block class="Normal">∂t
                                                                                                </block>
                                                                                            </editarea-line>
                                                                                        </compositeblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print sbd-lc"></div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr class="">
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <emptyblock
                                                                                            aria-label="empty line">
                                                                                        </emptyblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Normal">y</block>
                                                                                        <block class="Relation">=
                                                                                        </block>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="fraction-symbol smaller inline">
                                                                                            <editarea-line
                                                                                                class=" frac-edit-area enumerator"
                                                                                                style="line-height: 1.2; margin-bottom: -0.470588em;">
                                                                                                <baselineblock>
                                                                                                </baselineblock>
                                                                                                <compositeblock
                                                                                                    dir="ltr"
                                                                                                    class="summation-like-symbol limit-type limit-kind inline">
                                                                                                    <empty class="from">
                                                                                                        <editarea
                                                                                                            class="from no-area-container"
                                                                                                            style="margin-bottom: -0.176471em;">
                                                                                                            <line
                                                                                                                class=""
                                                                                                                style="line-height: 1.2;">
                                                                                                                <baselineblock>
                                                                                                                </baselineblock>
                                                                                                                <emptyblock
                                                                                                                    aria-label="empty line">
                                                                                                                </emptyblock>
                                                                                                            </line>
                                                                                                        </editarea>
                                                                                                    </empty>
                                                                                                    <symbol class=""
                                                                                                        style="font-family: Asana-Math, Asana; font-style: normal; font-weight: normal;">
                                                                                                        <span>∑</span>
                                                                                                    </symbol>
                                                                                                    <editarea
                                                                                                        class="to to-summation no-area-container"
                                                                                                        style="margin-top: 0.0588235em;">
                                                                                                        <line class=""
                                                                                                            style="line-height: 1.2;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block>i
                                                                                                            </block>
                                                                                                        </line>
                                                                                                    </editarea>
                                                                                                </compositeblock>
                                                                                                <block class="Normal">w
                                                                                                </block>
                                                                                                <compositeblock
                                                                                                    dir="ltr"
                                                                                                    class="power-index-symbol-container inline">
                                                                                                    <middle-base>
                                                                                                        <inline>
                                                                                                        </inline>
                                                                                                    </middle-base><editarea-block
                                                                                                        class="index-value"
                                                                                                        style="line-height: 1.2; margin-top: -0.747899em;">i</editarea-block>
                                                                                                </compositeblock>
                                                                                                <block class="Normal">y
                                                                                                </block>
                                                                                                <compositeblock
                                                                                                    dir="ltr"
                                                                                                    class="power-index-symbol-container inline">
                                                                                                    <middle-base>
                                                                                                        <inline>
                                                                                                        </inline>
                                                                                                    </middle-base><editarea-block
                                                                                                        class="index-value"
                                                                                                        style="line-height: 1.2; margin-top: -0.747899em;">i</editarea-block>
                                                                                                </compositeblock>
                                                                                            </editarea-line>
                                                                                            <div class="frac-line">
                                                                                                <inline
                                                                                                    style="border-bottom: 1px solid;">
                                                                                                </inline>
                                                                                            </div><editarea-line
                                                                                                class=" frac-edit-area denominator"
                                                                                                style="line-height: 1.2; margin-top: -0.411765em;">
                                                                                                <baselineblock>
                                                                                                </baselineblock>
                                                                                                <compositeblock
                                                                                                    dir="ltr"
                                                                                                    class="summation-like-symbol limit-type limit-kind inline">
                                                                                                    <empty class="from">
                                                                                                        <editarea
                                                                                                            class="from no-area-container"
                                                                                                            style="margin-bottom: -0.176471em;">
                                                                                                            <line
                                                                                                                class=""
                                                                                                                style="line-height: 1.2;">
                                                                                                                <baselineblock>
                                                                                                                </baselineblock>
                                                                                                                <emptyblock
                                                                                                                    aria-label="empty line">
                                                                                                                </emptyblock>
                                                                                                            </line>
                                                                                                        </editarea>
                                                                                                    </empty>
                                                                                                    <symbol class=""
                                                                                                        style="font-family: Asana-Math, Asana; font-style: normal; font-weight: normal;">
                                                                                                        <span>∑</span>
                                                                                                    </symbol>
                                                                                                    <editarea
                                                                                                        class="to to-summation no-area-container"
                                                                                                        style="margin-top: 0.0588235em;">
                                                                                                        <line class=""
                                                                                                            style="margin-bottom: -0.168067em; line-height: 1.2;">
                                                                                                            <baselineblock>
                                                                                                            </baselineblock>
                                                                                                            <block>i
                                                                                                            </block>
                                                                                                        </line>
                                                                                                    </editarea>
                                                                                                </compositeblock>
                                                                                                <block class="Normal">w
                                                                                                </block>
                                                                                                <compositeblock
                                                                                                    dir="ltr"
                                                                                                    class="power-index-symbol-container inline">
                                                                                                    <middle-base>
                                                                                                        <inline>
                                                                                                        </inline>
                                                                                                    </middle-base><editarea-block
                                                                                                        class="index-value"
                                                                                                        style="line-height: 1.2; margin-top: -0.747899em;">i</editarea-block>
                                                                                                </compositeblock>
                                                                                            </editarea-line>
                                                                                        </compositeblock>
                                                                                        <block class="Normal"> </block>
                                                                                        <block class="Punctuation">,
                                                                                        </block>
                                                                                        <block class="Normal">i</block>
                                                                                        <block class="Relation">=
                                                                                        </block>
                                                                                        <block class="Normal">1</block>
                                                                                        <block class="Punctuation">,
                                                                                        </block>
                                                                                        <block class="Punctuation">2...
                                                                                        </block>
                                                                                        <block class="Normal">k</block>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print"></div>
                                                                        </td>
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <block class="Normal">e</block>
                                                                                        <block class="Relation">=
                                                                                        </block>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="lim-like-symbol limit-type limit-kind close-left close-right inline">
                                                                                            <empty class="from">
                                                                                                <editarea
                                                                                                    class="from from-lim no-area-container"
                                                                                                    style="margin-bottom: -0.352941em;">
                                                                                                    <line class=""
                                                                                                        style="line-height: 1.2;">
                                                                                                        <baselineblock>
                                                                                                        </baselineblock>
                                                                                                        <emptyblock
                                                                                                            aria-label="empty line">
                                                                                                        </emptyblock>
                                                                                                    </line>
                                                                                                </editarea>
                                                                                            </empty>
                                                                                            <symbol class=""
                                                                                                style="font-family: Asana; font-style: normal;">
                                                                                                <span>lim</span>
                                                                                            </symbol>
                                                                                            <editarea
                                                                                                class="to to-lim no-area-container"
                                                                                                style="margin-top: -0.176471em;">
                                                                                                <line class=""
                                                                                                    style="line-height: 1.2;">
                                                                                                    <baselineblock>
                                                                                                    </baselineblock>
                                                                                                    <block>n</block>
                                                                                                    <compositeblock
                                                                                                        dir="ltr"
                                                                                                        class="arrow-like-symbol inline">
                                                                                                        <arrow-like-simple><span>→</span></arrow-like-simple>
                                                                                                    </compositeblock>
                                                                                                    <block>∞</block>
                                                                                                </line>
                                                                                            </editarea>
                                                                                        </compositeblock>
                                                                                        <opensymbolblock class=""
                                                                                            type="parenthesis"
                                                                                            style="height: 2.76471em;">
                                                                                            <hidden-span
                                                                                                style="margin-top: 0.764706em; align-self: flex-start;">(</hidden-span><svg
                                                                                                style="width: 0.5em;">
                                                                                                <path
                                                                                                    d=" M 6.27 1.70 c 0.00 -0.06 -0.04 -0.08 -0.08 -0.08 c -0.03 0.00 -0.06 0.01 -0.08 0.02 c -2.55 1.34 -4.65 3.73 -4.65 9.40 v 0.60 h 1.53 v -0.60 c 0.00 -3.23 0.64 -7.03 3.31 -9.06 c 0.12 -0.09 0.13 -0.19 0.13 -0.28 z   M 1.44 11.64 v 23.72 h 1.53 v -23.72 z"
                                                                                                    stroke="none">
                                                                                                </path>
                                                                                                <path
                                                                                                    d=" M 6.27 45.30 c 0.00 0.06 -0.04 0.08 -0.08 0.08 c -0.03 0.00 -0.06 -0.01 -0.08 -0.02 c -2.55 -1.34 -4.65 -3.73 -4.65 -9.40 v -0.60 h 1.53 v 0.60 c 0.00 3.23 0.64 7.03 3.31 9.06 c 0.12 0.09 0.13 0.19 0.13 0.28 z"
                                                                                                    stroke="none">
                                                                                                </path>
                                                                                            </svg></opensymbolblock>
                                                                                        <block class="Normal">1</block>
                                                                                        <block class="Binary">+</block>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="fraction-symbol smaller inline">
                                                                                            <editarea-line
                                                                                                class=" frac-edit-area enumerator"
                                                                                                style="line-height: 1.2; margin-bottom: -0.470588em;">
                                                                                                <baselineblock>
                                                                                                </baselineblock>
                                                                                                <block class="Normal">1
                                                                                                </block>
                                                                                            </editarea-line>
                                                                                            <div class="frac-line">
                                                                                                <inline
                                                                                                    style="border-bottom: 1px solid;">
                                                                                                </inline>
                                                                                            </div><editarea-line
                                                                                                class=" frac-edit-area denominator"
                                                                                                style="margin-bottom: -0.176471em; line-height: 1.2; margin-top: -0.411765em;">
                                                                                                <baselineblock>
                                                                                                </baselineblock>
                                                                                                <block class="Normal">n
                                                                                                </block>
                                                                                            </editarea-line>
                                                                                        </compositeblock>
                                                                                        <closesymbolblock class=""
                                                                                            type="parenthesis"
                                                                                            style="height: 2.76471em;">
                                                                                            <hidden-span
                                                                                                style="margin-top: 0.764706em; align-self: flex-start;">)</hidden-span><svg
                                                                                                style="width: 0.5em;">
                                                                                                <path
                                                                                                    d=" M 1.75 1.70 c 0.00 -0.06 0.04 -0.08 0.08 -0.08 c 0.03 0.00 0.06 0.01 0.08 0.02 c 2.55 1.34 4.65 3.73 4.65 9.40 v 0.60 h -1.53 v -0.60 c 0.00 -3.23 -0.64 -7.03 -3.31 -9.06 c -0.12 -0.09 -0.13 -0.19 -0.13 -0.28 z  M 6.58 11.64 v 23.72 h -1.53 v -23.72 z"
                                                                                                    stroke="none">
                                                                                                </path>
                                                                                                <path
                                                                                                    d=" M 1.75 45.30 c 0.00 0.06 0.04 0.08 0.08 0.08 c 0.03 0.00 0.06 -0.01 0.08 -0.02 c 2.55 -1.34 4.65 -3.73 4.65 -9.40 v -0.60 h -1.53 v 0.60 c 0.00 3.23 -0.64 7.03 -3.31 9.06 c -0.12 0.09 -0.13 0.19 -0.13 0.28 z"
                                                                                                    stroke="none">
                                                                                                </path>
                                                                                            </svg></closesymbolblock>
                                                                                        <compositeblock dir="ltr"
                                                                                            class="power-index-symbol-container has-power inline"
                                                                                            style="margin-left: -0.1em;">
                                                                                            <editarea-block
                                                                                                class="power-value"
                                                                                                style="line-height: 1.2; margin-bottom: 0.439492em;">n</editarea-block><middle-base>
                                                                                                <inline></inline>
                                                                                            </middle-base>
                                                                                        </compositeblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print sbd-lc"></div>
                                                                        </td>
                                                                    </tr>
                                                                    <tr class="">
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <emptyblock
                                                                                            aria-label="empty line">
                                                                                        </emptyblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print sbd-lr"></div>
                                                                        </td>
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <emptyblock
                                                                                            aria-label="empty line">
                                                                                        </emptyblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print sbd-lr"></div>
                                                                        </td>
                                                                        <td class="">
                                                                            <editarea
                                                                                class="matrix-item editor-cell center">
                                                                                <area-container>
                                                                                    <line class=""
                                                                                        style="line-height: 1.2;">
                                                                                        <baselineblock></baselineblock>
                                                                                        <emptyblock
                                                                                            aria-label="empty line">
                                                                                        </emptyblock>
                                                                                    </line>
                                                                                </area-container></editarea>
                                                                            <div class="sbd no-print sbd-lr sbd-lc">
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </matrix>
                                                    </compositeblock>
                                                </line>
                                                <line class="taggable" style="line-height: 1.2;">
                                                    <baselineblock></baselineblock>
                                                    <compositeblock dir="ltr" class="over-symbol ig-ex-br">
                                                        <symbol class="dot"
                                                            style="height: 0.411765em; margin-bottom: -0.411765em; font-style: normal;">
                                                            <inner class="">␒</inner>
                                                        </symbol>
                                                        <editarea class="center no-area-container"
                                                            style="display: inline-flex; width: 100%;">
                                                            <line class="" style="line-height: 1.2;">
                                                                <baselineblock></baselineblock>
                                                                <block class="Normal">x</block>
                                                            </line>
                                                        </editarea>
                                                    </compositeblock>
                                                    <compositeblock dir="ltr" class="power-index-symbol-container">
                                                        <middle-base>
                                                            <inline></inline>
                                                        </middle-base><editarea-block class="index-value"
                                                            style="line-height: 1.2; margin-top: -0.597483em;">i</editarea-block>
                                                    </compositeblock>
                                                    <block class="Relation">=</block>
                                                    <block class="Normal">a</block>
                                                    <compositeblock dir="ltr" class="power-index-symbol-container">
                                                        <middle-base>
                                                            <inline></inline>
                                                        </middle-base><editarea-block class="index-value"
                                                            style="line-height: 1.2; margin-top: -0.747899em;">i</editarea-block>
                                                    </compositeblock>
                                                    <block class="Normal">x</block>
                                                    <compositeblock dir="ltr" class="power-index-symbol-container">
                                                        <middle-base>
                                                            <inline></inline>
                                                        </middle-base><editarea-line class=" index-value"
                                                            style="line-height: 1.2; margin-top: -0.747899em;">
                                                            <baselineblock></baselineblock>
                                                            <block>i'</block>
                                                        </editarea-line></compositeblock>
                                                    <block class="Binary">-</block>
                                                    <opensymbolblock class="normal" type="parenthesis">
                                                        <bracket-span>(</bracket-span></opensymbolblock>
                                                    <block class="Normal">d</block>
                                                    <block class="Binary">+</block>
                                                    <block class="Normal">a</block>
                                                    <compositeblock dir="ltr" class="power-index-symbol-container">
                                                        <middle-base>
                                                            <inline></inline>
                                                        </middle-base><editarea-line class=" index-value"
                                                            style="line-height: 1.2; margin-top: -0.747899em;">
                                                            <baselineblock></baselineblock>
                                                            <block>i0</block>
                                                        </editarea-line></compositeblock>
                                                    <block class="Binary">+</block>
                                                    <block class="Normal">a</block>
                                                    <compositeblock dir="ltr" class="power-index-symbol-container">
                                                        <middle-base>
                                                            <inline></inline>
                                                        </middle-base><editarea-line class=" index-value"
                                                            style="line-height: 1.2; margin-top: -0.747899em;">
                                                            <baselineblock></baselineblock>
                                                            <block>i1</block>
                                                        </editarea-line></compositeblock>
                                                    <closesymbolblock class="normal" type="parenthesis">
                                                        <bracket-span>)</bracket-span></closesymbolblock>
                                                    <block class="Normal">x</block>
                                                    <compositeblock dir="ltr" class="power-index-symbol-container">
                                                        <middle-base>
                                                            <inline></inline>
                                                        </middle-base><editarea-block class="index-value"
                                                            style="line-height: 1.2; margin-top: -0.747899em;">i</editarea-block>
                                                    </compositeblock>
                                                    <block class="Binary">+</block>
                                                    <block class="Normal">rx</block>
                                                    <compositeblock dir="ltr" class="power-index-symbol-container">
                                                        <middle-base>
                                                            <inline></inline>
                                                        </middle-base><editarea-block class="index-value"
                                                            style="line-height: 1.2; margin-top: -0.747899em;">i</editarea-block>
                                                    </compositeblock>
                                                    <opensymbolblock class="normal" type="parenthesis">
                                                        <bracket-span>(</bracket-span></opensymbolblock>
                                                    <block class="Normal">f</block>
                                                    <compositeblock dir="ltr" class="power-index-symbol-container">
                                                        <middle-base>
                                                            <inline></inline>
                                                        </middle-base><editarea-block class="index-value"
                                                            style="line-height: 1.2; margin-top: -0.747899em;">i</editarea-block>
                                                    </compositeblock>
                                                    <block class="Binary">-</block>
                                                    <block class="Normal">𝜙</block>
                                                    <closesymbolblock class="normal" type="parenthesis">
                                                        <bracket-span>)</bracket-span></closesymbolblock>
                                                </line>
                                            </editarea>
                                        </compositeblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <emptyblock aria-label="empty line"> </emptyblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode selected full-line-block-inside" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <compositeblock dir="ltr"
                                            class="math-container-symbol role-mathmode-area display selected"
                                            style="font-size: 17px;">
                                            <editarea class="math-mode-font lazyable no-area-container"
                                                style="font-family: Asana-Math, Asana;">
                                                <line class="selected taggable" style="line-height: 1.2;">
                                                    <baselineblock></baselineblock><ref-tag class="empty-line-tag"
                                                        style="font-family: Arial, Helvetica, sans-serif; font-size: 15px;"><label>(...)</label></ref-tag>
                                                    <compositeblock dir="ltr" class="fraction-symbol smaller">
                                                        <editarea-line class=" frac-edit-area enumerator"
                                                            style="line-height: 1.2; margin-bottom: -0.470588em;">
                                                            <baselineblock></baselineblock>
                                                            <compositeblock dir="ltr" class="constant-text inline">
                                                                <constant-text class="close-left close-right"
                                                                    style="font-family: Asana; font-style: normal;">sin</constant-text>
                                                            </compositeblock>
                                                            <block class="Opening">(</block>
                                                            <block class="Normal">𝜃</block>
                                                            <block class="Binary">-</block>
                                                            <block class="Normal">5𝜋</block>
                                                            <block class="Closing">)</block>
                                                        </editarea-line>
                                                        <div class="frac-line">
                                                            <inline style="border-bottom: 1px solid;"></inline>
                                                        </div><editarea-line class=" frac-edit-area denominator"
                                                            style="line-height: 1.2; margin-top: -0.411765em;">
                                                            <baselineblock></baselineblock>
                                                            <compositeblock dir="ltr" class="constant-text inline">
                                                                <constant-text class="close-left close-right"
                                                                    style="font-family: Asana; font-style: normal; margin-bottom: -0.176471em;">tan</constant-text>
                                                            </compositeblock>
                                                            <block class="Opening">(</block>
                                                            <block class="Normal">3𝜋</block>
                                                            <block class="Binary">-</block>
                                                            <block class="Normal">𝜃</block>
                                                            <block class="Closing">)</block>
                                                        </editarea-line>
                                                    </compositeblock>
                                                    <block class="Binary">⋅</block>
                                                    <compositeblock dir="ltr" class="fraction-symbol smaller">
                                                        <editarea-line class=" frac-edit-area enumerator"
                                                            style="line-height: 1.2; margin-bottom: -0.470588em;">
                                                            <baselineblock></baselineblock>
                                                            <compositeblock dir="ltr" class="constant-text inline">
                                                                <constant-text class="close-left close-right"
                                                                    style="font-family: Asana; font-style: normal; margin-top: -0.235294em;">cot</constant-text>
                                                            </compositeblock>
                                                            <block class="Opening">(</block>
                                                            <compositeblock dir="ltr"
                                                                class="fraction-symbol smaller frac-inline inline">
                                                                <editarea-line class=" frac-edit-area enumerator"
                                                                    style="margin-top: -0.235294em; line-height: 1.2; margin-bottom: -0.411765em; padding-top: 0.0588235em;">
                                                                    <baselineblock></baselineblock>
                                                                    <block>𝜋</block>
                                                                </editarea-line>
                                                                <div class="frac-line">
                                                                    <inline
                                                                        style="vertical-align: 0.35em; border-bottom: 1px solid;">
                                                                    </inline>
                                                                </div><editarea-line class=" frac-edit-area denominator"
                                                                    style="margin-bottom: -0.0588235em; line-height: 1.2; margin-top: -0.588235em;">
                                                                    <baselineblock></baselineblock>
                                                                    <block>2</block>
                                                                </editarea-line>
                                                            </compositeblock>
                                                            <block class="Binary">-</block>
                                                            <block class="Normal">𝜃</block>
                                                            <block class="Closing">)</block>
                                                        </editarea-line>
                                                        <div class="frac-line">
                                                            <inline style="border-bottom: 1px solid;"></inline>
                                                        </div><editarea-line class=" frac-edit-area denominator"
                                                            style="line-height: 1.2; margin-top: -0.411765em;">
                                                            <baselineblock></baselineblock>
                                                            <compositeblock dir="ltr" class="constant-text inline">
                                                                <constant-text class="close-left close-right"
                                                                    style="font-family: Asana; font-style: normal; margin-bottom: -0.176471em;">tan</constant-text>
                                                            </compositeblock>
                                                            <block class="Opening">(</block>
                                                            <block class="Normal">𝜃</block>
                                                            <block class="Binary">-</block>
                                                            <compositeblock dir="ltr"
                                                                class="fraction-symbol smaller frac-inline inline">
                                                                <editarea-line class=" frac-edit-area enumerator"
                                                                    style="margin-bottom: -0.411765em; line-height: 1.2; padding-top: 0.0588235em;">
                                                                    <baselineblock></baselineblock>
                                                                    <block>3𝜋</block>
                                                                </editarea-line>
                                                                <div class="frac-line">
                                                                    <inline
                                                                        style="vertical-align: 0.35em; border-bottom: 1px solid;">
                                                                    </inline>
                                                                </div><editarea-line class=" frac-edit-area denominator"
                                                                    style="margin-bottom: -0.0588235em; line-height: 1.2; margin-top: -0.588235em;">
                                                                    <baselineblock></baselineblock>
                                                                    <block>2</block>
                                                                </editarea-line>
                                                            </compositeblock>
                                                            <block class="Closing">)</block>
                                                        </editarea-line>
                                                    </compositeblock>
                                                    <block class="Binary">⋅</block>
                                                    <compositeblock dir="ltr" class="fraction-symbol smaller">
                                                        <editarea-line class=" frac-edit-area enumerator"
                                                            style="line-height: 1.2; margin-bottom: -0.470588em;">
                                                            <baselineblock></baselineblock>
                                                            <compositeblock dir="ltr" class="constant-text inline">
                                                                <constant-text class="close-left close-right"
                                                                    style="font-family: Asana; font-style: normal; margin-top: -0.235294em;">cos</constant-text>
                                                            </compositeblock>
                                                            <block class="Opening">(</block>
                                                            <block class="Normal">8𝜋</block>
                                                            <block class="Binary">-</block>
                                                            <block class="Normal">𝜃</block>
                                                            <block class="Closing">)</block>
                                                        </editarea-line>
                                                        <div class="frac-line">
                                                            <inline style="border-bottom: 1px solid;"></inline>
                                                        </div><editarea-line class=" frac-edit-area denominator"
                                                            style="line-height: 1.2; margin-top: -0.411765em;">
                                                            <baselineblock></baselineblock>
                                                            <compositeblock dir="ltr" class="constant-text inline">
                                                                <constant-text class="close-left close-right"
                                                                    style="font-family: Asana; font-style: normal; margin-bottom: -0.176471em;">cos</constant-text>
                                                            </compositeblock>
                                                            <block class="Opening">(</block>
                                                            <block class="Normal">𝜃</block>
                                                            <block class="Binary">+</block>
                                                            <compositeblock dir="ltr"
                                                                class="fraction-symbol smaller frac-inline inline">
                                                                <editarea-line class=" frac-edit-area enumerator"
                                                                    style="margin-top: -0.235294em; margin-bottom: -0.411765em; line-height: 1.2; padding-top: 0.0588235em;">
                                                                    <baselineblock></baselineblock>
                                                                    <block>𝜋</block>
                                                                </editarea-line>
                                                                <div class="frac-line">
                                                                    <inline
                                                                        style="vertical-align: 0.35em; border-bottom: 1px solid;">
                                                                    </inline>
                                                                </div><editarea-line class=" frac-edit-area denominator"
                                                                    style="margin-bottom: -0.0588235em; line-height: 1.2; margin-top: -0.588235em;">
                                                                    <baselineblock></baselineblock>
                                                                    <block>2</block>
                                                                </editarea-line>
                                                            </compositeblock>
                                                            <block class="Closing">)</block>
                                                        </editarea-line>
                                                    </compositeblock>
                                                </line>
                                            </editarea>
                                        </compositeblock>
                                    </blocks>
                                </line>
                                <line class="root text-mode" style="line-height: 1.4;">
                                    <prefix></prefix>
                                    <blocks>
                                        <baselineblock class="inline"></baselineblock>
                                        <emptyblock aria-label="empty line"> </emptyblock>
                                    </blocks>
                                </line>
                            </area-container></editarea>
                        <div></div>
                    </math-edit-container><selection-wrapper class="inactive"></selection-wrapper><span
                        class="text-cursor"
                        style="height: 17px; transform: translate(212.25px, 1174.47px); border-color: rgb(0, 128, 0); opacity: 0.5;"></span><span
                        class="sub-text-cursor" style="visibility: hidden;"></span><composition-indicator
                        style="display: none;"></composition-indicator><region-highlight-container
                        style="z-index: 1;"></region-highlight-container>
                    <div></div>
                    <div style="display: none;"></div>
                </math-type><resize-bar
                    style="width: 2px; border-left: 1px solid rgb(222, 220, 220); cursor: col-resize; position: absolute; right: -1px; height: 100%; top: 0px;"></resize-bar></editor-container></content-area>
    </div>
    <div class="doc-items-bar-container"><items-bar class=""><item-container>
                <item title="Show Suggestion Box" class="trigger-item"><svg>
                        <path d="M0 4v11h16v-14h-16v3zM14 2h1v1h-1v-1zM1 4h14v10h-14v-10z" stroke="none"></path>
                    </svg><i class="fa fa-bars" aria-hidden="true"></i></item>
                <div title="Insert Diagram" data-amt="insert-diagram" class="disabled"><expandable-component
                        class="shape-selector" tabindex="0">
                        <div style="margin-left: 2px; padding: 3px 2px 2px;"><svg viewBox="0 0 512 512"
                                style="width: 17px; fill: none; stroke: lightgray; stroke-width: 30px; overflow: visible;">
                                <path
                                    d="M512 320v160c0 17.67-14.33 32-32 32H320c-17.67 0-32-14.33-32-32V320c0-17.67 14.33-32 32-32h160c17.67 0 32 14.33 32 32zm-384-64C57.31 256 0 313.31 0 384s57.31 128 128 128 128-57.31 128-128-57.31-128-128-128zm351.03-32c25.34 0 41.18-26.67 28.51-48L412.51 16c-12.67-21.33-44.35-21.33-57.02 0l-95.03 160c-12.67 21.33 3.17 48 28.51 48h190.06z">
                                </path>
                            </svg></div>
                    </expandable-component></div>
                <item title="Insert Table" class="table-item disabled"><svg
                        style="width: 19px; height: 15px; transform: translate(2px, 1px);">
                        <path d="M-0.5,0 L12.5,0 M-0.5,6 L12.5,6 M-0.5,12 L12.5,12 M0,0 L0,12 M6,0 L6,12 M12,0 L12,12"
                            fill="none" style="transform: translate(2px, 2px);"></path>
                    </svg></item>
                <item title="Insert Image" class="image-item disabled" style="padding: 4px 0px 0px 5px;"><svg
                        viewBox="0 0 512 512" style="width: 16px;">
                        <path
                            d="M464 64H48C21.49 64 0 85.49 0 112v288c0 26.51 21.49 48 48 48h416c26.51 0 48-21.49 48-48V112c0-26.51-21.49-48-48-48zm-6 336H54a6 6 0 0 1-6-6V118a6 6 0 0 1 6-6h404a6 6 0 0 1 6 6v276a6 6 0 0 1-6 6zM128 152c-22.091 0-40 17.909-40 40s17.909 40 40 40 40-17.909 40-40-17.909-40-40-40zM96 352h320v-80l-87.515-87.515c-4.686-4.686-12.284-4.686-16.971 0L192 304l-39.515-39.515c-4.686-4.686-12.284-4.686-16.971 0L96 304v48z">
                        </path>
                    </svg></item>
                <item title="Insert Checkbox" class="checkbox-item disabled" style="padding-bottom: 1px;"><svg
                        viewBox="0 0 24 24" style="width: 16px; height: 16px; margin-left: 2px; margin-top: 2px;">
                        <rect rx="2" x="1" y="1" width="22" height="21" fill="none" stroke-width="1.5"></rect>
                        <path transform="translate(4.5,4) scale(0.6)"
                            d="M9 21.035l-9-8.638 2.791-2.87 6.156 5.874 12.21-12.436 2.843 2.817z"></path>
                    </svg></item>
            </item-container><round-close-icon>
                <div
                    style="position: absolute; width: 24px; height: 15px; top: -6px; left: -2px; background: transparent;">
                </div><i class="fa fa-times" aria-hidden="true"></i>
            </round-close-icon></items-bar></div><document-info-container><document-info-bar style="bottom: 15px;">
            <div></div><span class="full-view-button" title="Content View Only (Alt+Shift+E)"><svg viewBox="0 0 24 24"
                    style="width: 20px; height: 20px; vertical-align: -6px; padding-right: 4px; padding-left: 4px; fill: gray; display: inline-block; cursor: pointer;">
                    <path fill="none" d="M0 0h24v24H0V0z"></path>
                    <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"></path>
                </svg></span>
            <div class="select-box-container" tabindex="998" style="display: inline-block; width: 50px;">
                <div data-for="select-box" class="input-container"><input-like readonly="true">100%</input-like>
                    <div class="arrow-down-icon "><i class="fa fa-caret-down" aria-hidden="true"></i></div>
                </div>
            </div><span><span><span><span
                            style="margin-left: 8px; display: inline-block; max-width: 160px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; vertical-align: text-top;"><!-- react-text: 583 -->Lang:
                            <!-- /react-text --><!-- react-text: 584 -->None<!-- /react-text --></span><span><!-- react-text: 586 -->,
                            Words:
                            <!-- /react-text --><!-- react-text: 587 -->27<!-- /react-text --><!-- react-text: 588 -->,
                            Math: <!-- /react-text --><!-- react-text: 589 -->7<!-- /react-text --></span></span><span
                        style="cursor: pointer; text-decoration: underline; padding-left: 10px; padding-right: 10px;">More</span></span></span>
        </document-info-bar>
        <div></div>
    </document-info-container>
</div>