<style type="text/css">/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
modal-dialog {
  font-family: "Segoe UI", Arial, Verdana, sans-serif;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: flex;
  overflow: auto;
}
modal-dialog > modal-container.mt-common-dialog {
  width: 700px;
  min-height: 300px;
  height: calc(100% - 100px);
  left: 50%;
  top: 60px;
  transform: translate(-50%, 0);
  position: absolute;
  display: flex;
  flex-direction: column;
  z-index: 20001;
  box-shadow: 1px 1px 1px 1px #888686;
}
modal-dialog > modal-container.mt-common-dialog {
  z-index: 20001;
}
modal-dialog modal-content {
  flex-grow: 1;
  display: flex;
  color: gray;
  min-height: 0px;
}
modal-dialog modal-header {
  min-height: 20px;
  /* border-bottom: 1px solid gray; */
  color: white;
  margin-left: -7px;
  margin-right: -7px;
  margin-top: -7px;
  padding-left: 10px;
  padding-top: 5px;
  font-size: 13px;
  background: #4CAF50;
  padding-right: 7px;
  margin-bottom: 10px;
  border-bottom: 1px #c1bfbf solid;
}
modal-dialog modal-header > cancel-button {
  float: right;
  color: white;
  margin-top: -6px;
  font-size: 14px;
  padding-top: 5px;
  cursor: pointer;
}
modal-dialog modal-header > cancel-button:hover {
  color: lightgray;
}
modal-dialog modal-footer {
  display: flex;
  align-items: center;
  min-height: 25px;
  flex-shrink: 0;
}
overlay {
  opacity: 0.3;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: black;
}
.modal-dialog-transparent {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-color: transparent;
}
/**end-ignore-save-as-html*/

compositeblock {
  display: inline-block;
  position: relative;
  text-align: left;
  unicode-bidi: isolate;
}
compositeblock.middle {
  vertical-align: middle;
}
latex-symbol-icon {
  display: inline-block;
  padding: 2px;
  border: 1px solid transparent;
  cursor: pointer;
}
latex-symbol-icon:hover,
latex-symbol-icon.selected {
  background: white;
  border: 1px solid lightgray;
}
inline {
  display: inline-block;
}
base-line-indicator {
  display: inline-block;
  overflow: hidden;
  width: 0px;
  height: inherit;
}

math-type .constant-text > constant-text {
  margin-left: 0.2em;
  margin-right: 0.2em;
}
math-type .constant-text > constant-text.close-left {
  margin-left: 0;
}
math-type .constant-text > constant-text.close-right {
  margin-right: 0;
}

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
hidden-input-wrapper {
  overflow: hidden;
  position: absolute;
  width: 0px;
  height: 0px;
  top: 0px;
  left: 0px;
  display: block;
  transform: scale(0);
  -webkit-user-select: none;
  /* disable selection/Copy of UIWebView */
  -webkit-touch-callout: none;
  /* disable the IOS popup when long-press on a link */
  user-select: none;
}
hidden-input-wrapper > textarea {
  position: absolute;
  width: 1000px;
  height: 1em;
  font-size: 30px;
}
/**end-ignore-save-as-html*/

/**different context*/
/**different context*/
math-type ref-tag {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translate(0, -50%);
  z-index: 8;
  cursor: pointer;
}
math-type ref-tag > label {
  cursor: pointer;
}
math-type ref-tag:hover {
  text-decoration: underline;
}
math-type ref-tag.line-tag {
  transition: background 0.4s;
}
math-type ref-tag.empty-line-tag {
  opacity: 0.5;
  animation: fadein 0.5s;
}
math-type tr.math-row ref-tag {
  left: calc(100% + 20px);
  right: unset;
}
@keyframes fadein {
  0% {
    opacity: 0;
  }
  70% {
    opacity: 0;
  }
  100% {
    opacity: 0.5;
  }
}

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
.select-box-container {
  font-size: 12px;
  position: relative;
  font-family: "Segoe UI", Arial, Verdana, sans-serif;
  outline: none;
}
.select-box-container .input-container {
  background-color: white;
  border: 1px solid lightgray;
  padding: 2px 3px;
}
.select-box-container .input-container.standout-on-hover {
  border: 1px solid transparent;
}
.select-box-container .input-container.standout-on-hover:hover {
  border: 1px solid lightgray;
}
.select-box-container .input-container input-like {
  white-space: nowrap;
  width: 100%;
  padding: 0;
  margin: 0;
  border-width: 0;
  outline: none;
  height: 100%;
  display: block;
  font-family: "Segoe UI", Arial, Verdana, sans-serif;
  line-height: 1.35em;
}
.select-box-container .input-container:hover {
  border: 1px solid darkgray;
}
.select-box-container .input-container .arrow-down-icon {
  position: absolute;
  top: 3px;
  color: lightgray;
  right: 3px;
  line-height: 18px;
}
.select-box-container .input-container .arrow-down-icon:hover {
  color: black;
}
.select-box-container .input-container .arrow-down-icon.selected {
  color: black;
}
.select-box-container .items {
  border: 1px solid lightgray;
  background-color: white;
  position: absolute;
  outline: none;
  z-index: 9999;
  box-shadow: 1px 1px 1px 0px #e0dddd;
}
.select-box-container .items .item {
  display: flex;
  flex-direction: row;
  min-height: 25px;
  padding-left: 4px;
  padding-right: 4px;
  align-items: center;
  cursor: pointer;
}
.select-box-container .items .item.last-selected {
  background-color: #e4e3e3;
}
.select-box-container .items .item.selected {
  background-color: #bfe4bd;
}
.select-box-container .items .item.no-select {
  background-color: inherit;
  cursor: default;
}
.select-box-container .items .item > div {
  flex: 1;
}
/**end-ignore-save-as-html*/

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
selection-settings.mt-common-dialog {
  position: relative;
  display: flex;
  height: 23px;
  flex-direction: row;
  white-space: nowrap;
  opacity: 1;
  box-shadow: initial;
  border: none;
  background: transparent;
  margin-top: -1px;
  transition: opacity 0.25s ease-in-out;
}
selection-settings.mt-common-dialog span {
  line-height: 1.35em;
}
selection-settings.mt-common-dialog selection-content {
  display: flex;
}
selection-settings.mt-common-dialog separate-hbar {
  border-left: 1px solid lightgray;
  margin-left: 2px;
  margin-right: 2px;
  display: block;
  margin-top: 4px;
  margin-bottom: 5px;
}
selection-settings.mt-common-dialog .fa.fa-share {
  border: 1px solid transparent;
  padding: 2px;
  color: gray;
  font-size: 13px;
  cursor: pointer;
}
selection-settings.mt-common-dialog .fa.fa-share:hover {
  background-color: white;
  border: 1px solid lightgray;
}
selection-settings.mt-common-dialog.hide {
  visibility: hidden;
  opacity: 0;
}
selection-settings.mt-common-dialog font-select-container {
  display: block;
}
selection-settings.mt-common-dialog small-symbol {
  font-family: Asana;
  font-size: 10px;
  vertical-align: 3px;
}
selection-settings.mt-common-dialog arrow-inside {
  font-family: Asana;
  font-size: 10px;
  vertical-align: 2px;
}
selection-settings.mt-common-dialog big-symbol {
  font-family: Asana;
  font-size: 14px;
  vertical-align: 3px;
}
selection-settings.mt-common-dialog.math {
  display: block;
  white-space: nowrap;
  padding: 5px;
  padding-left: 0px;
}
selection-settings.mt-common-dialog font-select {
  display: inline-block;
  padding: 0 2px;
  cursor: pointer;
  border: 1px solid transparent;
  color: #757575;
}
selection-settings.mt-common-dialog font-select.selected {
  background: white;
  border: 1px solid lightgray;
}
selection-settings.mt-common-dialog font-select:hover {
  border: 1px solid lightgray;
}
selection-settings.mt-common-dialog styled-select {
  padding-top: 3px;
}
selection-settings.mt-common-dialog styled-select.font-select {
  margin-right: 5px;
}
selection-settings.mt-common-dialog list-items-options {
  margin-top: 3px;
  margin-left: 5px;
}
selection-settings.mt-common-dialog list-items-options > i {
  width: 13px;
  text-align: center;
}
.text-style-settings-container list-items-options {
  margin-top: 3px;
  margin-left: 5px;
}
.text-style-settings-container list-items-options > i {
  width: 13px;
  text-align: center;
}
.text-style-settings-container styled-select {
  padding-top: 3px;
}
.text-style-settings-container styled-select.font-select {
  margin-right: 5px;
}
.text-style-settings-container separate-hbar {
  border-left: 1px solid lightgray;
  margin-left: 5px;
  margin-right: 5px;
  display: block;
  margin-top: 4px;
  margin-bottom: 5px;
}
/**end-ignore-save-as-html*/

/**start-ignore-save-as-html*/
expandable-component {
  outline: none;
  display: block;
  position: relative;
  z-index: 10;
}
expandable-component.mobile-tablet {
  cursor: pointer;
}
expandable-component item-option {
  cursor: default;
  display: block;
  border: 1px solid transparent;
  width: 20px;
  height: 20px;
  position: relative;
}
expandable-component item-option.selected {
  border: 1px solid #d4d3d3;
}
expandable-component item-option:hover {
  border: 1px solid #d4d3d3;
}
/**end-ignore-save-as-html*/

.color-picker no-color-select {
  display: block;
  text-align: center;
  /* width: 100%; */
  /* height: 20px; */
  background: white;
  border: 1px solid lightgray;
  margin-bottom: 2px;
  color: gray;
  padding: 2px;
  cursor: default;
  border-radius: 0;
  padding-bottom: 2px;
  margin-bottom: 3px;
  margin-left: -1px;
  margin-right: -1px;
  font-size: 13px;
}
.color-picker no-color-select:hover {
  color: black;
}
.color-picker color-select {
  position: absolute;
  top: 30px;
  left: 0px;
  background: white;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type editarea.no-area-container,
math-type editarea.text-mode,
math-type area-container,
math-type .print-as-area-container {
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
math-type editarea.no-area-container > line,
math-type editarea.text-mode > line,
math-type area-container > line,
math-type .print-as-area-container > line {
  min-width: 0.3em;
  position: relative;
  display: block;
  width: 100%;
  align-items: baseline;
  flex-wrap: wrap;
  box-sizing: border-box;
  flex-wrap: nowrap;
  line-height: 1.2;
  white-space: nowrap;
}
math-type editarea.no-area-container > line block,
math-type editarea.text-mode > line block,
math-type area-container > line block,
math-type .print-as-area-container > line block {
  white-space: pre;
}
math-type editarea.no-area-container > line.single-block > blocks > baselineblock,
math-type editarea.text-mode > line.single-block > blocks > baselineblock,
math-type area-container > line.single-block > blocks > baselineblock,
math-type .print-as-area-container > line.single-block > blocks > baselineblock {
  display: none;
}
math-type editarea.no-area-container > line > blocks,
math-type editarea.text-mode > line > blocks,
math-type area-container > line > blocks,
math-type .print-as-area-container > line > blocks {
  /**
            Different between inline and inline-block is about whether Section Text will be wrap in next wrapped line 
            */
  display: inline-block;
  text-align: left;
}
math-type editarea.no-area-container > line.root,
math-type editarea.text-mode > line.root,
math-type area-container > line.root,
math-type .print-as-area-container > line.root,
math-type editarea.no-area-container > line.text-mode,
math-type editarea.text-mode > line.text-mode,
math-type area-container > line.text-mode,
math-type .print-as-area-container > line.text-mode {
  display: block;
  font-style: normal;
}
math-type editarea.no-area-container > line.root > prefix,
math-type editarea.text-mode > line.root > prefix,
math-type area-container > line.root > prefix,
math-type .print-as-area-container > line.root > prefix,
math-type editarea.no-area-container > line.text-mode > prefix,
math-type editarea.text-mode > line.text-mode > prefix,
math-type area-container > line.text-mode > prefix,
math-type .print-as-area-container > line.text-mode > prefix {
  white-space: pre;
  display: inline-block;
  cursor: default;
  text-align: left;
}
math-type editarea.no-area-container > line.root > blocks,
math-type editarea.text-mode > line.root > blocks,
math-type area-container > line.root > blocks,
math-type .print-as-area-container > line.root > blocks,
math-type editarea.no-area-container > line.text-mode > blocks,
math-type editarea.text-mode > line.text-mode > blocks,
math-type area-container > line.text-mode > blocks,
math-type .print-as-area-container > line.text-mode > blocks {
  /** should not wrap here*/
  align-items: baseline;
  box-sizing: border-box;
}
math-type editarea.no-area-container > line.root > blocks > block,
math-type editarea.text-mode > line.root > blocks > block,
math-type area-container > line.root > blocks > block,
math-type .print-as-area-container > line.root > blocks > block,
math-type editarea.no-area-container > line.text-mode > blocks > block,
math-type editarea.text-mode > line.text-mode > blocks > block,
math-type area-container > line.text-mode > blocks > block,
math-type .print-as-area-container > line.text-mode > blocks > block {
  white-space: pre-wrap;
  white-space: break-spaces;
}
math-type editarea.no-area-container > line.root.full-line-block-inside > prefix,
math-type editarea.text-mode > line.root.full-line-block-inside > prefix,
math-type area-container > line.root.full-line-block-inside > prefix,
math-type .print-as-area-container > line.root.full-line-block-inside > prefix,
math-type editarea.no-area-container > line.text-mode.full-line-block-inside > prefix,
math-type editarea.text-mode > line.text-mode.full-line-block-inside > prefix,
math-type area-container > line.text-mode.full-line-block-inside > prefix,
math-type .print-as-area-container > line.text-mode.full-line-block-inside > prefix {
  float: left;
  vertical-align: top;
}
math-type editarea.no-area-container > line.root.full-line-block-inside > blocks,
math-type editarea.text-mode > line.root.full-line-block-inside > blocks,
math-type area-container > line.root.full-line-block-inside > blocks,
math-type .print-as-area-container > line.root.full-line-block-inside > blocks,
math-type editarea.no-area-container > line.text-mode.full-line-block-inside > blocks,
math-type editarea.text-mode > line.text-mode.full-line-block-inside > blocks,
math-type area-container > line.text-mode.full-line-block-inside > blocks,
math-type .print-as-area-container > line.text-mode.full-line-block-inside > blocks {
  display: block;
  width: 100%;
}
math-type editarea.no-area-container > line.root.full-line-block-inside > blocks > baselineblock,
math-type editarea.text-mode > line.root.full-line-block-inside > blocks > baselineblock,
math-type area-container > line.root.full-line-block-inside > blocks > baselineblock,
math-type .print-as-area-container > line.root.full-line-block-inside > blocks > baselineblock,
math-type editarea.no-area-container > line.text-mode.full-line-block-inside > blocks > baselineblock,
math-type editarea.text-mode > line.text-mode.full-line-block-inside > blocks > baselineblock,
math-type area-container > line.text-mode.full-line-block-inside > blocks > baselineblock,
math-type .print-as-area-container > line.text-mode.full-line-block-inside > blocks > baselineblock {
  display: inline-block;
  float: left;
}
math-type editarea.no-area-container > line.root.has-rtl.align-justify,
math-type editarea.text-mode > line.root.has-rtl.align-justify,
math-type area-container > line.root.has-rtl.align-justify,
math-type .print-as-area-container > line.root.has-rtl.align-justify,
math-type editarea.no-area-container > line.text-mode.has-rtl.align-justify,
math-type editarea.text-mode > line.text-mode.has-rtl.align-justify,
math-type area-container > line.text-mode.has-rtl.align-justify,
math-type .print-as-area-container > line.text-mode.has-rtl.align-justify {
  direction: rtl;
}
math-type editarea.no-area-container > line.root.section,
math-type editarea.text-mode > line.root.section,
math-type area-container > line.root.section,
math-type .print-as-area-container > line.root.section,
math-type editarea.no-area-container > line.text-mode.section,
math-type editarea.text-mode > line.text-mode.section,
math-type area-container > line.text-mode.section,
math-type .print-as-area-container > line.text-mode.section {
  text-align: left;
}
math-type editarea.no-area-container > line.root.section > blocks,
math-type editarea.text-mode > line.root.section > blocks,
math-type area-container > line.root.section > blocks,
math-type .print-as-area-container > line.root.section > blocks,
math-type editarea.no-area-container > line.text-mode.section > blocks,
math-type editarea.text-mode > line.text-mode.section > blocks,
math-type area-container > line.text-mode.section > blocks,
math-type .print-as-area-container > line.text-mode.section > blocks {
  /**
                    Different between inline and inline-block is about whether Section Text will be wrap in next wrapped line 
                    */
  display: inline;
}
math-type editarea.no-area-container > line.root.section.has-rtl,
math-type editarea.text-mode > line.root.section.has-rtl,
math-type area-container > line.root.section.has-rtl,
math-type .print-as-area-container > line.root.section.has-rtl,
math-type editarea.no-area-container > line.text-mode.section.has-rtl,
math-type editarea.text-mode > line.text-mode.section.has-rtl,
math-type area-container > line.text-mode.section.has-rtl,
math-type .print-as-area-container > line.text-mode.section.has-rtl {
  direction: rtl;
}
math-type editarea.no-area-container > line.root.section.has-rtl > prefix,
math-type editarea.text-mode > line.root.section.has-rtl > prefix,
math-type area-container > line.root.section.has-rtl > prefix,
math-type .print-as-area-container > line.root.section.has-rtl > prefix,
math-type editarea.no-area-container > line.text-mode.section.has-rtl > prefix,
math-type editarea.text-mode > line.text-mode.section.has-rtl > prefix,
math-type area-container > line.text-mode.section.has-rtl > prefix,
math-type .print-as-area-container > line.text-mode.section.has-rtl > prefix {
  direction: rtl;
  order: 1;
}
math-type editarea.no-area-container > line.root.theorem,
math-type editarea.text-mode > line.root.theorem,
math-type area-container > line.root.theorem,
math-type .print-as-area-container > line.root.theorem,
math-type editarea.no-area-container > line.text-mode.theorem,
math-type editarea.text-mode > line.text-mode.theorem,
math-type area-container > line.text-mode.theorem,
math-type .print-as-area-container > line.text-mode.theorem {
  padding-top: 0.3em;
  padding-bottom: 0.3em;
}
math-type editarea.no-area-container > line > prefix,
math-type editarea.text-mode > line > prefix,
math-type area-container > line > prefix,
math-type .print-as-area-container > line > prefix {
  text-align: right;
}
math-type editarea.no-area-container > line.has-indent,
math-type editarea.text-mode > line.has-indent,
math-type area-container > line.has-indent,
math-type .print-as-area-container > line.has-indent {
  display: flex;
  justify-content: flex-start;
}
math-type editarea.no-area-container > line.has-rtl.has-indent > prefix,
math-type editarea.text-mode > line.has-rtl.has-indent > prefix,
math-type area-container > line.has-rtl.has-indent > prefix,
math-type .print-as-area-container > line.has-rtl.has-indent > prefix {
  direction: rtl;
  order: 1;
}
math-type editarea.no-area-container > line.has-rtl.has-indent.indent-0,
math-type editarea.text-mode > line.has-rtl.has-indent.indent-0,
math-type area-container > line.has-rtl.has-indent.indent-0,
math-type .print-as-area-container > line.has-rtl.has-indent.indent-0 {
  padding-left: 0;
  padding-right: 30px;
}
math-type editarea.no-area-container > line.has-rtl.has-indent.indent-1,
math-type editarea.text-mode > line.has-rtl.has-indent.indent-1,
math-type area-container > line.has-rtl.has-indent.indent-1,
math-type .print-as-area-container > line.has-rtl.has-indent.indent-1 {
  padding-left: 0;
  padding-right: 65px;
}
math-type editarea.no-area-container > line.has-rtl.has-indent.indent-2,
math-type editarea.text-mode > line.has-rtl.has-indent.indent-2,
math-type area-container > line.has-rtl.has-indent.indent-2,
math-type .print-as-area-container > line.has-rtl.has-indent.indent-2 {
  padding-left: 0;
  padding-right: 105px;
}
math-type editarea.no-area-container > line.has-rtl.has-indent.indent-3,
math-type editarea.text-mode > line.has-rtl.has-indent.indent-3,
math-type area-container > line.has-rtl.has-indent.indent-3,
math-type .print-as-area-container > line.has-rtl.has-indent.indent-3 {
  padding-left: 0;
  padding-right: 145px;
}
math-type editarea.no-area-container > line.has-rtl.has-indent.indent-4,
math-type editarea.text-mode > line.has-rtl.has-indent.indent-4,
math-type area-container > line.has-rtl.has-indent.indent-4,
math-type .print-as-area-container > line.has-rtl.has-indent.indent-4 {
  padding-left: 0;
  padding-right: 185px;
}
math-type editarea.no-area-container > line.indent-0,
math-type editarea.text-mode > line.indent-0,
math-type area-container > line.indent-0,
math-type .print-as-area-container > line.indent-0 {
  padding-left: 30px;
}
math-type editarea.no-area-container > line.indent-1,
math-type editarea.text-mode > line.indent-1,
math-type area-container > line.indent-1,
math-type .print-as-area-container > line.indent-1 {
  padding-left: 65px;
}
math-type editarea.no-area-container > line.indent-2,
math-type editarea.text-mode > line.indent-2,
math-type area-container > line.indent-2,
math-type .print-as-area-container > line.indent-2 {
  padding-left: 105px;
}
math-type editarea.no-area-container > line.indent-3,
math-type editarea.text-mode > line.indent-3,
math-type area-container > line.indent-3,
math-type .print-as-area-container > line.indent-3 {
  padding-left: 145px;
}
math-type editarea.no-area-container > line.indent-4,
math-type editarea.text-mode > line.indent-4,
math-type area-container > line.indent-4,
math-type .print-as-area-container > line.indent-4 {
  padding-left: 185px;
}
math-type editarea.no-area-container > line.math-container-selected,
math-type editarea.text-mode > line.math-container-selected,
math-type area-container > line.math-container-selected,
math-type .print-as-area-container > line.math-container-selected {
  outline: 1px solid rgba(93, 92, 92, 0.3);
}
math-type editarea.no-area-container > line.math-container,
math-type editarea.text-mode > line.math-container,
math-type area-container > line.math-container,
math-type .print-as-area-container > line.math-container {
  font-family: 'Asana';
  justify-content: center;
  text-align: center;
  text-align-last: auto;
  display: flex;
}
math-type editarea.no-area-container > line.math-container block,
math-type editarea.text-mode > line.math-container block,
math-type area-container > line.math-container block,
math-type .print-as-area-container > line.math-container block {
  white-space: pre;
}
math-type editarea.no-area-container > line.align-center,
math-type editarea.text-mode > line.align-center,
math-type area-container > line.align-center,
math-type .print-as-area-container > line.align-center {
  text-align: center;
  text-align-last: auto;
}
math-type editarea.no-area-container > line.align-center.has-indent,
math-type editarea.text-mode > line.align-center.has-indent,
math-type area-container > line.align-center.has-indent,
math-type .print-as-area-container > line.align-center.has-indent {
  justify-content: center;
}
math-type editarea.no-area-container > line.align-center.section,
math-type editarea.text-mode > line.align-center.section,
math-type area-container > line.align-center.section,
math-type .print-as-area-container > line.align-center.section {
  text-align: center;
  text-align-last: auto;
}
math-type editarea.no-area-container > line.align-center > blocks,
math-type editarea.text-mode > line.align-center > blocks,
math-type area-container > line.align-center > blocks,
math-type .print-as-area-container > line.align-center > blocks {
  text-align: center;
  text-align-last: auto;
}
math-type editarea.no-area-container > line.align-justify,
math-type editarea.text-mode > line.align-justify,
math-type area-container > line.align-justify,
math-type .print-as-area-container > line.align-justify {
  text-align: justify;
}
math-type editarea.no-area-container > line.align-justify.section,
math-type editarea.text-mode > line.align-justify.section,
math-type area-container > line.align-justify.section,
math-type .print-as-area-container > line.align-justify.section {
  text-align: justify;
}
math-type editarea.no-area-container > line.align-justify > blocks,
math-type editarea.text-mode > line.align-justify > blocks,
math-type area-container > line.align-justify > blocks,
math-type .print-as-area-container > line.align-justify > blocks {
  text-align: justify;
}
math-type editarea.no-area-container > line.align-left,
math-type editarea.text-mode > line.align-left,
math-type area-container > line.align-left,
math-type .print-as-area-container > line.align-left {
  text-align: left;
}
math-type editarea.no-area-container > line.align-left > blocks,
math-type editarea.text-mode > line.align-left > blocks,
math-type area-container > line.align-left > blocks,
math-type .print-as-area-container > line.align-left > blocks {
  text-align: left;
}
math-type editarea.no-area-container > line.align-right,
math-type editarea.text-mode > line.align-right,
math-type area-container > line.align-right,
math-type .print-as-area-container > line.align-right {
  text-align: right;
}
math-type editarea.no-area-container > line.align-right.has-indent,
math-type editarea.text-mode > line.align-right.has-indent,
math-type area-container > line.align-right.has-indent,
math-type .print-as-area-container > line.align-right.has-indent {
  justify-content: flex-end;
}
math-type editarea.no-area-container > line.align-right.section,
math-type editarea.text-mode > line.align-right.section,
math-type area-container > line.align-right.section,
math-type .print-as-area-container > line.align-right.section {
  text-align: right;
}
math-type editarea.no-area-container > line.align-right > blocks,
math-type editarea.text-mode > line.align-right > blocks,
math-type area-container > line.align-right > blocks,
math-type .print-as-area-container > line.align-right > blocks {
  text-align: right;
}
math-type editarea.no-area-container > line > blocks > block,
math-type editarea.text-mode > line > blocks > block,
math-type area-container > line > blocks > block,
math-type .print-as-area-container > line > blocks > block {
  display: inline;
}
math-type editarea.no-area-container,
math-type area-container {
  align-items: initial;
}
math-type editarea.root-editor {
  align-items: initial;
  display: block;
  color: black;
  fill: black;
  stroke: black;
  border-color: black;
}
math-type editarea.root-editor.read-only {
  cursor: default;
}
math-type editarea.root-editor.read-only block {
  cursor: default;
}
math-type editarea.root-editor > area-container {
  display: block;
}
math-type area-container {
  width: 100%;
}
math-type block {
  cursor: text;
}
math-type editarea {
  box-sizing: border-box;
  display: flex;
  overflow-x: visible;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  flex-wrap: nowrap;
}
math-type editarea block {
  cursor: text;
}
math-type editarea.high-order {
  position: relative;
  z-index: 10;
}
math-type editarea.selected {
  outline: 1px solid rgba(93, 92, 92, 0.3);
}
math-type editarea.bordered {
  outline: 1px solid rgba(93, 92, 92, 0.3);
}
math-type editarea.center > area-container > line,
math-type editarea.center > line {
  justify-content: center;
  text-align: center;
  text-align-last: auto;
}
math-type editarea.left > area-container > line,
math-type editarea.left > line {
  justify-content: flex-start;
  text-align: left;
}
math-type editarea.right > area-container > line,
math-type editarea.right > line {
  justify-content: flex-end;
  text-align: right;
}
math-type editarea > area-baseline {
  width: 0;
  overflow: hidden;
  display: inline-block;
  float: left;
}
math-type setting.mt-common-dialog.line-tag-setting {
  transform: translate(0, -100%);
  position: absolute;
  font-family: serif;
  font-size: 11px;
  top: -4px;
  left: -10px;
  outline: none;
}
math-type setting.mt-common-dialog.line-tag-setting i {
  font-style: normal;
  padding: 1px 3px;
}
baselineblock {
  height: 0px;
  display: inline-block;
  width: 0;
  overflow: hidden;
}
baselineblock.inline {
  display: inline;
}
emptyblock {
  white-space: pre;
}
composite {
  display: inline-block;
}
block.Binary {
  padding: 0 0.2em;
}
block.Relation {
  padding: 0 0.3em;
}
block.Punctuation {
  padding-right: 0.2em;
}

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
bulb {
  background-color: #f7f7f7;
  border: 1px solid #c3c2c2;
  display: flex;
  position: absolute;
  left: -17px;
  top: -23px;
  cursor: pointer;
  color: gray;
  font-size: 18px;
  opacity: 0.7;
  line-height: normal;
  height: 22px;
  width: 22px;
  z-index: 11;
  justify-content: center;
  align-items: center;
  text-align: center;
}
bulb > i.fa.fa-cog {
  display: block;
}
bulb.smaller {
  font-size: 16px;
  height: 18px;
  width: 18px;
}
bulb:hover {
  opacity: 1;
}
/**end-ignore-save-as-html*/


check-box-wrapper {
  display: block;
  font-size: 11px;
  color: gray;
  cursor: pointer;
}
check-box-wrapper.disabled {
  color: lightgray;
}
check-box-wrapper.disabled check-box-rect {
  border: 1px solid #e2e1e1;
  color: lightgray;
}
check-box-wrapper.disabled check-box-rect:hover {
  border: 1px solid #e2e1e1;
}
check-box-wrapper.mobile-tablet check-box-rect {
  margin-top: -3px;
  width: 16px;
  height: 15px;
  font-size: 13px;
}
check-box-wrapper.mobile-tablet check-box-rect > i {
  left: 2px;
  top: 1px;
}
check-box-wrapper check-box-rect {
  width: 1.4em;
  height: 1.35em;
  border: 1px solid #c3c2c2;
  position: relative;
  display: inline-flex;
  font-size: 11px;
  color: green;
  background: white;
  justify-content: center;
  align-items: center;
}
check-box-wrapper check-box-rect:hover {
  border: 1px solid gray;
}
check-box-wrapper.unchecked check-box-rect > i {
  visibility: hidden;
}

vcomposed-symbol {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  flex-direction: column;
}
vcomposed-symbol > start {
  display: block;
  float: left;
  overflow: hidden;
  margin-bottom: -0.02em;
}
vcomposed-symbol > middle-center {
  overflow: hidden;
  margin-top: -0.02em;
  margin-bottom: -0.02em;
}
vcomposed-symbol > middle {
  display: block;
  flex-grow: 1;
  position: relative;
  overflow: hidden;
  margin-top: -0.02em;
  margin-bottom: -0.02em;
}
vcomposed-symbol > middle > fixed {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
}
vcomposed-symbol > middle > fixed > inside {
  display: block;
  overflow: hidden;
}
vcomposed-symbol > end {
  margin-top: -0.02em;
  display: block;
  overflow: hidden;
}


.open-brace,
.close-brace {
  padding: 0 0.1em;
  width: 0.5em;
  min-width: 0.5em;
}
.open-brace > start,
.close-brace > start {
  min-height: 0.75em;
  height: 0.75em;
}
.open-brace > middle-center,
.close-brace > middle-center {
  min-height: 0.6em;
  height: 0.6em;
}
.open-brace > end,
.close-brace > end {
  min-height: 0.75em;
  height: 0.75em;
}
.open-bracket,
.close-bracket {
  width: 0.43em;
  min-width: 0.43em;
}
.open-bracket > start,
.close-bracket > start {
  height: 0.5em;
  min-height: 0.5em;
}
.open-bracket > end,
.close-bracket > end {
  height: 0.7em;
  min-height: 0.7em;
}
.open-parenthesis,
.close-parenthesis {
  width: 0.45em;
  min-width: 0.45em;
}
.open-parenthesis > start,
.close-parenthesis > start {
  min-height: 0.85em;
  height: 0.85em;
}
.open-parenthesis > end,
.close-parenthesis > end {
  min-height: 0.8em;
  height: 0.8em;
}
.open-floor,
.close-floor {
  width: 0.45em;
  min-width: 0.45em;
}
.open-floor > end,
.close-floor > end {
  height: 0.35em;
  min-height: 0.35em;
}
.open-ceil,
.close-ceil {
  width: 0.45em;
  min-width: 0.45em;
}
.open-ceil > start,
.close-ceil > start {
  height: 0.5em;
  min-height: 0.5em;
}
.open-vert,
.close-vert {
  width: 0.3em;
  min-width: 0.3em;
}
.open-Vert,
.close-Vert {
  width: 0.5em;
  min-width: 0.5em;
}
.open-uparrow,
.close-uparrow {
  width: 0.45em;
  min-width: 0.45em;
}
.open-uparrow > start,
.close-uparrow > start {
  height: 0.5em;
  min-height: 0.5em;
}
.open-downarrow,
.close-downarrow {
  width: 0.45em;
  min-width: 0.45em;
}
.open-downarrow > end,
.close-downarrow > end {
  height: 0.4em;
  min-height: 0.4em;
}
.open-updownarrow,
.close-updownarrow {
  width: 0.45em;
  min-width: 0.45em;
}
.open-updownarrow > start,
.close-updownarrow > start {
  height: 0.5em;
  min-height: 0.5em;
}
.open-updownarrow > end,
.close-updownarrow > end {
  height: 0.4em;
  min-height: 0.4em;
}
.open-Uparrow,
.close-Uparrow {
  width: 0.7em;
  min-width: 0.7em;
}
.open-Uparrow > start,
.close-Uparrow > start {
  height: 0.5em;
  min-height: 0.5em;
}
.open-Downarrow,
.close-Downarrow {
  width: 0.7em;
  min-width: 0.7em;
}
.open-Downarrow > end,
.close-Downarrow > end {
  height: 0.5em;
  min-height: 0.5em;
}
.open-Updownarrow,
.close-Updownarrow {
  width: 0.6em;
  min-width: 0.6em;
}
.open-Updownarrow > start,
.close-Updownarrow > start {
  height: 0.5em;
  min-height: 0.5em;
}
.open-Updownarrow > end,
.close-Updownarrow > end {
  height: 0.5em;
  min-height: 0.5em;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
opensymbolblock,
closesymbolblock,
.middle-vert-symbol {
  display: inline-flex;
  align-items: center;
  position: relative;
  text-align: left;
  margin-top: 0.1em;
  margin-bottom: 0.1em;
}
opensymbolblock.normal,
closesymbolblock.normal,
.middle-vert-symbol.normal {
  margin-top: 0;
  margin-bottom: 0;
}
opensymbolblock > hidden-span,
closesymbolblock > hidden-span,
.middle-vert-symbol > hidden-span {
  visibility: hidden;
  width: 0px;
}
opensymbolblock[type='brace'],
closesymbolblock[type='brace'],
.middle-vert-symbol[type='brace'] {
  margin-left: 0.05em;
  margin-right: 0.05em;
  overflow: visible;
}
opensymbolblock[type='brace'] > svg,
closesymbolblock[type='brace'] > svg,
.middle-vert-symbol[type='brace'] > svg {
  overflow: visible;
  width: 0.5em;
  height: 100%;
}
opensymbolblock[type='parenthesis'] > svg,
closesymbolblock[type='parenthesis'] > svg,
.middle-vert-symbol[type='parenthesis'] > svg {
  width: 0.5em;
  height: 100%;
}
opensymbolblock[type='bracket'] > svg,
closesymbolblock[type='bracket'] > svg,
.middle-vert-symbol[type='bracket'] > svg {
  width: 0.5em;
  height: 100%;
}
opensymbolblock[type='angle'].sm-1 > svg,
closesymbolblock[type='angle'].sm-1 > svg,
.middle-vert-symbol[type='angle'].sm-1 > svg {
  width: 0.3em;
}
opensymbolblock[type='angle'].sm-2 > svg,
closesymbolblock[type='angle'].sm-2 > svg,
.middle-vert-symbol[type='angle'].sm-2 > svg {
  width: 0.45em;
}
opensymbolblock[type='angle'] > svg,
closesymbolblock[type='angle'] > svg,
.middle-vert-symbol[type='angle'] > svg {
  width: 0.6em;
  height: 100%;
}
opensymbolblock[type='slash'] > svg,
closesymbolblock[type='slash'] > svg,
.middle-vert-symbol[type='slash'] > svg {
  width: 0.7em;
  height: 100%;
}
opensymbolblock > bracket-span,
closesymbolblock > bracket-span,
.middle-vert-symbol > bracket-span {
  display: block;
}
opensymbolblock > vcomposed-symbol,
closesymbolblock > vcomposed-symbol,
.middle-vert-symbol > vcomposed-symbol,
opensymbolblock > bracket-span,
closesymbolblock > bracket-span,
.middle-vert-symbol > bracket-span {
  height: 100%;
  width: 100%;
  font-style: normal;
}
opensymbolblock > brace-span,
closesymbolblock > brace-span,
.middle-vert-symbol > brace-span {
  visibility: hidden;
  display: inline-block;
  padding: 0 0.1em;
}
opensymbolblock,
.middle-vert-symbol {
  margin-left: 0.07em;
}
opensymbolblock.normal,
.middle-vert-symbol.normal {
  margin-left: 0;
}
opensymbolblock[type='vert'],
.middle-vert-symbol[type='vert'],
opensymbolblock.middle-vert-symbol,
.middle-vert-symbol.middle-vert-symbol {
  margin-left: 0.05em;
  margin-right: 0.05em;
}
opensymbolblock[type='vert'] > svg,
.middle-vert-symbol[type='vert'] > svg,
opensymbolblock.middle-vert-symbol > svg,
.middle-vert-symbol.middle-vert-symbol > svg {
  width: 0.4em;
  height: 100%;
}
opensymbolblock[type='parenthesis'] > svg,
.middle-vert-symbol[type='parenthesis'] > svg {
  margin-right: -0.1em;
}
closesymbolblock {
  margin-right: 0.07em;
}
closesymbolblock.normal {
  margin-right: 0;
}
closesymbolblock[type='vert'] {
  margin-left: 0.05em;
  margin-right: 0.05em;
}
closesymbolblock[type='vert'] > svg {
  width: 0.4em;
  height: 100%;
}
closesymbolblock[type='parenthesis'] > svg {
  margin-left: -0.1em;
}

opensymbolblock[type=empty] > empty,
closesymbolblock[type=empty] > empty {
  width: 2px;
  height: 100%;
  display: block;
}
opensymbolblock[type=empty] > empty.normal-size,
closesymbolblock[type=empty] > empty.normal-size {
  height: 1em;
}
opensymbolblock[type=empty] > empty.line-selected,
closesymbolblock[type=empty] > empty.line-selected {
  background-color: lightgray;
}

.dirac-bracket-symbol > div > .oangle,
.dirac-bracket-symbol > div > .cangle {
  align-self: stretch;
  width: 0.55em;
  position: relative;
  margin-top: 0.1em;
  margin-bottom: -0.1em;
}
.dirac-bracket-symbol > div > .oangle > svg,
.dirac-bracket-symbol > div > .cangle > svg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.dirac-bracket-symbol > div > .vert {
  align-self: stretch;
  width: 0.25em;
  position: relative;
  margin-top: 0.1em;
  margin-bottom: -0.1em;
}
.dirac-bracket-symbol > div > .vert > svg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.dirac-bracket-symbol > div > editarea.first {
  min-width: 0.3em;
  padding-right: 0.1em;
}
.dirac-bracket-symbol > div > editarea.last {
  min-width: 0.3em;
  padding-left: 0.1em;
}
.dirac-bracket-symbol > div > editarea.middle {
  min-width: 0.3em;
  padding-left: 0.1em;
  padding-right: 0.1em;
}

cancel-symbol > editarea {
  display: inline-flex;
}
cancel-symbol > svg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: visible;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type compositeblock.frac-slashed-symbol.frac-inline {
  font-size: 0.75em;
}
math-type compositeblock.fraction-symbol {
  margin-left: 0.1em;
  margin-right: 0.1em;
  vertical-align: baseline;
}
math-type compositeblock.fraction-symbol.frac-inline {
  font-size: 0.75em;
}
math-type compositeblock.fraction-symbol > .enumerator {
  float: left;
  width: 100%;
  clear: both;
}
math-type compositeblock.fraction-symbol > .denominator {
  margin-top: -5px;
  float: left;
  width: 100%;
}
math-type compositeblock.fraction-symbol > .frac-line {
  clear: both;
  pointer-events: none;
}
math-type compositeblock.fraction-symbol > .frac-line > inline {
  display: inline-block;
  width: 100%;
  vertical-align: 0.25em;
  border-bottom: solid 1px;
}
math-type compositeblock.fraction-symbol > editarea {
  border: 0px;
  min-width: 0.6em;
  text-align: center;
  text-align-last: auto;
}
math-type compositeblock.fraction-symbol > editarea > area-container > line,
math-type compositeblock.fraction-symbol > editarea > line {
  justify-content: center;
  text-align: center;
  text-align-last: auto;
}
math-type compositeblock.fraction-symbol > editarea-line {
  text-align: center;
  text-align-last: auto;
}

.stackrel-icon {
  margin: auto;
  display: flex;
  align-items: center;
  flex-direction: column;
}
.stackrel-icon .square {
  width: 6px;
  height: 6px;
  border-style: solid;
  border-width: 1px;
  border-color: gray;
}
.stackrel-icon .distance {
  margin-top: 3px;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .stackrel-symbol.selected {
  outline: 1px solid rgba(93, 92, 92, 0.3);
}
math-type .stackrel-symbol > .cur-value {
  clear: both;
  display: inline-flex;
  width: 100%;
}
math-type .stackrel-symbol > .top {
  float: left;
  clear: both;
  width: 100%;
  font-size: 0.7em;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .underset-symbol > .center {
  display: inline-flex;
  width: 100%;
}
math-type .underset-symbol > .bottom {
  float: left;
  display: block;
  width: 100%;
  font-size: 0.7em;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .over-under-set-symbol.selected {
  outline: 1px solid rgba(93, 92, 92, 0.3);
}
math-type .over-under-set-symbol > .top {
  display: flex;
  float: left;
  clear: both;
  width: 100%;
}
math-type .over-under-set-symbol > .top > line {
  font-size: 0.7em;
}
math-type .over-under-set-symbol > .middle {
  display: inline-flex;
  width: 100%;
  clear: both;
}
math-type .over-under-set-symbol > .middle.select {
  border-bottom: none;
}
math-type .over-under-set-symbol > .bottom {
  display: flex;
  float: left;
  width: 100%;
}
math-type .over-under-set-symbol > .bottom > line {
  font-size: 0.7em;
}

expandable-component.thickness item-option.thickness-option {
  width: 40px;
  height: 12px;
}
expandable-component.thickness svg {
  stroke: gray;
  stroke-width: 1px;
  fill: none;
}
expandable-component.thickness thickness {
  position: relative;
  display: flex;
  height: 100%;
}
expandable-component.thickness thickness > svg.thickness {
  width: 25px;
  height: 100%;
  position: static;
}
expandable-component.thickness thickness > svg.thickness line {
  stroke: gray;
  stroke-width: 1px;
}
expandable-component.thickness thickness > svg.thickness path {
  fill: #d2cfcf;
  stroke: none;
}

/**start-ignore-save-as-html*/
numeric-slider {
  display: flex;
  align-items: baseline;
  padding-top: 3px;
  margin-left: 3px;
  margin-right: 3px;
}
numeric-slider.mobile-tablet {
  align-items: center;
}
numeric-slider numeric-unit {
  font-size: 12px;
  color: gray;
  padding-left: 2px;
  padding-right: 0px;
}
numeric-slider numeric-icon {
  border: 1px solid transparent;
}
numeric-slider numeric-icon.selected {
  border: 1px solid lightgray;
}
/**end-ignore-save-as-html*/

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .textcircled > div > editarea {
  text-align: center;
  text-align-last: auto;
}
math-type .textcircled > div > editarea > area-container > line,
math-type .textcircled > div > editarea > line {
  justify-content: center;
  text-align: center;
  text-align-last: auto;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
.math-container-symbol {
  margin-top: 0.3em;
  margin-bottom: 0.3em;
  display: inline-flex;
}
.math-container-symbol.inline {
  margin-top: 0px;
  margin-bottom: 0px;
}
.math-container-symbol.selected {
  background-color: rgba(76, 175, 80, 0.05);
  outline: 1px solid #c1d4c1;
}
.math-container-symbol.display {
  display: block;
}
.math-container-symbol.display.selected {
  background-color: rgba(76, 175, 80, 0.05);
  outline: none;
}
.math-container-symbol.display > editarea {
  width: 100%;
  flex-grow: 1;
  display: block;
  text-align: center;
  text-align-last: auto;
}
.math-container-symbol.display > editarea > area-container > line,
.math-container-symbol.display > editarea > line {
  justify-content: center;
  text-align: center;
  text-align-last: auto;
}
.math-container-symbol.display > editarea > area-container > line {
  padding-top: 8px;
  padding-bottom: 8px;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
.math-container-symbol > editarea.multiline {
  display: block;
  flex-grow: 1;
  text-align: center;
  text-align-last: auto;
}
.math-container-symbol > editarea.multiline > area-container > line,
.math-container-symbol > editarea.multiline > line {
  justify-content: center;
  text-align: center;
  text-align-last: auto;
}
.math-container-symbol > editarea.multiline > line:last-child {
  justify-content: flex-end;
  text-align: right;
}
.math-container-symbol > editarea.multiline > line:first-child {
  justify-content: flex-start;
  text-align: left;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .icon-sqrt,
.auto-complete-external-area .icon-sqrt {
  display: flex;
  margin: auto;
  font-size: 12px;
}
math-type .icon-sqrt .square,
.auto-complete-external-area .icon-sqrt .square {
  margin-top: 3px;
  margin-right: -6px;
}
math-type .icon-sqrt .square-expand,
.auto-complete-external-area .icon-sqrt .square-expand {
  background-color: #f7f7f7;
}
math-type .icon-sqrt .align-end,
.auto-complete-external-area .icon-sqrt .align-end {
  margin-top: 8px;
}
math-type .icon-sqrt .line,
.auto-complete-external-area .icon-sqrt .line {
  width: 10px;
  border-top: solid 1px black;
  margin-top: 6px;
}
math-type .icon-sqrt .big-square,
.auto-complete-external-area .icon-sqrt .big-square {
  margin-top: 11px;
  margin-left: -8px;
}
math-type .sqrt-symbol {
  white-space: nowrap;
  align-items: stretch;
  position: relative;
}
math-type .sqrt-symbol > add {
  opacity: 0.5;
  background-color: #f7f7f7;
  border: 1px solid #c3c2c2;
  color: gray;
  display: block;
  position: absolute;
  font-size: 0.6em;
  cursor: pointer;
  margin-left: -0.4em;
  z-index: 999;
  opacity: 0.7;
  left: 50%;
  top: -0.5em;
  left: 0.5em;
}
math-type .sqrt-symbol > add:hover {
  border-color: gray;
  opacity: 1;
}
math-type .sqrt-symbol > add > i {
  display: block;
  height: 1em;
  padding: 0.1em 0.15em;
  padding-bottom: 0;
  width: 0.8em;
}
math-type .sqrt-symbol text {
  fill: black;
}
math-type .sqrt-symbol > sqrt-edit {
  position: relative;
  order: 3;
  display: inline-block;
  align-self: flex-end;
}
math-type .sqrt-symbol > sqrt-edit > sqrt-symbol-line {
  overflow: visible;
  z-index: 1;
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}
math-type .sqrt-symbol > sqrt-edit > sqrt-symbol-line > svg {
  overflow: visible;
  width: 100%;
  height: 1em;
}
math-type .sqrt-symbol > sqrt-edit > area-container > line {
  width: 1em;
  position: relative;
}
math-type .sqrt-symbol > sqrt-edit > editarea,
math-type .sqrt-symbol > sqrt-edit > editarea-line {
  z-index: 2;
  display: inline-block;
  min-width: 0.5em;
  margin-left: 1em;
}
math-type .sqrt-symbol > sqrt-top {
  position: relative;
  z-index: 2;
  vertical-align: bottom;
  padding-bottom: 0.95em;
  align-self: flex-end;
  display: inline-block;
}
math-type .sqrt-symbol > sqrt-top.selected {
  outline: 1px solid rgba(93, 92, 92, 0.3);
}
math-type .sqrt-symbol > sqrt-top > editarea {
  font-size: 0.7em;
  border: none 0px;
  min-width: 0.5em;
  display: flex;
  float: right;
  text-align: center;
  text-align-last: auto;
}
math-type .sqrt-symbol > sqrt-top > editarea > line {
  justify-content: center;
  text-align: center;
  text-align-last: auto;
}

/**start-ignore-save-as-html*/
.settings__item {
  border: 1px solid transparent;
}
.settings__item:hover {
  border: 1px solid #e4e3e3;
}
.settings__item.selected {
  background-color: white;
  border: 1px solid lightgray;
}
/**end-ignore-save-as-html*/

/**start-ignore-save-as-html*/
/**end-ignore-save-as-html*/

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
image-viewer {
  width: 100%;
}
image-viewer.inline {
  width: auto;
}
image-viewer .image-content {
  cursor: pointer;
  text-align: left;
}
image-viewer detail list-items-options div {
  border: 1px solid transparent;
  cursor: pointer;
  margin-left: 3px;
  padding: 0 2px 0 2px;
}
image-viewer detail list-items-options div:hover {
  background-color: white;
  border: 1px solid lightgray;
}
image-viewer detail list-items-options div.selected {
  background-color: white;
  border: 1px solid lightgray;
}
image-viewer image-container {
  display: flex;
  flex-direction: column;
}
image-viewer image-container img {
  position: absolute;
}
image-viewer image-container.center {
  align-items: center;
}
image-viewer image-container.left {
  align-items: flex-start;
}
image-viewer image-container.right {
  align-items: flex-end;
}
.resize-box {
  position: absolute;
  width: 8px;
  height: 8px;
  border-width: 1px;
  border-color: gray;
  border-style: solid;
  background-color: white;
  z-index: 999;
}
.resize-box.mobile-tablet {
  width: 16px;
  height: 16px;
}
.resize-box.hide {
  display: none;
}
topleft {
  top: 0;
  left: 0;
  margin-left: -5px;
  margin-top: -5px;
  cursor: nwse-resize;
}
topleft.mobile-tablet {
  margin-left: -8px;
  margin-top: -8px;
}
topright {
  top: 0;
  right: 0;
  margin-right: -5px;
  margin-top: -5px;
  cursor: nesw-resize;
}
topright.mobile-tablet {
  margin-right: -8px;
  margin-top: -8px;
}
topmiddle {
  top: 0;
  left: 50%;
  margin-left: -5px;
  margin-top: -5px;
  cursor: ns-resize;
}
topmiddle.mobile-tablet {
  margin-left: -8px;
  margin-top: -8px;
}
leftmiddle {
  top: 50%;
  left: 0;
  margin-left: -5px;
  margin-top: -5px;
  cursor: ew-resize;
}
leftmiddle.mobile-tablet {
  margin-left: -8px;
  margin-top: -8px;
}
rightmiddle {
  top: 50%;
  right: 0;
  margin-right: -5px;
  margin-top: -5px;
  cursor: ew-resize;
}
rightmiddle.mobile-tablet {
  margin-right: -8px;
  margin-top: -8px;
}
bottomleft {
  bottom: 0;
  left: 0;
  margin-left: -5px;
  margin-bottom: -5px;
  cursor: nesw-resize;
}
bottomleft.mobile-tablet {
  margin-left: -8px;
  margin-bottom: -8px;
}
bottomright {
  bottom: 0;
  right: 0;
  margin-right: -5px;
  margin-bottom: -5px;
  cursor: nwse-resize;
}
bottomright.mobile-tablet {
  margin-right: -8px;
  margin-bottom: -8px;
}
bottommiddle {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  margin-bottom: -5px;
  cursor: ns-resize;
}
bottommiddle.mobile-tablet {
  margin-left: -8px;
  margin-bottom: -8px;
}
.ghost-viewer {
  outline: 1px dotted gray;
  width: 100%;
  height: 100%;
  display: block;
}

compositeblock.image-container {
  position: relative;
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
}
compositeblock.image-container > image-viewer {
  display: block;
}
compositeblock.image-container.inline {
  width: auto;
  margin-top: 0;
  margin-bottom: 0;
  display: inline-block;
}
.image-caption > line.text-mode:first-child {
  display: inline;
}
.image-caption > line.text-mode:first-child > blocks {
  display: inline;
}
.add-caption {
  opacity: 0.85;
  text-align: "center";
  color: #1155cc;
  cursor: pointer;
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translate(-50%, 0);
  z-index: 10;
  font-size: 13px;
  background: white;
  border: 1px solid lightgray;
  padding: 5px;
}
.add-caption:hover {
  opacity: 1;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .power-index-symbol-container {
  margin-right: 0.06em;
  margin-left: 0.03em;
}
math-type .power-index-symbol-container.has-power > .power-value {
  margin-left: 0.04em;
}
math-type .power-index-symbol-container > .power-value,
math-type .pre-script-symbol-container > .power-value {
  /*margin-bottom: -2px;*/
  float: left;
  clear: both;
  text-align: left;
}
math-type .power-index-symbol-container > .power-value.small > line,
math-type .pre-script-symbol-container > .power-value.small > line {
  margin-bottom: -0.35em;
}
math-type .power-index-symbol-container > .power-value > line,
math-type .pre-script-symbol-container > .power-value > line {
  font-size: 0.7em;
}
math-type .power-index-symbol-container > .power-value > area-container > line,
math-type .pre-script-symbol-container > .power-value > area-container > line,
math-type .power-index-symbol-container > .power-value > line,
math-type .pre-script-symbol-container > .power-value > line {
  justify-content: flex-start;
  text-align: left;
}
math-type .power-index-symbol-container > middle-base,
math-type .pre-script-symbol-container > middle-base {
  display: block;
  clear: both;
  height: 1em;
}
math-type .power-index-symbol-container > middle-base > inline,
math-type .pre-script-symbol-container > middle-base > inline {
  display: inline-block;
}
math-type .power-index-symbol-container > .index-value,
math-type .pre-script-symbol-container > .index-value {
  float: left;
  text-align: left;
}
math-type .power-index-symbol-container > .index-value > line,
math-type .pre-script-symbol-container > .index-value > line {
  font-size: 0.7em;
}
math-type .power-index-symbol-container > .index-value > area-container > line,
math-type .pre-script-symbol-container > .index-value > area-container > line,
math-type .power-index-symbol-container > .index-value > line,
math-type .pre-script-symbol-container > .index-value > line {
  justify-content: flex-start;
  text-align: left;
}
math-type .power-index-symbol-container > editarea-block.index-value,
math-type .pre-script-symbol-container > editarea-block.index-value,
math-type .power-index-symbol-container > editarea-line.index-value,
math-type .pre-script-symbol-container > editarea-line.index-value {
  font-size: 0.7em;
}
math-type .power-index-symbol-container > editarea-block.power-value,
math-type .pre-script-symbol-container > editarea-block.power-value,
math-type .power-index-symbol-container > editarea-line.power-value,
math-type .pre-script-symbol-container > editarea-line.power-value {
  font-size: 0.7em;
}
math-type .pre-script-symbol-container > .power-value {
  float: right;
}
math-type .pre-script-symbol-container > .index-value {
  float: right;
}
.icon-power-index-symbol {
  display: flex;
  margin: auto;
  width: 20px;
  padding-bottom: 6px;
}
.icon-power-index-symbol .square-up {
  margin-top: 4px;
}
.icon-power-index-symbol .square-down {
  margin-top: 14px;
  margin-left: -6px;
}
.icon-power-index-symbol .align-end {
  margin-top: 4px;
}
.icon-power-symbol {
  display: flex;
  margin: auto;
  width: 20px;
  padding-bottom: 6px;
}
.icon-power-symbol .square {
  margin-top: 4px;
}
.icon-power-symbol .align-end {
  margin-top: 4px;
}
.icon-index-symbol {
  display: flex;
  margin: auto;
  width: 20px;
  padding-bottom: 6px;
}
.icon-index-symbol .square {
  margin-top: 14px;
  margin-left: 0px;
}
.icon-index-symbol .align-end {
  margin-top: 4px;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
.matrix-symbol.matrix-like > matrix > table > tbody > tr > td {
  padding-bottom: 0.1em;
  padding-top: 0.1em;
}
.matrix-symbol.matrix-like > matrix > table > tbody > tr.hline {
  border-top: 1px solid;
}
.matrix-symbol.matrix-like > matrix > table > tbody > tr:last-child.last-hline {
  border-bottom: 1px solid;
}
.matrix-symbol {
  /* must be flex to have vertical center align , unless figure out another way */
  display: inline-flex;
  align-items: center;
}
.matrix-symbol > matrix > table > tbody > tr > td > .sbd {
  position: absolute;
  left: -1px;
  top: -1px;
  right: -1px;
  bottom: -1px;
  pointer-events: none;
  background: none;
}
.matrix-symbol.selected > matrix > table > tbody > tr > td > .sbd {
  border-top: 1px solid rgba(93, 92, 92, 0.15);
  border-left: 1px solid rgba(93, 92, 92, 0.15);
}
.matrix-symbol.selected > matrix > table > tbody > tr > td > .sbd.sbd-lr {
  border-bottom: 1px solid rgba(93, 92, 92, 0.15);
}
.matrix-symbol.selected > matrix > table > tbody > tr > td > .sbd.sbd-lc {
  border-right: 1px solid rgba(93, 92, 92, 0.15);
}
.matrix-symbol > base-line-indicator {
  height: inherit;
  margin-top: -0.18em;
}
.matrix-symbol matrixclosesymbol,
.matrix-symbol matrixopensymbol {
  font-style: normal;
  color: black;
  position: relative;
}
.matrix-symbol matrixclosesymbol > span,
.matrix-symbol matrixopensymbol > span {
  visibility: hidden;
  display: inline-block;
}
.matrix-symbol matrixclosesymbol > svg,
.matrix-symbol matrixopensymbol > svg {
  overflow: visible;
  font-size: 15px;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.matrix-symbol matrixclosesymbol > svg text,
.matrix-symbol matrixopensymbol > svg text {
  fill: black;
}
.matrix-symbol > matrix.matrix > table > tbody > tr > td {
  padding: 0 0.3em;
}
.matrix-symbol > matrix {
  display: flex;
  position: relative;
  float: left;
  align-items: flex-start;
  break-inside: avoid;
}
.matrix-symbol > matrix > svg {
  width: 0.6em;
  /*init height, willl be changed by inline style*/
  height: 3px;
  overflow: visible;
  align-self: flex-start;
}
.matrix-symbol > matrix > vcomposed-symbol {
  height: inherit;
  align-self: stretch;
}
.matrix-symbol > matrix > table {
  border: 1px solid transparent;
  border-collapse: collapse;
}
.matrix-symbol > matrix > table > tbody > tr {
  box-sizing: border-box;
}
.matrix-symbol > matrix > table > tbody > tr.smaller {
  font-size: 0.75em;
}
.matrix-symbol > matrix > table > tbody > tr > td {
  position: relative;
  border: 1px dotted transparent;
  box-sizing: border-box;
  padding: 0 0.3em;
  vertical-align: baseline;
}
.matrix-symbol > matrix > table > tbody > tr > td > .editor-cell {
  display: inline-block;
  width: 100%;
}
.matrix-symbol > matrix > table > tbody > tr > td.vline {
  border-left: 1px solid;
}
.matrix-symbol > matrix > table > tbody > tr > td:last-child.last-vline {
  border-right: 1px solid;
}
.matrix-symbol > matrix > table > tbody > tr > td > .editor-cell {
  min-width: 0.5em;
  text-align: center;
  text-align-last: auto;
}
.matrix-symbol > matrix > table > tbody > tr > td > .editor-cell > area-container > line,
.matrix-symbol > matrix > table > tbody > tr > td > .editor-cell > line {
  justify-content: center;
  text-align: center;
  text-align-last: auto;
}
.matrix-symbol > matrix.matrix > table {
  margin-left: -0.2em;
  margin-right: -0.2em;
}
.matrix-symbol > matrix.matrix > table > tbody > tr > td > .editor-cell {
  text-align: center;
  text-align-last: auto;
}
.matrix-symbol > matrix.matrix > table > tbody > tr > td > .editor-cell > area-container > line,
.matrix-symbol > matrix.matrix > table > tbody > tr > td > .editor-cell > line {
  justify-content: center;
  text-align: center;
  text-align-last: auto;
}
.matrix-symbol > matrix.array > table > tbody > tr > td > .editor-cell.left {
  text-align: left;
}
.matrix-symbol > matrix.array > table > tbody > tr > td > .editor-cell.left > area-container > line,
.matrix-symbol > matrix.array > table > tbody > tr > td > .editor-cell.left > line {
  justify-content: flex-start;
  text-align: left;
}
.matrix-symbol > matrix.array > table > tbody > tr > td > .editor-cell.center {
  text-align: center;
  text-align-last: auto;
}
.matrix-symbol > matrix.array > table > tbody > tr > td > .editor-cell.center > area-container > line,
.matrix-symbol > matrix.array > table > tbody > tr > td > .editor-cell.center > line {
  justify-content: center;
  text-align: center;
  text-align-last: auto;
}
.matrix-symbol > matrix.array > table > tbody > tr > td > .editor-cell.right {
  text-align: right;
}
.matrix-symbol > matrix.array > table > tbody > tr > td > .editor-cell.right > area-container > line,
.matrix-symbol > matrix.array > table > tbody > tr > td > .editor-cell.right > line {
  justify-content: flex-end;
  text-align: right;
}
.matrix-symbol > matrix.gathered > table > tbody > tr > td:nth-child(2n+3) {
  padding-left: 1em;
}
.matrix-symbol > matrix.gathered line {
  text-align: center;
  text-align-last: auto;
}
.matrix-symbol > matrix.aligned > table > tbody > tr > td {
  padding: 0;
}
.matrix-symbol > matrix.aligned > table > tbody > tr > td:nth-child(2n+3) {
  padding-left: 1em;
}
.matrix-symbol > matrix.aligned > table > tbody > tr > td:nth-child(2n+1) > .editor-cell {
  text-align: right;
}
.matrix-symbol > matrix.aligned > table > tbody > tr > td:nth-child(2n+1) > .editor-cell > area-container > line,
.matrix-symbol > matrix.aligned > table > tbody > tr > td:nth-child(2n+1) > .editor-cell > line {
  justify-content: flex-end;
  text-align: right;
}
.matrix-symbol > matrix.aligned > table > tbody > tr > td:nth-child(2n) > .editor-cell {
  text-align: left;
}
.matrix-symbol > matrix.aligned > table > tbody > tr > td:nth-child(2n) > .editor-cell > area-container > line,
.matrix-symbol > matrix.aligned > table > tbody > tr > td:nth-child(2n) > .editor-cell > line {
  justify-content: flex-start;
  text-align: left;
}
.matrix-symbol > matrix > border-design {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  display: block;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 10;
}
.matrix-symbol > matrix > border-design > border-line {
  cursor: pointer;
  position: absolute;
  /*border:1px solid gray;*/
}
.matrix-symbol > matrix > border-design > border-line > horizontal-line {
  border-top: 1px solid #eceaea;
  display: block;
  position: absolute;
  top: 50%;
  width: 100%;
  height: 1px;
  margin-top: 0;
  left: 0;
}
.matrix-symbol > matrix > border-design > border-line > vertical-line {
  border-left: 1px solid #eceaea;
  display: block;
  position: absolute;
  left: 50%;
  width: 1px;
  height: 100%;
  top: 0;
}
.matrix-symbol > matrix > border-design > border-line > vertical-line.enabled {
  border-top: 1px solid;
}
.matrix-symbol > matrix > border-design > border-line:hover > horizontal-line {
  border-top: 1px solid gray;
}
.matrix-symbol > matrix > border-design > border-line:hover > vertical-line {
  border-left: 1px solid gray;
}
.matrix-symbol > matrix > border-design > border-line.enabled > horizontal-line {
  border-top: 1px solid;
}
.matrix-symbol > matrix > border-design > border-line.enabled > vertical-line {
  border-left: 1px solid;
}
.matrix-symbol > matrix > setting {
  padding: 7px;
  position: absolute;
  top: -6px;
  font-family: "Segoe UI", Arial, Verdana, sans-serif;
  transform: translate(0, -100%);
  left: 0;
}
.matrix-symbol > matrix > setting > main-setting {
  display: flex;
  flex-direction: row;
}
.matrix-symbol > matrix > setting > main-setting styled-select {
  display: block;
  background-color: white;
}
.fa-table.array-settings {
  border: 1px solid lightgray;
  padding: 3px 5px;
  font-size: 14px;
  color: gray;
  cursor: pointer;
  /*margin-left: 5px;*/
}
.fa-table.array-settings:hover {
  color: black;
  background: white;
}
.matrix-icon {
  display: flex;
}
.matrix-icon .square {
  margin-top: 2px;
}
.matrix-icon .display-flex {
  display: flex;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
.matrix-symbol.case-symbol > matrix > table > tbody > tr > td,
.matrix-symbol.square-case-symbol > matrix > table > tbody > tr > td {
  padding: 0 0.2em;
}
.matrix-symbol.case-symbol > matrix > table > tbody > tr > td.non-select,
.matrix-symbol.square-case-symbol > matrix > table > tbody > tr > td.non-select {
  user-select: none;
  width: 0.2em;
  padding: 0;
}
.matrix-symbol.case-symbol > matrix > table > tbody > tr > td > .editor-cell,
.matrix-symbol.square-case-symbol > matrix > table > tbody > tr > td > .editor-cell {
  text-align: left;
}
.matrix-symbol.case-symbol > matrix > table > tbody > tr > td > .editor-cell > area-container > line,
.matrix-symbol.square-case-symbol > matrix > table > tbody > tr > td > .editor-cell > area-container > line,
.matrix-symbol.case-symbol > matrix > table > tbody > tr > td > .editor-cell > line,
.matrix-symbol.square-case-symbol > matrix > table > tbody > tr > td > .editor-cell > line {
  justify-content: flex-start;
  text-align: left;
}
.case-icon {
  display: flex;
  font-size: 18px;
  line-height: 18px;
}
.case-icon .square {
  margin-top: 2px;
}
.case-icon .display-flex {
  display: flex;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
.matrix-symbol.dr-case-symbol > matrix > table > tbody > tr > td {
  padding: 0 0.2em;
}
.matrix-symbol.dr-case-symbol > matrix > table > tbody > tr > td.selected {
  border: 1px solid #e0e0e0;
}
.matrix-symbol.dr-case-symbol > matrix > table > tbody > tr > td > .editor-cell {
  text-align: left;
}
.matrix-symbol.dr-case-symbol > matrix > table > tbody > tr > td > .editor-cell > area-container > line,
.matrix-symbol.dr-case-symbol > matrix > table > tbody > tr > td > .editor-cell > line {
  justify-content: flex-start;
  text-align: left;
}
.case-icon {
  display: flex;
  font-size: 18px;
  line-height: 18px;
}
.case-icon .square {
  margin-top: 2px;
}
.case-icon .display-flex {
  display: flex;
}

.array-icon .square {
  margin-top: 2px;
}
.array-icon .display-flex {
  display: flex;
}

.matrix-like.binom > .binom > table > tbody > tr > td {
  padding: 0;
}
.matrix-like.binom > .binom > table > tbody > tr > td line {
  text-align: center;
  text-align-last: auto;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
.matrix-symbol.align-symbol {
  /*display flex because to make sure it's spend whole line*/
  display: flex;
  justify-content: center;
  margin-top: 10px;
  margin-bottom: 10px;
  width: 100%;
}
.matrix-symbol.align-symbol.selected {
  background-color: rgba(76, 175, 80, 0.05);
}
.matrix-symbol.align-symbol > matrix > setting > main-setting {
  margin-bottom: 0px;
}
.matrix-symbol.align-symbol > matrix > bulb {
  left: 50%;
}
.matrix-symbol.align-symbol > matrix > setting {
  margin-left: 20px;
}
.matrix-symbol.align-symbol > matrix.align {
  width: 100%;
  display: block;
}
.matrix-symbol.align-symbol > matrix > table > tbody > tr > td {
  padding: 0;
  vertical-align: baseline;
  border: 1px solid transparent;
}
.matrix-symbol.align-symbol > matrix > table > tbody > tr > td > .editor-cell {
  /* should align start and area-container level (instead of line level)
                as for lazy rendering, we need to have exact line width */
}
.matrix-symbol.align-symbol > matrix > table > tbody > tr > td > .editor-cell > area-container {
  align-items: flex-start;
}
.matrix-symbol.align-symbol > matrix > table > tbody > tr > td > .editor-cell > area-container > line {
  width: auto;
  text-align: left;
}
.matrix-symbol.align-symbol > matrix > table > tbody > tr > td.align-right > .editor-cell {
  /* should align start and area-container level (instead of line level)
                as for lazy rendering, we need to have exact line width */
}
.matrix-symbol.align-symbol > matrix > table > tbody > tr > td.align-right > .editor-cell > area-container {
  align-items: flex-end;
}
.matrix-symbol.align-symbol > matrix > table > tbody > tr > td.align-right > .editor-cell > area-container > line {
  width: auto;
  text-align: right;
}
.matrix-symbol.align-symbol > matrix > table > tbody > tr > td.non-select {
  border: none;
  user-select: none;
}
.matrix-symbol.align-symbol > matrix > table > tbody > tr > td.expand {
  width: 100%;
}
.matrix-symbol.align-symbol > matrix > table > tbody > tr > td.selected {
  border: 1px solid #e0e0e0;
}
.matrix-symbol.align-symbol > matrix > table > tbody > tr > td > .editor-cell {
  min-width: 1.5em;
}
.matrix-symbol.align-symbol > matrix > table > tbody > tr > td > tupple {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: baseline;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
.matrix-symbol.gather-symbol {
  /*display flex because to make sure it's spend whole line*/
  display: flex;
  justify-content: center;
  margin-top: 10px;
  margin-bottom: 10px;
  width: 100%;
}
.matrix-symbol.gather-symbol.selected {
  background-color: rgba(76, 175, 80, 0.05);
}
.matrix-symbol.gather-symbol > matrix.gather {
  width: 100%;
  display: block;
}
.matrix-symbol.gather-symbol > matrix > setting > main-setting {
  margin-bottom: 0px;
}
.matrix-symbol.gather-symbol > matrix > bulb {
  left: -1em;
}
.matrix-symbol.gather-symbol > matrix > setting {
  margin-left: 20px;
}
.matrix-symbol.gather-symbol > matrix > table > tbody > tr > td {
  padding: 0;
  vertical-align: baseline;
  border: 1px solid transparent;
  text-align: center;
  text-align-last: auto;
}
.matrix-symbol.gather-symbol > matrix > table > tbody > tr > td.non-select {
  user-select: none;
  width: 1.5em;
  border: none;
}
.matrix-symbol.gather-symbol > matrix > table > tbody > tr > td.expand {
  width: 100%;
}
.matrix-symbol.gather-symbol > matrix > table > tbody > tr > td.selected {
  border: 1px solid #e0e0e0;
}
.matrix-symbol.gather-symbol > matrix > table > tbody > tr > td line {
  text-align: center;
  text-align-last: auto;
}
.matrix-symbol.gather-symbol > matrix > table > tbody > tr > td > .editor-cell {
  min-width: 1.5em;
  /* should align start and area-container level (instead of line level)
                as for lazy rendering, we need to have exact line width */
}
.matrix-symbol.gather-symbol > matrix > table > tbody > tr > td > .editor-cell > area-container {
  align-items: center;
}
.matrix-symbol.gather-symbol > matrix > table > tbody > tr > td > .editor-cell > area-container > line {
  width: auto;
}
.matrix-symbol.gather-symbol > matrix > table > tbody > tr > td > tupple {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: baseline;
}

math-type compositeblock.table-symbol {
  width: 100%;
  display: flex;
}
math-type compositeblock.table-symbol.cap-above {
  flex-direction: column;
}
math-type .matrix-symbol > matrix.table {
  width: 100%;
  flex-direction: column;
  align-items: stretch;
}
math-type .matrix-symbol > matrix.table > table {
  table-layout: fixed;
  border: unset;
  width: 100%;
}
math-type .matrix-symbol > matrix.table > table > tbody > tr > td {
  vertical-align: top;
  padding: 0;
  position: relative;
}
math-type .cell-border-box {
  position: absolute;
  left: -1px;
  top: -1px;
  width: 100%;
  height: 100%;
}
.table-caption > line.text-mode:first-child {
  display: inline;
}
.table-caption > line.text-mode:first-child > blocks {
  display: inline;
}

editarea.lttb.text-mode > line.text-mode > blocks {
  text-align: inherit;
}

/**start-ignore-save-as-html*/
.border-selection__line-section {
  cursor: pointer;
}
.border-selection__line-section:hover {
  background: rgba(135, 206, 250, 0.57);
}
.border-selection__column-section {
  cursor: pointer;
}
.border-selection__column-section:hover {
  background: rgba(135, 206, 250, 0.2);
}
/**end-ignore-save-as-html*/

math-type compositeblock.table-of-content {
  display: block;
  width: 100%;
}
math-type compositeblock.table-of-content > toc-wrapper {
  position: relative;
  width: 100%;
  display: block;
  cursor: default;
}
math-type compositeblock.table-of-content > toc-wrapper .toc-setting-icons {
  display: none;
  position: absolute;
  left: -65px;
  width: 70px;
  top: -20px;
  bottom: 0;
  font-size: 1em;
  z-index: 999;
  padding-top: 20px;
}
math-type compositeblock.table-of-content > toc-wrapper .toc-setting-icons > .icon-refresh,
math-type compositeblock.table-of-content > toc-wrapper .toc-setting-icons > .icon-settings {
  border: 1px solid #c3c2c2;
  padding: 0.3em;
  padding-top: 0.1em;
  line-height: 1.2;
  padding-bottom: 0;
  cursor: pointer;
  background-color: #f7f7f7;
  color: gray;
  width: 12px;
  display: inline-block;
  margin-left: 5px;
}
math-type compositeblock.table-of-content > toc-wrapper:hover .toc-setting-icons {
  display: block;
}
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode {
  width: 100%;
  cursor: default;
  display: block;
}
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode {
  display: block;
  width: auto;
  cursor: default;
  padding-left: 1em;
  padding-right: 15px;
  font-style: unset;
}
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode > a > blocks,
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode > blocks {
  display: block;
}
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode > a > prefix,
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode > prefix {
  display: none;
}
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode.has-rtl {
  padding-left: 15px;
  padding-right: 1em;
}
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode.has-rtl > a > blocks,
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode.has-rtl > blocks {
  text-align: right;
}
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode.has-rtl a > blocks > compositeblock.toc-page-number-symbol,
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode.has-rtl > blocks > compositeblock.toc-page-number-symbol {
  float: left;
  padding-left: 0;
  margin-right: 0;
  margin-left: 0;
  padding-right: 0.5em;
}
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode > block,
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode > a > block,
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode > blocks > block,
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode a > blocks > block {
  white-space: pre-wrap;
  white-space: break-spaces;
  cursor: pointer;
  font-style: unset;
}
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode > a > blocks > compositeblock.toc-page-number-symbol,
math-type compositeblock.table-of-content > toc-wrapper > editarea.toc-ea.text-mode > line.text-mode > blocks > compositeblock.toc-page-number-symbol {
  float: right;
  padding-left: 0.5em;
  margin-right: 0;
}

.icon-integral-symbol {
  margin: auto 4px auto auto;
  display: flex;
  flex-direction: row-reverse;
  font-size: 12px;
}
.icon-integral-symbol .square-up {
  border-width: 1px;
  margin-top: 0px;
}
.icon-integral-symbol .square-down {
  border-width: 1px;
  margin-top: 21px;
  margin-left: -10px;
}
.icon-integral-symbol .align-end {
  margin-top: 6px;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .limit-type {
  position: relative;
}
math-type .limit-type > add {
  opacity: 0.5;
  background-color: #f7f7f7;
  border: 1px solid #c3c2c2;
  color: gray;
  display: block;
  position: absolute;
  font-size: 0.6em;
  cursor: pointer;
  margin-left: -0.4em;
  z-index: 999;
  opacity: 0.7;
  top: -0.5em;
  left: 50%;
  top: -1.4em;
  left: 0.6em;
  align-items: flex-start;
}
math-type .limit-type > add:hover {
  border-color: gray;
  opacity: 1;
}
math-type .limit-type > add > i {
  display: block;
  height: 1em;
  padding: 0.1em 0.15em;
  padding-bottom: 0;
  width: 0.8em;
}
math-type .limit-type > .to,
math-type .limit-type > .from {
  float: left;
  clear: both;
}
math-type .limit-type > .to > line,
math-type .limit-type > .from > line,
math-type .limit-type > .to > editarea > line,
math-type .limit-type > .from > editarea > line {
  font-size: 0.7em;
}
math-type .limit-type > editarea-line,
math-type .limit-type > editarea-block {
  font-size: 0.7em;
}
math-type .limit-type > symbol {
  display: block;
  clear: both;
  pointer-events: none;
}
math-type .limit-type > symbol > span {
  white-space: nowrap;
  pointer-events: none;
}
math-type .limit-type > setting {
  background-color: #f7f7f7;
  border: 1px solid #c3c2c2;
  color: gray;
  opacity: 0.5;
  opacity: 1;
  position: absolute;
  left: 50%;
  top: -2em;
  font-size: 0.6em;
  margin-left: -0.6em;
  padding: 0 0.2em;
  cursor: pointer;
}
math-type .limit-type > setting:hover {
  border-color: gray;
  opacity: 1;
}
math-type .limit-type > setting:hover {
  border: 1px solid gray;
}
math-type .limit-type > setting > i {
  transform: rotate(-60deg);
}
math-type .limit-type.limit-kind > setting > i {
  transform: rotate(45deg);
}
math-type .limit-type.limit-kind > symbol {
  text-align: center;
  text-align-last: auto;
}
math-type .limit-type.limit-kind > .from,
math-type .limit-type.limit-kind > .to {
  width: 100%;
  text-align: center;
  text-align-last: auto;
}
math-type .limit-type.limit-kind > .from > line,
math-type .limit-type.limit-kind > .to > line {
  justify-content: center;
  text-align: center;
  text-align-last: auto;
}
math-type .limit-type.limit-kind > empty.from {
  height: 0;
  margin-top: -0.75em;
}
math-type .limit-type.limit-kind > empty.from > editarea.selected > line,
math-type .limit-type.limit-kind > empty.from > editarea-block.selected,
math-type .limit-type.limit-kind > empty.from > editarea-line.selected {
  background-color: white;
  text-align: center;
  text-align-last: auto;
}
math-type .limit-type.limit-kind > empty.to {
  height: 0;
  text-align: center;
  text-align-last: auto;
}
math-type .limit-type.limit-kind > empty.to > editarea.selected > line,
math-type .limit-type.limit-kind > empty.to > editarea-block.selected,
math-type .limit-type.limit-kind > empty.to > editarea-line.selected {
  background-color: white;
  text-align: center;
  text-align-last: auto;
}
math-type .integral-like-symbol.inline > symbol {
  font-size: 1em;
}
math-type .integral-like-symbol.int-ml {
  margin-left: -0.5em;
}
math-type .integral-like-symbol.oint-ml {
  margin-left: -0.3em;
}
math-type .integral-like-symbol > symbol {
  font-size: 1.5em;
}
math-type .integral-like-symbol > symbol > span {
  line-height: 1.4em;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .limit-type.lim-like-symbol {
  margin-left: 0.2em;
  margin-right: 0.2em;
}
math-type .limit-type.lim-like-symbol.close-left {
  margin-left: 0;
}
math-type .limit-type.lim-like-symbol.close-right {
  margin-right: 0;
}
math-type .limit-type.lim-like-symbol.limit-kind > empty.from > editarea {
  margin-top: -0.4em;
}
math-type .lim-like-symbol > add {
  left: 50%;
  top: -1em;
}
math-type .lim-like-symbol > symbol.inf {
  text-decoration: underline;
}
math-type .lim-like-symbol > symbol.sup {
  text-decoration: overline;
}
math-type .lim-like-symbol.non-limit-kind > editarea.from,
math-type .lim-like-symbol.non-limit-kind > editarea.to {
  margin-left: 1.47em;
}
math-type .lim-like-symbol.non-limit-kind > editarea.from.from-lim,
math-type .lim-like-symbol.non-limit-kind > editarea.to.to-lim {
  margin-left: 1.5435em;
}
math-type .lim-like-symbol.non-limit-kind > editarea.from.from-liminf,
math-type .lim-like-symbol.non-limit-kind > editarea.to.to-liminf {
  margin-left: 3.15em;
}
math-type .lim-like-symbol.non-limit-kind > editarea.from.from-limsup,
math-type .lim-like-symbol.non-limit-kind > editarea.to.to-limsup {
  margin-left: 3.528em;
}
math-type .lim-like-symbol.non-limit-kind > editarea.from.from-sup,
math-type .lim-like-symbol.non-limit-kind > editarea.to.to-sup {
  margin-left: 1.6905em;
}
math-type .lim-like-symbol.non-limit-kind > editarea.from.from-varlimsup,
math-type .lim-like-symbol.non-limit-kind > editarea.to.to-varlimsup {
  margin-left: 1.5435em;
}
math-type .lim-like-symbol.non-limit-kind > editarea-block.from,
math-type .lim-like-symbol.non-limit-kind > editarea-block.to {
  margin-left: 2.072em;
}
math-type .lim-like-symbol.non-limit-kind > editarea-block.from.from-lim,
math-type .lim-like-symbol.non-limit-kind > editarea-block.to.to-lim {
  margin-left: 2.1756em;
}
math-type .lim-like-symbol.non-limit-kind > editarea-block.from.from-liminf,
math-type .lim-like-symbol.non-limit-kind > editarea-block.to.to-liminf {
  margin-left: 4.44em;
}
math-type .lim-like-symbol.non-limit-kind > editarea-block.from.from-limsup,
math-type .lim-like-symbol.non-limit-kind > editarea-block.to.to-limsup {
  margin-left: 4.9728em;
}
math-type .lim-like-symbol.non-limit-kind > editarea-block.from.from-sup,
math-type .lim-like-symbol.non-limit-kind > editarea-block.to.to-sup {
  margin-left: 2.3828em;
}
math-type .lim-like-symbol.non-limit-kind > editarea-block.from.from-varlimsup,
math-type .lim-like-symbol.non-limit-kind > editarea-block.to.to-varlimsup {
  margin-left: 2.1756em;
}

/**start-ignore-save-as-html*/
.icon-lim {
  margin: auto 4px auto auto;
  display: flex;
  flex-direction: column;
  font-size: 12px;
}
.icon-lim .square-up {
  border-width: 1px;
  margin: auto;
}
.icon-lim .square-down {
  border-width: 1px;
  margin: auto;
}
.icon-lim .align-center {
  margin: auto;
}
.icon-lim .align-center.border-top {
  border-top: 1px solid black;
  margin-top: 2px;
}
.icon-lim .align-center.border-bottom {
  border-bottom: 1px solid black;
  margin-bottom: 2px;
}
/**end-ignore-save-as-html*/

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .limit-type.summation-like-symbol.limit-kind > empty.from {
  margin-top: -0.3em;
}
math-type .limit-type.summation-like-symbol.limit-kind > empty.from > editarea,
math-type .limit-type.summation-like-symbol.limit-kind > empty.from > editarea-block {
  margin-top: -0.2em;
}
math-type .summation-like-symbol.inline > add {
  top: -1.1em;
}
math-type .summation-like-symbol.inline > symbol {
  font-size: 1em;
}
math-type .summation-like-symbol.inline > symbol > span {
  vertical-align: 0.15em;
}
math-type .summation-like-symbol {
  padding-right: 0.15em;
}
math-type .summation-like-symbol > symbol {
  font-size: 1.5em;
}
math-type .summation-like-symbol > symbol > span {
  vertical-align: 0.1em;
  line-height: 1.4em;
}
math-type .summation-like-symbol > add {
  top: -1.5em;
}
math-type .summation-like-symbol > .custom-symbol {
  font-weight: bold;
}

hcomposed-symbol {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
hcomposed-symbol > start {
  display: block;
  float: left;
  overflow: hidden;
}
hcomposed-symbol > middle {
  display: block;
  flex-grow: 1;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}
hcomposed-symbol > middle > fixed {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
}
hcomposed-symbol > middle > fixed > inside {
  display: inline-block;
  overflow: hidden;
}
hcomposed-symbol > end {
  display: block;
  overflow: hidden;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .arrow-like-symbol {
  padding: 0 0.2em;
}
math-type .arrow-like-symbol > arrow-like-simple > add {
  opacity: 0.5;
  background-color: #f7f7f7;
  border: 1px solid #c3c2c2;
  color: gray;
  display: block;
  position: absolute;
  font-size: 0.6em;
  cursor: pointer;
  margin-left: -0.4em;
  z-index: 999;
  opacity: 0.7;
  top: -0.5em;
  left: 50%;
  left: 0.8em;
  top: -0.7em;
}
math-type .arrow-like-symbol > arrow-like-simple > add:hover {
  border-color: gray;
  opacity: 1;
}
math-type .arrow-like-symbol > arrow-like-simple > add > i {
  display: block;
  height: 1em;
  padding: 0.1em 0.15em;
  padding-bottom: 0;
  width: 0.8em;
}
math-type .arrow-like-symbol > middle {
  display: block;
  text-align: center;
  text-align-last: auto;
  clear: both;
  width: 100%;
  position: relative;
}
math-type .arrow-like-symbol > middle > inside {
  display: inline-block;
  height: 0.55em;
  width: 100%;
}
math-type .arrow-like-symbol > middle > inside > hcomposed-symbol {
  position: absolute;
  top: 0.4em;
  left: 0;
}
math-type .arrow-like-symbol > editarea.sright-1 {
  padding-left: 0.55em;
  padding-right: 0.25em;
}
math-type .arrow-like-symbol > editarea.sleft-1 {
  padding-left: 0.25em;
  padding-right: 0.55em;
}
math-type .arrow-like-symbol > editarea.small-margin {
  padding-left: 0.4em;
  padding-right: 0.4em;
}
math-type .arrow-like-symbol > editarea.large-padding {
  margin: 0 1.3em;
}
math-type .arrow-like-symbol > editarea > line {
  font-size: 0.7em;
}
math-type .arrow-like-symbol > .top {
  float: left;
  width: 100%;
  clear: both;
  margin-bottom: -0.5em;
  z-index: 1;
  position: relative;
}
math-type .arrow-like-symbol > .top.space-1,
math-type .arrow-like-symbol > .top.up-space-1-lower-space-2 {
  margin-bottom: -0.4em;
}
math-type .arrow-like-symbol > .bottom {
  margin-top: -0.45em;
  float: left;
  width: 100%;
}
math-type .arrow-like-symbol > .bottom.space-1,
math-type .arrow-like-symbol > .bottom.lower-space-1 {
  margin-top: -0.35em;
}
math-type .arrow-like-symbol > .bottom.space-2,
math-type .arrow-like-symbol > .bottom.up-space-1-lower-space-2 {
  margin-top: -0.25em;
}

.over-icon {
  margin: auto;
  width: 12px;
  font-size: 13px;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .over-arrow-symbol > arrow-symbol {
  display: block;
  margin-right: -0.05em;
  position: relative;
}
math-type .over-arrow-symbol > arrow-symbol > hcomposed-symbol {
  position: absolute;
}
math-type .over-arrow-symbol > arrow-symbol > arrow-wrapper {
  margin-right: -0.2em;
}
math-type .over-arrow-symbol > editarea {
  display: inline-flex;
  text-align: center;
  text-align-last: auto;
}
math-type .over-arrow-symbol > editarea > area-container > line,
math-type .over-arrow-symbol > editarea > line {
  justify-content: center;
  text-align: center;
  text-align-last: auto;
}
math-type .over-arrow-symbol > editarea-block {
  min-width: 0.5em;
  text-align: center;
  text-align-last: auto;
  display: inline-block;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .over-line-symbol {
  margin-left: 0.05em;
  margin-right: 0.05em;
}
math-type .over-line-symbol > editarea {
  display: inline-flex;
  min-width: 0.5em;
}
math-type .over-line-symbol > editarea-line {
  min-width: 0.5em;
  display: inline-block;
  text-align: center;
  text-align-last: auto;
}
math-type .over-line-symbol > line-border {
  display: block;
  position: relative;
}
math-type .over-line-symbol > line-border > hcomposed-symbol {
  position: absolute;
}
math-type .over-line-symbol > line-border > svg {
  position: absolute;
  width: 100%;
  height: 100%;
}

brace-top-wrapper {
  display: flex;
  flex-direction: row;
  height: 100%;
  font-family: Asana-Math, Asana;
}
brace-top-wrapper > middle-wrapper {
  flex-grow: 1;
  position: relative;
  overflow: hidden;
}
brace-top-wrapper > middle-wrapper > fixed {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
}
brace-top-wrapper > middle-wrapper > fixed > middle-inside {
  overflow: hidden;
}
brace-top-wrapper first,
brace-top-wrapper middle,
brace-top-wrapper last {
  overflow: hidden;
  position: relative;
}
brace-top-wrapper first {
  min-width: 0.7em;
  width: 0.7em;
}
brace-top-wrapper > middle {
  min-width: 0.6em;
  width: 0.6em;
}
brace-top-wrapper > last {
  min-width: 0.7em;
  width: 0.7em;
}
bracket-top-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  font-family: Asana-Math, Asana;
}
bracket-top-wrapper > first,
bracket-top-wrapper middle,
bracket-top-wrapper last {
  overflow: hidden;
  position: relative;
}
bracket-top-wrapper > first {
  height: 0.5em;
  min-height: 0.5em;
}
bracket-top-wrapper middle-wrapper {
  flex-grow: 1;
  position: relative;
  overflow: hidden;
}
bracket-top-wrapper middle-wrapper > fixed {
  overflow: hidden;
  position: absolute;
  left: 0;
  top: 0;
}
bracket-top-wrapper middle-wrapper > fixed > middle {
  display: block;
  overflow: hidden;
}
bracket-top-wrapper > last {
  height: 0.65em;
  min-height: 0.65em;
}
parenthesis-top-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  font-family: Asana-Math, Asana;
}
parenthesis-top-wrapper > first,
parenthesis-top-wrapper middle,
parenthesis-top-wrapper last {
  overflow: hidden;
  position: relative;
  height: 1em;
  min-height: 1em;
}
parenthesis-top-wrapper > first {
  min-height: 0.9em;
  height: 0.9em;
}
parenthesis-top-wrapper middle-wrapper {
  flex-grow: 1;
  position: relative;
  overflow: hidden;
}
parenthesis-top-wrapper middle-wrapper > fixed {
  position: absolute;
  top: 0;
  left: 0;
}
parenthesis-top-wrapper middle-wrapper > fixed > middle {
  display: block;
  overflow: hidden;
}
parenthesis-top-wrapper > last {
  min-height: 1em;
  height: 1em;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .over-brace-symbol {
  position: relative;
}
math-type .over-brace-symbol > add {
  opacity: 0.5;
  background-color: #f7f7f7;
  border: 1px solid #c3c2c2;
  color: gray;
  display: block;
  position: absolute;
  font-size: 0.6em;
  cursor: pointer;
  margin-left: -0.4em;
  z-index: 999;
  opacity: 0.7;
  top: -0.5em;
  left: 50%;
  top: -1.5em;
  margin-left: -0.6em;
}
math-type .over-brace-symbol > add:hover {
  border-color: gray;
  opacity: 1;
}
math-type .over-brace-symbol > add > i {
  display: block;
  height: 1em;
  padding: 0.1em 0.15em;
  padding-bottom: 0;
  width: 0.8em;
}
math-type .over-brace-symbol > top-brace {
  pointer-events: none;
  display: block;
  min-width: 1.1em;
  position: relative;
  clear: both;
  margin-bottom: -0.1em;
  font-style: normal;
  font-weight: normal;
}
math-type .over-brace-symbol > top-brace > brace-top-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  left: 50%;
  transform: translate(-50%, 0);
}
math-type .over-brace-symbol > editarea {
  display: inline-flex;
  width: 100%;
  align-self: center;
  clear: both;
}
math-type .over-brace-symbol > editarea.overValue {
  font-size: 0.7em;
  float: left;
  width: 100%;
  margin-bottom: 0.1em;
}

.icon-hat {
  margin: auto;
  width: 12px;
  font-size: 12px;
}
.icon-hat .square {
  margin-top: -3px;
}

.under-arc-symbol {
  display: inline-block;
}
.under-arc-symbol > editarea {
  display: inline-flex;
}
.under-arc-symbol > symbol {
  display: block;
  width: 100%;
  position: relative;
}
.under-arc-symbol > symbol > svg {
  position: absolute;
  left: 0;
  top: 0;
  display: block;
  width: 100%;
  height: 100%;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .under-line-symbol > editarea {
  display: inline-flex;
}
math-type .under-line-symbol > line-border {
  display: block;
  position: relative;
  float: left;
  width: 100%;
}
math-type .under-line-symbol > line-border > svg {
  position: absolute;
  width: 100%;
  height: 100%;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type compositeblock.under-brace-symbol {
  display: inline-flex;
  flex-flow: column;
  position: relative;
}
math-type compositeblock.under-brace-symbol > add {
  opacity: 0.5;
  background-color: #f7f7f7;
  border: 1px solid #c3c2c2;
  color: gray;
  display: block;
  position: absolute;
  font-size: 0.6em;
  cursor: pointer;
  margin-left: -0.4em;
  z-index: 999;
  opacity: 0.7;
  top: -0.5em;
  left: 50%;
  top: 3em;
}
math-type compositeblock.under-brace-symbol > add:hover {
  border-color: gray;
  opacity: 1;
}
math-type compositeblock.under-brace-symbol > add > i {
  display: block;
  height: 1em;
  padding: 0.1em 0.15em;
  padding-bottom: 0;
  width: 0.8em;
}
math-type compositeblock.under-brace-symbol > bottom-brace {
  pointer-events: none;
  display: block;
  position: relative;
  min-width: 1.3em;
  font-style: normal;
  font-weight: normal;
}
math-type compositeblock.under-brace-symbol > bottom-brace > brace-top-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  left: 50%;
  transform: translate(-50%, 0);
}
math-type compositeblock.under-brace-symbol > editarea {
  margin-left: 0.15em;
  margin-right: 0.15em;
  align-self: center;
}
math-type compositeblock.under-brace-symbol > editarea.underValue {
  font-size: 0.7em;
}
.under-brace-icon {
  width: 12px;
  margin: auto;
  font-size: 12px;
}
.under-brace-icon .brace-wrapper {
  margin-top: -14px;
}


math-type .operator-name > editarea {
  margin-left: 0.2em;
  margin-right: 0.2em;
  display: inline-flex;
}
math-type .operator-name > editarea.close-left {
  margin-left: 0;
}
math-type .operator-name > editarea.close-right {
  margin-right: 0;
}

.math-group > editarea {
  float: left;
}
.group-icon .rectangle {
  width: 23px;
  height: 12px;
  border-style: solid;
  border-width: 1px;
  text-align: center;
  border-color: gray;
  font-family: fantasy;
  color: black;
  padding-top: 1px;
  font-size: 9px;
  line-height: 14px;
  vertical-align: middle;
  margin: auto;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type compositeblock.over-symbol {
  position: relative;
}
math-type compositeblock.over-symbol > symbol {
  font-family: Asana-Math, Asana, Arial;
  height: 8em;
  display: block;
  text-align: center;
  text-align-last: auto;
  position: relative;
}
math-type compositeblock.over-symbol > symbol > inner {
  display: block;
  position: absolute;
  text-align: center;
  text-align-last: auto;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
math-type compositeblock.over-symbol > symbol.dot > inner.left-sign,
math-type compositeblock.over-symbol > symbol.ring > inner.left-sign {
  transform: translate(-0.07em, 0);
}
math-type compositeblock.over-symbol > symbol.dot > inner.right-sign,
math-type compositeblock.over-symbol > symbol.ring > inner.right-sign {
  transform: translate(0.2em, 0);
}
math-type compositeblock.over-symbol > symbol.dot > inner,
math-type compositeblock.over-symbol > symbol.ring > inner {
  transform: translate(0.05em, 0);
}
math-type compositeblock.over-symbol > symbol.small-hat > inner.left-sign,
math-type compositeblock.over-symbol > symbol.small-tilde > inner.left-sign {
  transform: translate(-0.04em, 0);
}
math-type compositeblock.over-symbol > symbol.small-hat > inner.right-sign,
math-type compositeblock.over-symbol > symbol.small-tilde > inner.right-sign {
  transform: translate(0.15em, 0);
}
math-type compositeblock.over-symbol > symbol.small-hat > inner.ignore-shift,
math-type compositeblock.over-symbol > symbol.small-tilde > inner.ignore-shift {
  transform: translate(0, 0);
}
math-type compositeblock.over-symbol > symbol.small-hat > inner,
math-type compositeblock.over-symbol > symbol.small-tilde > inner {
  transform: translate(0.05em, 0);
}
math-type compositeblock.over-symbol > symbol.acute > inner.left-sign {
  transform: translate(-0.04em, 0);
}
math-type compositeblock.over-symbol > symbol.acute > inner.right-sign {
  transform: translate(0.25em, 0);
}
math-type compositeblock.over-symbol > symbol.acute > inner {
  transform: translate(0.1em, 0);
}
math-type compositeblock.over-symbol > symbol.grave,
math-type compositeblock.over-symbol > symbol.breve,
math-type compositeblock.over-symbol > symbol.acute {
  font-family: 'Times New Roman', Times, serif;
}
math-type compositeblock.over-symbol > symbol.grave > inner.left-sign,
math-type compositeblock.over-symbol > symbol.breve > inner.left-sign,
math-type compositeblock.over-symbol > symbol.check > inner.left-sign {
  transform: translate(-0.07em, 0);
}
math-type compositeblock.over-symbol > symbol.grave > inner.right-sign,
math-type compositeblock.over-symbol > symbol.breve > inner.right-sign,
math-type compositeblock.over-symbol > symbol.check > inner.right-sign {
  transform: translate(0.17em, 0);
}
math-type compositeblock.over-symbol > symbol.ddddot {
  min-width: 0.9em;
}
math-type compositeblock.over-symbol > symbol.dddot {
  min-width: 0.7em;
}

.math-xx > editarea {
  float: left;
}

math-type .big-delimiter-symbol {
  display: inline-flex;
  align-items: center;
  position: relative;
  margin: 0.1em 0;
}
math-type .big-delimiter-symbol big-delimiter.big-open {
  margin-left: 0.09em;
}
math-type .big-delimiter-symbol big-delimiter.big-close {
  margin-right: 0.07em;
}
math-type .big-delimiter-symbol > base-line-indicator {
  height: inherit;
}
math-type .big-delimiter-symbol > big-delimiter > svg {
  height: 100%;
  width: 100%;
}
math-type .big-delimiter-symbol > big-delimiter > bulb {
  left: -10px;
}
math-type .big-delimiter-symbol > big-delimiter > setting {
  width: 158px;
  display: flex;
  position: absolute;
  left: 0;
  top: -40px;
}
math-type .big-delimiter-symbol > big-delimiter > setting > .select-box-container {
  margin-right: 4px;
}
math-type .big-delimiter-symbol > big-delimiter > setting > .select-box-container:last-child {
  margin-right: 0px;
}

select-buttons {
  font-size: 13px;
  color: gray;
  display: block;
}
select-buttons > sb-item {
  display: inline-block;
  padding: 2px 2px;
  cursor: pointer;
  border: 1px solid transparent;
  /*box-sizing: border-box;*/
}
select-buttons > sb-item.selected {
  background-color: white;
  border: 1px solid lightgray;
}
select-buttons > sb-item:hover {
  border: 1px solid lightgray;
}
select-buttons > sb-item.disabled {
  opacity: 0.3;
  cursor: default;
  border: 1px solid transparent;
}
select-buttons > sb-item.disabled:hover {
  background-color: inherit;
  border: 1px solid transparent;
}

tabs-container tab-item {
  color: gray;
}
tabs-container tab-item:hover {
  color: black;
}

expandable-component.button-action {
  cursor: pointer;
  color: gray;
  fill: gray;
}
expandable-component.button-action:hover,
expandable-component.button-action.selected {
  color: black;
  fill: black;
}
expandable-component.button-action:hover label-display,
expandable-component.button-action.selected label-display {
  border: 1px solid #e2e0e0;
}
expandable-component.button-action label-display {
  font-size: 12px;
  padding-top: 5px;
  display: flex;
  border: 1px solid transparent;
  padding-left: 2px;
  padding-right: 2px;
  margin-top: 0px;
  padding-bottom: 4px;
  cursor: pointer;
}
expandable-component.button-action label-display > i {
  padding-left: 5px;
  padding-top: 1px;
}
expandable-component.button-action label-item-container > label-item:hover {
  background-color: #bfe4bd;
}
expandable-component.button-action label-item-container > label-item.ignore-select {
  background-color: inherit;
  cursor: default;
}
expandable-component.button-action.button-style label-display {
  background: white;
  border: 1px solid lightgray;
}
expandable-component.button-action.button-style:hover,
expandable-component.button-action.button-style.selected {
  color: gray;
}
expandable-component.button-action.button-style:hover label-display,
expandable-component.button-action.button-style.selected label-display {
  background: #f7f6f6;
}

g.group-box-move {
  opacity: 0.2;
}
g.group-box-move:hover {
  opacity: 0.8;
}

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
item.setting {
  outline: none;
  color: gray;
  cursor: pointer;
  border: 1px solid transparent;
  position: relative;
  height: 23px;
  display: block;
  width: 30px;
}
item.setting.selected {
  background: white;
  border: 1px solid lightgray;
}
item.setting:hover {
  border: 1px solid lightgray;
}
item.setting svg {
  width: 100%;
  height: 100%;
}
expandable-component pair-option {
  display: flex;
  flex-direction: row;
}
math-type diagram-settings,
.role-toolbar-wrapper diagram-settings {
  width: 650px;
  background: #f7f7f7;
  margin-top: 2px;
  height: 25px;
  padding-top: 3px;
  padding-bottom: 3px;
  display: block;
  font-size: 13px;
  font-family: "Segoe UI", Arial, Verdana, sans-serif;
}
math-type diagram-settings rest,
.role-toolbar-wrapper diagram-settings rest {
  display: flex;
  justify-content: flex-end;
}
math-type diagram-settings numeric-slider,
.role-toolbar-wrapper diagram-settings numeric-slider {
  padding-top: 2px;
}
math-type diagram-settings main,
.role-toolbar-wrapper diagram-settings main {
  display: flex;
  flex-direction: row;
}
math-type diagram-settings inside,
.role-toolbar-wrapper diagram-settings inside {
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;
}
math-type diagram-settings id-item,
.role-toolbar-wrapper diagram-settings id-item {
  color: gray;
  font-size: 10px;
  padding-left: 5px;
  padding-top: 7px;
  margin-right: 5px;
  width: 30px;
}
math-type diagram-settings item,
.role-toolbar-wrapper diagram-settings item {
  outline: none;
  color: gray;
  cursor: pointer;
  border: 1px solid transparent;
  position: relative;
  height: 23px;
  display: block;
  width: 30px;
}
math-type diagram-settings item.selected,
.role-toolbar-wrapper diagram-settings item.selected {
  background: white;
  border: 1px solid lightgray;
}
math-type diagram-settings item:hover,
.role-toolbar-wrapper diagram-settings item:hover {
  border: 1px solid lightgray;
}
math-type diagram-settings item svg,
.role-toolbar-wrapper diagram-settings item svg {
  width: 100%;
  height: 100%;
}
math-type diagram-settings .quadratic-line-select,
.role-toolbar-wrapper diagram-settings .quadratic-line-select,
math-type diagram-settings .cubic-line-select,
.role-toolbar-wrapper diagram-settings .cubic-line-select {
  display: block;
}
math-type diagram-settings item-group,
.role-toolbar-wrapper diagram-settings item-group {
  display: flex;
}
math-type diagram-settings .control-point-removing,
.role-toolbar-wrapper diagram-settings .control-point-removing {
  margin-left: 0;
}
math-type diagram-settings separator,
.role-toolbar-wrapper diagram-settings separator {
  display: block;
  position: absolute;
  height: 22px;
  top: 5px;
  border-left: 1px solid lightgray;
  width: 1px;
}
math-type diagram-settings relative-separator,
.role-toolbar-wrapper diagram-settings relative-separator {
  display: block;
  position: relative;
  margin-left: 5px;
  margin-right: 6px;
}
math-type diagram-settings relative-separator > bar,
.role-toolbar-wrapper diagram-settings relative-separator > bar {
  display: block;
  position: absolute;
  height: 22px;
  top: 2px;
  border-left: 1px solid lightgray;
  width: 1px;
}
math-type diagram-settings .id-separator,
.role-toolbar-wrapper diagram-settings .id-separator {
  left: 35px;
}
math-type diagram-settings .arrow-end-separator,
.role-toolbar-wrapper diagram-settings .arrow-end-separator {
  left: 106px;
}
math-type diagram-settings .arrow-parts-separator,
.role-toolbar-wrapper diagram-settings .arrow-parts-separator {
  left: 186px;
}
math-type diagram-settings .intersect-detail,
.role-toolbar-wrapper diagram-settings .intersect-detail {
  display: block;
  width: 15px;
  height: 100%;
}
math-type diagram-settings intersection-types,
.role-toolbar-wrapper diagram-settings intersection-types {
  display: flex;
  flex-direction: row;
}
math-type diagram-settings intersections-details,
.role-toolbar-wrapper diagram-settings intersections-details {
  display: flex;
  flex-direction: row;
}
math-type diagram-settings intersections-details slider,
.role-toolbar-wrapper diagram-settings intersections-details slider {
  width: 70px;
  margin-left: 3px;
  margin-right: 3px;
}
math-type diagram-settings .intersection-type,
.role-toolbar-wrapper diagram-settings .intersection-type {
  width: 25px;
}
math-type diagram-settings .intersection-type svg,
.role-toolbar-wrapper diagram-settings .intersection-type svg {
  stroke: gray;
  stroke-width: 1px;
  fill: none;
}
math-type diagram-settings check-box-wrapper,
.role-toolbar-wrapper diagram-settings check-box-wrapper {
  margin-top: 8px;
}
/**end-ignore-save-as-html*/


.math-diagram .intersections-group {
  stroke-width: 2px;
  stroke: black;
  fill: transparent;
  cursor: move;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type compositeblock.math-diagram > math-diagram {
  background: white;
}
math-type compositeblock.math-diagram {
  cursor: default;
  width: 100%;
  /** display block as if inline block, it will be disapear in some cases*/
  display: block;
}
math-type compositeblock.math-diagram .connection-point {
  cursor: crosshair;
}
math-type compositeblock.math-diagram .connection-point g {
  fill-opacity: 0.6;
}
math-type compositeblock.math-diagram .connection-point:hover g {
  fill-opacity: 1;
}
math-type compositeblock.math-diagram .text-diagram-editor editarea.text-mode.non-wrap > line.text-mode {
  font-size: unset;
}
math-type compositeblock.math-diagram .text-diagram-editor editarea.text-mode.non-wrap > line.text-mode > block {
  white-space: pre;
}
math-type compositeblock.math-diagram .text-diagram-editor editarea.text-mode.non-wrap > line.text-mode > blocks > block {
  white-space: pre;
}
math-type compositeblock.math-diagram > math-diagram {
  border: 1px solid transparent;
}
math-type compositeblock.math-diagram > math-diagram > clip-region > zoom-region {
  display: block;
  width: 100%;
  height: 100%;
}
math-type compositeblock.math-diagram > math-diagram > clip-region > zoom-region > svg {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
math-type compositeblock.math-diagram .add-label {
  fill: gray;
}
math-type compositeblock.math-diagram .add-label:hover {
  fill: black;
}
math-type compositeblock.math-diagram .diagram-editor {
  margin: 0 2px;
}
math-type compositeblock.math-diagram .control-point-guide {
  stroke: lightgray;
  stroke-width: 0.5px;
  pointer-events: none;
}
math-type compositeblock.math-diagram .connection-group {
  stroke: black;
  fill: none;
}
math-type compositeblock.math-diagram .arrow-line {
  cursor: move;
}
math-type compositeblock.math-diagram .mobile-tablet .shape .transparent,
math-type compositeblock.math-diagram .mobile-tablet .composite-shape .transparent {
  stroke-width: 10px;
}
math-type compositeblock.math-diagram .shape,
math-type compositeblock.math-diagram .composite-shape {
  stroke-width: 1px;
  fill: none;
  cursor: move;
}
math-type compositeblock.math-diagram .shape .transparent,
math-type compositeblock.math-diagram .composite-shape .transparent {
  pointer-events: visiblePainted;
  stroke-width: 6px;
  stroke: transparent;
  stroke-dasharray: none;
  fill: none;
}
math-type compositeblock.math-diagram .mobile-tablet .connection.transparent,
math-type compositeblock.math-diagram .mobile-tablet .connection.transparent > line,
math-type compositeblock.math-diagram .mobile-tablet .connection.transparent > path {
  stroke-width: 10px;
}
math-type compositeblock.math-diagram .connection {
  stroke-width: 1px;
  fill: none;
  cursor: move;
}
math-type compositeblock.math-diagram .connection.transparent,
math-type compositeblock.math-diagram .connection.transparent > line,
math-type compositeblock.math-diagram .connection.transparent > path {
  pointer-events: visiblePainted;
  stroke-width: 6px;
  stroke: transparent;
  stroke-dasharray: none;
  fill: none;
}
math-type compositeblock.math-diagram .frame {
  stroke-width: 1px;
  fill: transparent;
}
math-type math-diagram {
  display: block;
  position: relative;
  width: 100%;
  height: 300px;
}
math-type math-diagram position-container {
  position: absolute;
  width: 1px;
  height: 1px;
  display: block;
}
math-type math-diagram dg-editor-container {
  position: absolute;
  min-width: 0.85em;
  border-collapse: border-box;
}
math-type math-diagram coverlayer {
  position: absolute;
  background: transparent;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  cursor: move;
}
math-type math-diagram connection-point {
  position: absolute;
  display: block;
  height: 6px;
  width: 6px;
  background: lightgray;
  border: 1px solid gray;
  bottom: -10px;
  left: 50%;
  margin-left: -3px;
  cursor: e-resize;
}
math-type .diagram-caption > line.text-mode:first-child {
  display: inline;
}
math-type .diagram-caption > line.text-mode:first-child > blocks {
  display: inline;
}
/**start-ignore-save-as-html*/
math-type diagram-bar-region {
  display: block;
}
math-type diagram-bar {
  display: flex;
  flex-direction: column;
  font-size: 13px;
  font-family: "Segoe UI", Arial, Verdana, sans-serif;
}
math-type diagram-bar svg {
  position: absolute;
}
math-type diagram-bar item {
  cursor: default;
  padding: 5px;
  border: 1px transparent solid;
  color: gray;
}
math-type diagram-bar item:hover,
math-type diagram-bar item.selected {
  border: 1px #e6e5e5 solid;
  background: white;
}
math-type diagram-bar .text-field,
math-type diagram-bar .arrow {
  font-size: 12px;
}
math-type diagram-bar .text-field:hover {
  color: black;
}
math-type diagram-bar .arrow {
  font-weight: bold;
  display: inline-flex;
  flex-direction: row;
  position: relative;
}
math-type diagram-bar .arrow arrow-container {
  font-size: 16px;
  display: block;
  width: 14px;
}
math-type diagram-bar .arrow arrow-container:hover {
  color: black;
}
math-type diagram-bar .arrow inside {
  position: absolute;
  top: 1px;
  left: 3px;
}
math-type diagram-bar .shape {
  position: relative;
  display: flex;
  flex-direction: row;
}
math-type diagram-bar .shape selected-shape {
  width: 13px;
  display: block;
  position: relative;
  height: 14px;
}
math-type diagram-bar .shape selected-shape svg {
  width: 100%;
  height: 100%;
  stroke: gray;
}
math-type diagram-bar .shape selected-shape svg:hover {
  stroke: black;
}
math-type diagram-bar .shape selected-shape circle {
  fill: transparent;
  stroke-width: 1px;
}
.plot-settings-icon-wrapper {
  color: #b5b4b4;
  border: 1px solid #b5b4b4;
}
.plot-settings-icon-wrapper:hover {
  color: gray;
  border: 1px solid gray;
}
/**end-ignore-save-as-html*/

.plot-input-style-settings {
  opacity: 0.5;
}
.plot-input-style-settings:hover {
  opacity: 1;
}

.psa-cn .ps-hint {
  opacity: 0;
  transition: opacity 0.3s linear;
}
.psa-cn:hover .ps-hint {
  opacity: 0.6;
}

.role-toolbar-wrapper.dg-full-view tool-bar too-bar-container {
  justify-content: start;
  width: auto;
  min-width: 0;
}
.role-toolbar-wrapper.dg-full-view diagram-settings {
  width: auto;
}

.dg-items-bar-container item {
  display: inline-block;
  padding: 3px;
  fill: gray;
  stroke: gray;
}
.dg-items-bar-container item,
.dg-items-bar-container item-option {
  border: 1px transparent solid;
}
.dg-items-bar-container item:hover,
.dg-items-bar-container item-option:hover {
  border: 1px #c4c1c1 solid;
  background: white;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type compositeblock.theorem {
  display: block;
}
math-type compositeblock.theorem.selected {
  outline: 1px solid rgba(93, 92, 92, 0.3);
}
math-type theorem-name {
  min-width: 20px;
  display: inline;
}
math-type theorem-name > line.text-mode:first-child {
  display: inline;
}
math-type editarea.text-mode.edit-theorem-content {
  min-width: 100px;
  display: inline;
  font-style: italic;
}
math-type editarea.text-mode.edit-theorem-content > line.text-mode:first-child {
  display: inline;
}
math-type editarea.text-mode.edit-theorem-content > line.text-mode:first-child > blocks {
  display: inline;
}
math-type editarea.text-mode.edit-theorem-content > line.text-mode > block {
  font-style: italic;
}
math-type editarea.text-mode.edit-theorem-content > line.text-mode > blocks > block {
  font-style: italic;
}
math-type editarea.text-mode.edit-theorem-content.normal > line.text-mode > block {
  font-style: normal;
}
math-type editarea.text-mode.edit-theorem-content.normal > line.text-mode > blocks > block {
  font-style: normal;
}
math-type editarea.text-mode.edit-theorem-content .qed-symbol {
  line-height: 1em;
}
.deletable {
  cursor: pointer;
}
.deletable:hover {
  color: red;
}

.regular-checkbox:hover {
  background-color: #f3f2f2;
}

@media print {
  code[class*="language-"],
  pre[class*="language-"] {
    text-shadow: none !important;
  }
}
math-type .code-section prefix.code-line-number {
  color: #a9a7a7;
  width: 2.5em;
  display: inline-block;
  flex-shrink: 0;
}
math-type .code-section prefix.code-line-number.prefix-l10 {
  width: 1.5em;
}
math-type .code-section prefix.code-line-number.prefix-l100 {
  width: 2em;
}
math-type .code-section .code-line-blocks-container {
  white-space: pre-wrap;
  white-space: break-spaces;
}
math-type .code-section .code-line-blocks-container block.token {
  white-space: pre-wrap;
  white-space: break-spaces;
}
math-type .code-section line.text-mode.code-section-line {
  display: flex;
  flex-direction: row;
  line-height: 1.5;
}

/**
 * prism.js default theme for JavaScript, CSS and HTML
 * Based on dabblet (http://dabblet.com)
 * <AUTHOR> Verou
 */
.code-section.theme-default editarea {
  color: black;
}
.code-section.theme-default .token.comment,
.code-section.theme-default .token.prolog,
.code-section.theme-default .token.doctype,
.code-section.theme-default .token.cdata {
  color: slategray;
}
.code-section.theme-default .token.punctuation {
  color: #999;
}
.code-section.theme-default .namespace {
  opacity: 0.7;
}
.code-section.theme-default .token.property,
.code-section.theme-default .token.tag,
.code-section.theme-default .token.boolean,
.code-section.theme-default .token.number,
.code-section.theme-default .token.constant,
.code-section.theme-default .token.symbol,
.code-section.theme-default .token.deleted {
  color: #905;
}
.code-section.theme-default .token.selector,
.code-section.theme-default .token.attr-name,
.code-section.theme-default .token.string,
.code-section.theme-default .token.char,
.code-section.theme-default .token.builtin,
.code-section.theme-default .token.inserted {
  color: #690;
}
.code-section.theme-default .token.operator,
.code-section.theme-default .token.entity,
.code-section.theme-default .token.url,
.code-section.theme-default .language-css .token.string,
.code-section.theme-default .style .token.string {
  color: #9a6e3a;
}
.code-section.theme-default .token.atrule,
.code-section.theme-default .token.attr-value,
.code-section.theme-default .token.keyword {
  color: #07a;
}
.code-section.theme-default .token.function,
.code-section.theme-default .token.class-name {
  color: #DD4A68;
}
.code-section.theme-default .token.regex,
.code-section.theme-default .token.important,
.code-section.theme-default .token.important1,
.code-section.theme-default .token.variable {
  color: #e90;
}
.code-section.theme-default .token.important,
.code-section.theme-default .token.bold,
.code-section.theme-default .token.bold1 {
  font-weight: bold;
}
.code-section.theme-default .token.italic,
.code-section.theme-default .token.italic1 {
  font-style: italic;
}
.code-section.theme-default .token.entity {
  cursor: help;
}

/**
 * prism.js default theme for JavaScript, CSS and HTML
 * Based on dabblet (http://dabblet.com)
 * <AUTHOR> Verou
 */
.code-section.theme-tomorrow-night {
  /**
 * prism.js tomorrow night eighties for JavaScript, CoffeeScript, CSS and HTML
 * Based on https://github.com/chriskempson/tomorrow-theme
 * <AUTHOR> Pritchard
 */
}
.code-section.theme-tomorrow-night editarea {
  caret-color: #ccc;
}
.code-section.theme-tomorrow-night editarea {
  color: #ccc;
}
.code-section.theme-tomorrow-night .token.comment,
.code-section.theme-tomorrow-night .token.block-comment,
.code-section.theme-tomorrow-night .token.prolog,
.code-section.theme-tomorrow-night .token.doctype,
.code-section.theme-tomorrow-night .token.cdata {
  color: #999;
}
.code-section.theme-tomorrow-night .token.punctuation {
  color: #ccc;
}
.code-section.theme-tomorrow-night .token.tag,
.code-section.theme-tomorrow-night .token.attr-name,
.code-section.theme-tomorrow-night .token.namespace,
.code-section.theme-tomorrow-night .token.deleted {
  color: #e2777a;
}
.code-section.theme-tomorrow-night .token.function-name {
  color: #6196cc;
}
.code-section.theme-tomorrow-night .token.boolean,
.code-section.theme-tomorrow-night .token.number,
.code-section.theme-tomorrow-night .token.function {
  color: #f08d49;
}
.code-section.theme-tomorrow-night .token.property,
.code-section.theme-tomorrow-night .token.class-name,
.code-section.theme-tomorrow-night .token.constant,
.code-section.theme-tomorrow-night .token.symbol {
  color: #f8c555;
}
.code-section.theme-tomorrow-night .token.selector,
.code-section.theme-tomorrow-night .token.important,
.code-section.theme-tomorrow-night .token.important1,
.code-section.theme-tomorrow-night .token.atrule,
.code-section.theme-tomorrow-night .token.keyword,
.code-section.theme-tomorrow-night .token.builtin {
  color: #cc99cd;
}
.code-section.theme-tomorrow-night .token.string,
.code-section.theme-tomorrow-night .token.char,
.code-section.theme-tomorrow-night .token.attr-value,
.code-section.theme-tomorrow-night .token.regex,
.code-section.theme-tomorrow-night .token.variable {
  color: #7ec699;
}
.code-section.theme-tomorrow-night .token.operator,
.code-section.theme-tomorrow-night .token.entity,
.code-section.theme-tomorrow-night .token.url {
  color: #67cdcc;
}
.code-section.theme-tomorrow-night .token.important,
.code-section.theme-tomorrow-night .token.bold,
.code-section.theme-tomorrow-night .token.bold1 {
  font-weight: bold;
}
.code-section.theme-tomorrow-night .token.italic,
.code-section.theme-tomorrow-night .token.italic1 {
  font-style: italic;
}
.code-section.theme-tomorrow-night .token.entity {
  cursor: help;
}
.code-section.theme-tomorrow-night .token.inserted {
  color: green;
}

editor-container > math-type.dark-mode {
  background: #1e1e1e;
}
math-type.dark-mode {
  background: #1e1e1e;
}
math-type.dark-mode.math-type-for-print {
  -webkit-print-color-adjust: exact;
}
math-type.dark-mode editarea.root-editor {
  color: #e0e0e0;
  fill: #e0e0e0;
  stroke: #e0e0e0;
  border-color: #e0e0e0;
}
math-type.dark-mode editarea.root-editor {
  background: #1e1e1e;
}
math-type.dark-mode .math-container-symbol.selected,
math-type.dark-mode .math-container-symbol.display.selected {
  background-color: #2a352a;
}
math-type.dark-mode .matrix-symbol.gather-symbol.selected {
  background-color: #2a352a;
}
math-type.dark-mode .matrix-symbol.gather-symbol > matrix > table > tbody > tr > td.selected {
  border: 1px solid #525252;
}
math-type.dark-mode .matrix-symbol.align-symbol.selected {
  background-color: #2a352a;
}
math-type.dark-mode .matrix-symbol.align-symbol > matrix > table > tbody > tr > td.selected {
  border: 1px solid #525252;
}
math-type.dark-mode .matrix-symbol.matrix-like.selected {
  background-color: #2a352a;
}
math-type.dark-mode .matrix-symbol.matrix-like > matrix > table > tbody > tr.selected:not(.hline) {
  border-top: 1px solid #525252;
}
math-type.dark-mode .matrix-symbol.matrix-like > matrix > table > tbody > tr:last-child.selected {
  border-bottom: 1px solid #525252;
}
math-type.dark-mode .matrix-symbol > matrix > table > tbody > tr > td.selected:not(.vline) {
  border-left: 1px solid #525252;
}
math-type.dark-mode .matrix-symbol > matrix > table > tbody > tr > td:last-child.selected {
  border-right: 1px solid #525252;
}
math-type.dark-mode .matrix-symbol.case-symbol > matrix > table > tbody > tr > td.selected {
  border: 1px solid #525252;
}
math-type.dark-mode selection-wrapper > selection {
  background-color: rgba(33, 150, 243, 0.25);
}
math-type.dark-mode selection-wrapper.inactive > selection {
  background-color: rgba(255, 255, 255, 0.27);
}
math-type.dark-mode editarea.selected {
  outline: 1px solid rgba(177, 174, 174, 0.3);
}
math-type.dark-mode compositeblock.math-diagram > math-diagram {
  background: #1e1e1e;
}
container-layer.dark-mode editor-container {
  background: #1e1e1e;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
editor-container > math-type {
  background: white;
  outline: 1px solid lightgray;
}
math-type {
  display: block;
  position: relative;
  padding: 20px;
  color: black;
  padding-top: 15px;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  outline: none;
  cursor: text;
  text-align: initial;
}
math-type .rcnt {
  color: white;
  font-size: 12px;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: row;
}
math-type > .mt-sa {
  position: absolute;
  top: 0;
  left: -100px;
  right: -100px;
  bottom: -400px;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
}
math-type.math-only compositeblock.math-container-symbol {
  width: 100%;
}
math-type.math-only compositeblock.math-container-symbol.selected {
  outline: none;
  background: none;
}
math-type.math-only compositeblock.math-container-symbol > editarea {
  width: 100%;
}
math-type.math-only compositeblock.math-container-symbol > editarea > line {
  justify-content: flex-start;
}
math-type > input:not([type]),
math-type input[type="text"] {
  width: 50px;
}
math-type composition-indicator {
  display: block;
  position: absolute;
  height: 2px;
  min-width: 2px;
  border-bottom: 2px solid gray;
  left: 0;
  top: 0;
}
math-type .focus-element {
  outline: none;
  overflow: hidden;
  width: 0px;
  height: 0px;
  top: 0px;
  left: 0px;
  display: block;
  position: absolute;
  font-size: 40px;
}
.scroll-without-scrollbar {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE 10+ */
}
.scroll-without-scrollbar::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
}
#root,
.main-container {
  width: 100%;
  color: #212121;
}
.sub-text-cursor,
.text-cursor {
  position: absolute;
  color: green;
  /*font-size: 1.2em;*/
  pointer-events: none;
  border-left: 2px solid green;
  display: block;
  top: 0px;
  left: 0px;
  z-index: 10;
}
.sub-text-cursor {
  opacity: 0.4;
}
math-edit-container {
  display: block;
  outline: none;
  position: relative;
  font-family: 'Asana';
}
.root-editor {
  min-height: 500px;
  box-sizing: border-box;
}
.root-editor.restricted-view {
  min-height: unset;
}
.root-editor.test-view {
  min-height: 100px;
}
.menu {
  color: #ffffff;
  text-transform: uppercase;
  text-decoration: none;
  font-family: "HelveticaNeue-Medium", sans-serif;
  font-size: 14px;
  display: inline-block;
  transform: scale(1, 1.5);
  -webkit-transform: scale(1, 1.5);
  /* Safari and Chrome */
  -moz-transform: scale(1, 1.5);
  /* Firefox */
  -ms-transform: scale(1, 1.5);
  /* IE 9+ */
  -o-transform: scale(1, 1.5);
  /* Opera */
}
.setting-group-options {
  font-size: 13px;
  color: gray;
  fill: gray;
}
.setting-group-options > i,
.setting-group-options > svg.icon {
  padding: 3px;
  cursor: pointer;
  border: 1px solid transparent;
  /*box-sizing: border-box;*/
}
.setting-group-options > i.selected,
.setting-group-options > svg.icon.selected {
  background-color: white;
  border: 1px solid lightgray;
}
.setting-group-options > i:hover,
.setting-group-options > svg.icon:hover {
  border: 1px solid lightgray;
}
.setting-group-options > i.disabled,
.setting-group-options > svg.icon.disabled {
  opacity: 0.3;
}
.setting-group-options > i.disabled:hover,
.setting-group-options > svg.icon.disabled:hover {
  background-color: inherit;
  border: 1px solid transparent;
}
math-type .math-text,
math-type .normal-text,
math-type .raw-latex {
  font-family: arial, verdana, geneva, lucida, 'lucida grande', arial, helvetica, sans-serif;
}
math-type .math-text editarea.text-mode > line.text-mode > blocks > block,
math-type .normal-text editarea.text-mode > line.text-mode > blocks > block,
math-type .raw-latex editarea.text-mode > line.text-mode > blocks > block {
  white-space: nowrap;
}
.mt-common-dialog {
  box-shadow: 1px 1px 1px 0px #e0dddd;
  background-color: #f7f7f7;
  border: 1px lightgray solid;
  z-index: 14;
  padding: 6px;
  cursor: default;
  font-family: "Segoe UI", Arial, Verdana, sans-serif;
}
disabled-layer {
  display: block;
  position: absolute;
  left: 0;
  top: 1px;
  right: 0;
  bottom: 2px;
  background: rgba(247, 247, 247, 0.66);
  /** consider export button z-index when change this one*/
  z-index: 500;
}
math-type tag-container tag-select-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
math-type tag-container tag-box-select {
  position: absolute;
  display: block;
  background-color: green;
  opacity: 0;
  cursor: pointer;
  z-index: 15;
}
math-type tag-container tag-box-select:hover {
  opacity: 0.2;
}
math-type tag-container tag-box-gap {
  position: absolute;
  display: block;
  background-color: white;
  opacity: 0.9;
  z-index: 15;
}
.setting-popup-zindex {
  z-index: 11;
}
svg.selectable {
  cursor: pointer;
  border: 1px solid transparent;
}
svg.selectable.selected {
  background: white;
  border: solid 1px lightgray;
}
svg.selectable:hover {
  border: solid 1px lightgray;
}
/**start-ignore-save-as-html*/
button.btn-ripple {
  height: 39px;
  display: inline-block;
  text-align: center;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  background: #f7f7f7;
  border: 1px solid lightgray;
  border-radius: 7px;
  box-shadow: #e0dddd 1px 1px 1px 0px;
  color: gray;
  margin-right: 3px;
  outline: none;
  padding: 0.2em;
}
/* Ripple magic */
button.btn-ripple {
  position: relative;
  overflow: hidden;
}
button.btn-ripple:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 5px;
  height: 5px;
  background: rgba(0, 0, 0, 0.15);
  opacity: 0;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}
@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 1;
  }
  20% {
    transform: scale(25, 25);
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}
button.btn-ripple:active:after {
  animation: ripple 1s ease-out;
}
.setting-select {
  display: inline-flex;
  background-color: #f7f7f7;
  border: 1px solid #c3c2c2;
  cursor: pointer;
  color: gray;
  opacity: 0.7;
  z-index: 11;
  align-items: center;
  text-align: center;
  justify-content: center;
}
.setting-select:hover {
  opacity: 1;
}
.anchor-tag-border,
.section-ref-border {
  border-bottom: 1px dotted green;
}
.anchor-tag-border.empty,
.section-ref-border.empty {
  border-top: 1px dotted green;
  border-right: 1px dotted green;
  border-left: 1px dotted green;
}
.anchor-tag-border.empty > span,
.section-ref-border.empty > span {
  display: inline-block;
}
/**end-ignore-save-as-html*/

@media print {
  @page {
    margin: 0;
  }
  /**
    Convert Preview into main print area
    */
  .math-type-doc,
  .docs-container,
  .modal-dialog-root-container,
  instructions-container {
    display: none !important;
  }
  .print-preview-container {
    position: static !important;
    display: block !important;
    visibility: visible !important;
    width: auto !important;
    height: auto !important;
    z-index: 99999 !important;
  }
  .print-container {
    position: static !important;
    overflow: hidden;
  }
  .print-preview-controls-container {
    display: none !important;
  }
  .math-type-no-print {
    display: none !important;
  }
  area-container.print-page-level {
    margin-left: 0 !important;
    margin-top: 0 !important;
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    border: none !important;
    border-left: none !important;
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
    break-inside: avoid !important;
  }
  .area-container-border {
    border: none !important;
    border-left: none !important;
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
    display: none !important;
  }
  #print-container {
    background-color: transparent !important;
  }
  #print-container > math-type {
    background-color: transparent !important;
  }
  #print-container > math-type > math-edit-container > editarea {
    background-color: transparent !important;
  }
  /*-----------------------------*/
  header,
  .export-bar-container {
    display: none !important;
  }
  math-type {
    padding: 0 !important;
  }
  .print-remove-background {
    background-color: transparent !important;
  }
  document-info-bar {
    display: none !important;
  }
  document-sidebar-container {
    display: none !important;
  }
  body {
    position: relative !important;
  }
  page,
  #root,
  body,
  html {
    height: 100% !important;
    width: 100% !important;
    position: relative !important;
    background: white !important;
    overflow: visible !important;
  }
}
.print-background.math-type-for-print {
  -webkit-print-color-adjust: exact;
}
@media print {
  outline: none !important;
  .npm__react-simple-code-editor__textarea {
    display: none !important;
  }
  math-diagram {
    border: none !important;
  }
  .no-border-on-print {
    border: none !important;
  }
  .setting-select {
    display: none !important;
  }
  .no-print,
  ref-tag.empty-line-tag,
  .text-cursor,
  .sub-text-cursor,
  selection-wrapper,
  message-box-container,
  connection-controls,
  .control-point-guide,
  tool-bar,
  items-bar,
  document-sidebar,
  resize-bar,
  diagram-expander,
  warning-error-region,
  loading-layer,
  resizing-info,
  toc-refresh {
    display: none !important;
  }
  .no-print-outline {
    outline: none !important;
  }
  .selected {
    background: unset !important;
  }
  .math-container-symbol.selected {
    background: unset !important;
  }
  .matrix-symbol.align-symbol {
    background: unset !important;
  }
  .matrix-symbol.gather-symbol {
    background: unset !important;
  }
  tr.selected,
  td.selected {
    border: unset !important;
  }
  tr.selected.hline {
    border-top: 1px solid black !important;
  }
  tr.selected.last-hline {
    border-bottom: 1px solid black !important;
  }
  td.selected.vline {
    border-left: 1px solid black !important;
  }
  td.selected.last-vline {
    border-right: 1px solid black !important;
  }
  .selected {
    outline: unset !important;
  }
  container-layer {
    margin: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    height: initial !important;
    overflow: visible !important;
  }
  editor-container {
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    width: 100% !important;
  }
  content-area {
    padding: 0px !important;
    margin: 0;
    background: transparent !important;
  }
  vcomposed-symbol.non-safari > start {
    margin-bottom: -0.05em !important;
  }
  vcomposed-symbol.non-safari > middle-center {
    margin-top: -0.05em;
    margin-bottom: -0.05em !important;
  }
  vcomposed-symbol.non-safari > middle {
    margin-top: -0.05em;
    margin-bottom: -0.05em !important;
  }
  vcomposed-symbol.non-safari > end {
    margin-top: -0.05em !important;
  }
}
.math-type-for-print {
  outline: none !important;
}
.math-type-for-print .npm__react-simple-code-editor__textarea {
  display: none !important;
}
.math-type-for-print math-diagram {
  border: none !important;
}
.math-type-for-print .no-border-on-print {
  border: none !important;
}
.math-type-for-print .setting-select {
  display: none !important;
}
.math-type-for-print .no-print,
.math-type-for-print ref-tag.empty-line-tag,
.math-type-for-print .text-cursor,
.math-type-for-print .sub-text-cursor,
.math-type-for-print selection-wrapper,
.math-type-for-print message-box-container,
.math-type-for-print connection-controls,
.math-type-for-print .control-point-guide,
.math-type-for-print tool-bar,
.math-type-for-print items-bar,
.math-type-for-print document-sidebar,
.math-type-for-print resize-bar,
.math-type-for-print diagram-expander,
.math-type-for-print warning-error-region,
.math-type-for-print loading-layer,
.math-type-for-print resizing-info,
.math-type-for-print toc-refresh {
  display: none !important;
}
.math-type-for-print .no-print-outline {
  outline: none !important;
}
.math-type-for-print .selected {
  background: unset !important;
}
.math-type-for-print .math-container-symbol.selected {
  background: unset !important;
}
.math-type-for-print .matrix-symbol.align-symbol {
  background: unset !important;
}
.math-type-for-print .matrix-symbol.gather-symbol {
  background: unset !important;
}
.math-type-for-print tr.selected,
.math-type-for-print td.selected {
  border: unset !important;
}
.math-type-for-print tr.selected.hline {
  border-top: 1px solid black !important;
}
.math-type-for-print tr.selected.last-hline {
  border-bottom: 1px solid black !important;
}
.math-type-for-print td.selected.vline {
  border-left: 1px solid black !important;
}
.math-type-for-print td.selected.last-vline {
  border-right: 1px solid black !important;
}
.math-type-for-print .selected {
  outline: unset !important;
}
.math-type-for-print container-layer {
  margin: 0 !important;
  padding: 0 !important;
  background: transparent !important;
  height: initial !important;
  overflow: visible !important;
}
.math-type-for-print editor-container {
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
  background: transparent !important;
  width: 100% !important;
}
.math-type-for-print content-area {
  padding: 0px !important;
  margin: 0;
  background: transparent !important;
}
.math-type-for-print vcomposed-symbol.non-safari > start {
  margin-bottom: -0.05em !important;
}
.math-type-for-print vcomposed-symbol.non-safari > middle-center {
  margin-top: -0.05em;
  margin-bottom: -0.05em !important;
}
.math-type-for-print vcomposed-symbol.non-safari > middle {
  margin-top: -0.05em;
  margin-bottom: -0.05em !important;
}
.math-type-for-print vcomposed-symbol.non-safari > end {
  margin-top: -0.05em !important;
}
.justify-single-line {
  display: inline-block;
  text-align-last: justify;
  width: 100%;
}
.rtl-on-line-level {
  direction: rtl;
}

math-type modal-dialog.import-latex math-type editarea.root-editor {
  min-height: 100px;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type .mobile-keys-support-region {
  position: fixed;
  top: 84px;
  left: 45px;
  color: gray;
  z-index: 11609;
  opacity: 0.9;
  display: flex;
  width: auto;
  z-index: 600;
  cursor: pointer;
}
math-type .mobile-keys-support-region.is-android {
  top: unset;
  bottom: 4px;
  left: 17px;
}
math-type .mobile-keys-support-region.select-only {
  top: 40px;
  left: 25px;
}
math-type .mobile-keys-support-region.select-only .btn-suggestion-box-mobile {
  display: none;
}

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type line-setting,
.role-toolbar-wrapper line-setting {
  display: flex;
  position: relative;
  margin-top: -1px;
}
math-type line-setting > setting-icon,
.role-toolbar-wrapper line-setting > setting-icon {
  background-color: #f7f7f7;
  opacity: 0.2;
  cursor: pointer;
  font-size: 13px;
}
math-type line-setting > setting-icon:hover,
.role-toolbar-wrapper line-setting > setting-icon:hover {
  opacity: 1;
}
math-type line-setting > detail,
.role-toolbar-wrapper line-setting > detail {
  display: flex;
  margin-top: 8px;
}
math-type line-setting > detail > list-items-options,
.role-toolbar-wrapper line-setting > detail > list-items-options,
math-type line-setting > detail indent-outdent-options,
.role-toolbar-wrapper line-setting > detail indent-outdent-options {
  padding-left: 3px;
}
math-type line-setting .section-toolbar-settings,
.role-toolbar-wrapper line-setting .section-toolbar-settings {
  display: inline-flex;
  cursor: pointer;
  position: relative;
  vertical-align: -5px;
}
math-type line-setting .section-toolbar-settings.disabled,
.role-toolbar-wrapper line-setting .section-toolbar-settings.disabled {
  color: lightgray;
  cursor: default;
  fill: lightgray;
  border: 1px solid transparent;
}
math-type line-setting .section-toolbar-settings.disabled > .fa.fa-caret-down.toolbar__item-with-option__caret,
.role-toolbar-wrapper line-setting .section-toolbar-settings.disabled > .fa.fa-caret-down.toolbar__item-with-option__caret {
  border-left: 1px solid transparent;
  color: lightgray;
}
math-type line-setting .section-toolbar-settings > .section-icon,
.role-toolbar-wrapper line-setting .section-toolbar-settings > .section-icon {
  border: 1px solid transparent;
  width: 15px;
  height: 15px;
  font-size: 6.5px;
  padding: 2px;
  font-weight: bold;
  margin-left: -1px;
  margin-top: -1px;
  margin-bottom: -1px;
}
math-type line-setting .section-toolbar-settings > .section-icon.selected,
.role-toolbar-wrapper line-setting .section-toolbar-settings > .section-icon.selected {
  background: white;
  border: 1px solid lightgray;
}
math-type line-setting .section-toolbar-settings > .fa.fa-caret-down.toolbar__item-with-option__caret,
.role-toolbar-wrapper line-setting .section-toolbar-settings > .fa.fa-caret-down.toolbar__item-with-option__caret {
  padding: 5px 4px 5px 3px;
  margin-left: -1px;
  margin-bottom: -1px;
}
.toolbar-container .tool-bar-text {
  visibility: hidden;
  opacity: 0;
  transition: opacity 500ms;
  transition-delay: 0.5s;
}
.toolbar-container:hover .tool-bar-text {
  visibility: visible;
  opacity: 1;
}
.toolbar_svg-icon-wrapper {
  border: 1px solid transparent;
  width: 19px;
  height: 19px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.toolbar_svg-icon-wrapper.selected {
  background-color: white;
  border: 1px solid lightgray;
}
.toolbar_svg-icon-wrapper:hover {
  border: 1px solid lightgray;
}
.toolbar_svg-icon-wrapper.disabled {
  fill: lightgray;
  border: 1px solid transparent;
  cursor: default;
  background-color: transparent;
}
.toolbar__item-with-option {
  border: 1px solid transparent;
  display: flex;
  justify-content: stretch;
  cursor: pointer;
}
.toolbar__item-with-option:hover {
  border: 1px solid lightgray;
}
.toolbar__item-with-option:hover > i.fa.toolbar__item-with-option__caret {
  border-left: 1px solid lightgray;
}
i.fa.toolbar__item-with-option__caret {
  border-left: 1px solid transparent;
  font-size: 0.8em;
  padding-left: 3px;
  display: inline-block;
  padding-top: 0.45em;
  color: #c3c2c2;
}
i.fa.toolbar__item-with-option__caret:hover {
  color: gray;
}
/**end-ignore-save-as-html*/

.special-symbol-dialog__special-char {
  display: inline-block;
  width: 24px;
  height: 24px;
  line-height: 24px;
  border-bottom: 1px solid #ddd;
  border-right: 1px solid #ddd;
  text-align: center;
  cursor: pointer;
  outline: none;
}
.special-symbol-dialog__special-char:hover {
  background: #bfe4bd;
}

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
context-menu-container {
  position: fixed;
  left: 100px;
  top: 300px;
  z-index: 13;
  box-shadow: 2px 2px 3px 0px #c3c3c3;
  border: 1px solid lightgray;
  width: 200px;
  background: white;
  cursor: default;
  outline: none;
  font-family: "Segoe UI", Arial, Verdana, sans-serif;
}
context-menu-container ct-item {
  display: block;
  padding: 6px 9px;
  font-size: 13px;
  color: gray;
}
context-menu-container ct-item.disabled {
  color: lightgray;
}
context-menu-container ct-item:hover {
  background: #eae9e9;
}
context-menu-container ct-item.hidden {
  display: none;
}
context-menu-container ct-icon {
  width: 20px;
  display: inline-block;
}
context-menu-container ct-separator {
  width: 100%;
  display: block;
  height: 1px;
  margin-top: 2px;
  margin-bottom: 2px;
  border-top: 1px solid lightgray;
}

/* Collection default theme */
/* Grid default theme */
/* Table default theme */
.ReactVirtualized__Table__headerRow {
  font-weight: 700;
  text-transform: uppercase;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.ReactVirtualized__Table__row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.ReactVirtualized__Table__headerTruncatedText {
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.ReactVirtualized__Table__headerColumn,
.ReactVirtualized__Table__rowColumn {
  margin-right: 10px;
  min-width: 0px;
}
.ReactVirtualized__Table__rowColumn {
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ReactVirtualized__Table__headerColumn:first-of-type,
.ReactVirtualized__Table__rowColumn:first-of-type {
  margin-left: 10px;
}
.ReactVirtualized__Table__sortableHeaderColumn {
  cursor: pointer;
}
.ReactVirtualized__Table__sortableHeaderIconContainer {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
.ReactVirtualized__Table__sortableHeaderIcon {
  -webkit-box-flex: 0;
  -ms-flex: 0 0 24px;
  flex: 0 0 24px;
  height: 1em;
  width: 1em;
  fill: currentColor;
}
/* List default theme */

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
.common-square-icon {
  width: 4px;
  height: 4px;
  min-height: 4px;
  border-style: solid;
  border-width: 1px;
  text-align: center;
  border-color: #b3b0b0;
  background-color: lightgray;
  /* border-style: dotted; */
}
.common-square-icon-expand {
  background-color: white;
}
.common-big-square-icon {
  width: 6px;
  height: 6px;
  min-height: 6px;
  border: 1px solid gray;
  background-color: white;
  margin: auto;
}
.auto-complete-container {
  width: 282px;
  outline: none;
  font-size: 12px;
  position: absolute;
  background: transparent;
  z-index: 12;
  cursor: default;
}
.auto-complete-container.mobile-tablet {
  position: relative;
}
.auto-complete-container .auto-complete-content {
  margin: 7px;
}
.auto-complete-container .drawing-container {
  position: relative;
  background-color: white;
  border: 1px solid lightgray;
  height: 130px;
  width: 250px;
  margin: auto;
}
.auto-complete-container .drawing-container > clear-button {
  position: absolute;
  right: 1px;
  display: block;
  bottom: -1px;
  font-size: 14px;
  /* border: 1px solid lightgray; */
  padding: 6px;
  cursor: pointer;
  color: gray;
}
.auto-complete-container .drawing-container > clear-button:hover {
  color: green;
}
.auto-complete-container .math-symbol-container {
  max-height: 400px;
}
/**end-ignore-save-as-html*/

math-type .symbol-container,
.auto-complete-external-area .symbol-container {
  display: flex;
  flex-direction: row;
  min-height: 25px;
  padding-left: 4px;
  padding-right: 12px;
  align-items: center;
  cursor: pointer;
}
math-type .symbol-container.selected,
.auto-complete-external-area .symbol-container.selected {
  background-color: #bfe4bd;
}
math-type .symbol-friendly-name,
.auto-complete-external-area .symbol-friendly-name {
  flex-grow: 1;
  font-family: Asana;
  /*padding-top: 6px;*/
}
math-type .symbol-icon,
.auto-complete-external-area .symbol-icon {
  /*padding-top: 6px;*/
  margin-right: 15px;
  min-width: 20px;
  text-align: center;
}
math-type .short-cut,
.auto-complete-external-area .short-cut {
  text-align: center;
  color: gray;
  /*vertical-align: middle;*/
  /*line-height: 20px;*/
  margin: 0 2px 0 2px;
  font-size: 12px;
  display: flex;
  align-items: center;
}
math-type .short-cut span,
.auto-complete-external-area .short-cut span {
  font-size: 10px;
}
math-type .hidden,
.auto-complete-external-area .hidden {
  display: none;
}

/**start-ignore-save-as-html*/
.categories-container {
  position: absolute;
  top: 0;
  left: 282px;
  box-shadow: 1px 1px 1px 0px #e0dddd;
  background-color: #f7f7f7;
  border: 1px lightgray solid;
  font-size: 12px;
  width: 42px;
  height: 274px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 7px;
  padding-bottom: 7px;
}
.categories-container.mobile-tablet {
  height: 277px;
}
.categories-container .item {
  display: flex;
  align-items: center;
  height: 30px;
  border: 1px solid lightgray;
  width: 32px;
  border-top: none;
  cursor: pointer;
}
.categories-container .item:hover,
.categories-container .item.selected {
  background-color: #bfe4bd;
}
.categories-container .item .symbol {
  display: flex;
  margin: auto;
}
.categories-container .item .align-summation-symbol {
  margin-right: -8px;
}
.categories-container .item .align-over-brace-symbol {
  margin-right: -5px;
}
/**end-ignore-save-as-html*/

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
items-bar {
  cursor: default;
  display: block;
  position: absolute;
  left: 10px;
  top: 80px;
  border: 1px lightgray solid;
  background: #f7f7f7;
  padding: 5px;
  box-shadow: 1px 1px 1px 0px #e0dddd;
  color: gray;
}
items-bar.mobile-tablet {
  top: 170px;
  left: 3px;
  transform: scale(1.3);
  cursor: pointer;
}
items-bar item {
  display: block;
  border: 1px solid transparent;
  padding: 3px;
  stroke: gray;
  fill: gray;
}
items-bar item:hover {
  border: 1px #e6e5e5 solid;
  background: white;
}
items-bar item.disabled {
  border: 1px solid transparent;
  color: lightgray;
  stroke: lightgray;
  fill: lightgray;
}
items-bar item.trigger-item {
  position: relative;
  height: 16px;
}
items-bar item.trigger-item svg {
  width: 19px;
  height: 16px;
  transform: translate(2px, 0);
}
items-bar item.trigger-item svg path {
  stroke: none;
}
items-bar item.trigger-item .fa.fa-bars {
  position: absolute;
  left: 9.5px;
  top: 7.5px;
  font-size: 8px;
}
items-bar:hover round-close-icon {
  opacity: 1;
}
round-close-icon {
  top: -10px;
  left: -7px;
  background: white;
  font-size: 12px;
  width: 15px;
  height: 15px;
  text-align: center;
  line-height: 1.4em;
  position: absolute;
  display: block;
  opacity: 0;
  border: 1px solid lightgray;
  border-radius: 50%;
  cursor: pointer;
}
round-close-icon:hover {
  color: #bb0505;
}
/**end-ignore-save-as-html*/

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
math-type tool-bar,
.role-toolbar-wrapper tool-bar {
  cursor: default;
  height: 35px;
}
math-type tool-bar too-bar-container,
.role-toolbar-wrapper tool-bar too-bar-container {
  display: flex;
  justify-content: center;
  width: 770px;
  min-width: 770px;
}
math-type tool-bar.mobile-tablet,
.role-toolbar-wrapper tool-bar.mobile-tablet {
  overflow-x: visible;
  padding-bottom: 8px;
  cursor: pointer;
}
math-type tool-bar.mobile-tablet too-bar-container,
.role-toolbar-wrapper tool-bar.mobile-tablet too-bar-container {
  transform: scale(1.3, 1.3);
  padding: 0;
  transform-origin: 0px 0px;
  position: relative;
  padding-left: 13px;
}
math-type tool-bar.mobile-tablet.select-only,
.role-toolbar-wrapper tool-bar.mobile-tablet.select-only {
  display: none;
}
math-type tool-bar v-separator,
.role-toolbar-wrapper tool-bar v-separator {
  display: block;
  border-left: 1px solid lightgray;
  margin-top: 2px;
  margin-bottom: 9px;
  margin-left: 2px;
}
math-type tool-bar tool-bar-item,
.role-toolbar-wrapper tool-bar tool-bar-item {
  display: flex;
  flex-shrink: 0;
}
math-type tool-bar tool-bar-item-separator,
.role-toolbar-wrapper tool-bar tool-bar-item-separator {
  display: block;
  width: 4px;
  margin-left: 4px;
  margin-top: 6px;
  margin-bottom: 6px;
  border-left: 2px solid #e0dfdf;
}
math-type tool-bar .undo-redo-span,
.role-toolbar-wrapper tool-bar .undo-redo-span {
  padding: 0px;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 3px;
  fill: gray;
  cursor: pointer;
  border: 1px solid transparent;
}
math-type tool-bar .undo-redo-span:hover,
.role-toolbar-wrapper tool-bar .undo-redo-span:hover {
  border: 1px solid #cccaca;
}
/**end-ignore-save-as-html*/

/**different context*/
/**different context*/
selection-wrapper {
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  user-select: none;
  pointer-events: none;
  z-index: 9;
}
selection-wrapper > selection {
  display: block;
  position: absolute;
  background-color: rgba(0, 126, 255, 0.15);
  user-select: none;
  width: 100px;
  height: 100px;
  transform-origin: 0 0;
}
selection-wrapper.inactive > selection {
  background-color: rgba(58, 57, 57, 0.13);
}

region-highlight-container {
  user-select: none;
  pointer-events: none;
  display: block;
  position: absolute;
  width: 0px;
  height: 0px;
  top: 0;
  left: 0;
  border: 1px solid lightgoldenrodyellow;
  opacity: 0.2;
}
region-highlight-container > rhc-all > rh-rect {
  display: block;
  position: absolute;
  background: orange;
}
region-highlight-container > rh-selected-rect {
  display: block;
  position: absolute;
  background: red;
}

.math-template-btb.btn-normal {
  background: transparent;
  margin-left: 0;
  font-size: 12px;
  border: none;
}
.math-template-btb.btn-normal:hover {
  background: white;
}
.math-template-name-item:hover {
  background: #f7f7f7;
}

.math-template-tree .tree-node-name {
  white-space: nowrap;
}
.math-template-tree .tree-node-name:hover {
  background: #f7f7f7;
}

math-type compositeblock.bib-bibliography {
  width: 100%;
  display: block;
}
math-type compositeblock.bib-cite {
  display: inline;
  white-space: break-spaces;
}
math-type compositeblock.bib-cite > editarea.text-mode {
  display: inline;
}
math-type compositeblock.bib-cite > editarea.text-mode > line.text-mode {
  display: inline;
}
math-type compositeblock.bib-cite > editarea.text-mode > line.text-mode > blocks {
  display: inline;
}
.csl-context {
  white-space: pre-wrap;
}
.csl-context.csl-cite div {
  display: inline;
}
.csl-context div.csl-left-margin {
  float: left;
}
.csl-context div.csl-block {
  font-weight: bold;
}
.csl-context div.csl-right-inline {
  margin-left: 2.5em;
}
.csl-context div.csl-indent {
  margin-top: 0.5em;
  margin-left: 2em;
  padding-bottom: 0.2em;
  padding-left: 0.5em;
  border-left: 5px solid #ccc;
}

.caption-ref-select .role-item:hover {
  background: #edf9ec;
}

.footnote-mt-container {
  position: relative;
}
.footnote-mt-container .more-option-box {
  position: absolute;
  left: -25px;
  top: 0;
  width: 30px;
  bottom: 0;
  display: none;
}
.footnote-mt-container .more-option-box > div {
  background: #f7f7f7;
  border: 1px solid #d6d3d3;
  padding: 0 0.2em;
  cursor: pointer;
  color: gray;
  display: inline-block;
}
.footnote-mt-container.hover-select:hover .more-option-box {
  display: block;
}
.mt-doc-fn > math-edit-container > editarea > area-container > line > first-line-prefix {
  padding-right: 0.3em;
}
.mt-doc-fn > math-edit-container > editarea > area-container > line.has-rtl {
  direction: rtl;
}
.mt-doc-fn > math-edit-container > editarea > area-container > line.has-rtl > first-line-prefix {
  padding-left: 0.3em;
}
.blocks-inline > math-edit-container > editarea > area-container > line > blocks {
  display: inline;
}

.lang-name:hover {
  background: #f7f7f7;
}
.lang-name.selected {
  background: #e1e8f5;
}
.custom-lange-area input::placeholder,
.custom-lange-area textarea::placeholder {
  color: lightgray;
}

.page-print-item {
  position: relative;
}
.page-print-item:hover .page-print-item-hover {
  display: block;
}
.page-print-item-hover {
  display: none;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 10px;
  background: #f7f6f6;
  z-index: 10;
  padding: 5px;
  font-size: 0.8em;
  white-space: nowrap;
  color: gray;
  border: 1px solid lightgray;
}

.settings-show-hide {
  background: rgba(255, 255, 255, 0.61);
}
.settings-show-hide:hover {
  background: white;
}
.group-symbol-collapsed {
  display: block;
  border: 1px dotted lightgray;
  padding-top: 0.1em;
  padding-bottom: 0.1em;
}
.role-math-mode-group.math-mode-group-expanded .settings-show-hide,
.role-text-mode-group-inline.text-mode-group-inline-expanded .settings-show-hide {
  display: none;
}
.role-math-mode-group.math-mode-group-expanded:hover,
.role-text-mode-group-inline.text-mode-group-inline-expanded:hover {
  outline: 1px solid lightgray;
}
.role-math-mode-group.math-mode-group-expanded:hover .settings-show-hide,
.role-text-mode-group-inline.text-mode-group-inline-expanded:hover .settings-show-hide {
  display: flex;
}

math-type compositeblock.text-super-script-symbol,
math-type compositeblock.text-sub-script-symbol {
  display: inline;
}
math-type compositeblock.text-super-script-symbol > editarea.text-mode,
math-type compositeblock.text-sub-script-symbol > editarea.text-mode {
  display: inline;
}
math-type compositeblock.text-super-script-symbol > editarea.text-mode > line.text-mode,
math-type compositeblock.text-sub-script-symbol > editarea.text-mode > line.text-mode {
  display: inline;
}
math-type compositeblock.text-super-script-symbol > editarea.text-mode > line.text-mode > blocks,
math-type compositeblock.text-sub-script-symbol > editarea.text-mode > line.text-mode > blocks {
  display: inline;
}
math-type .text-super-script-symbol {
  vertical-align: super;
}
math-type .text-sub-script-symbol {
  vertical-align: sub;
}

compositeblock.section-ref {
  display: inline;
}
compositeblock.section-ref > .section-ref-border {
  display: inline;
  white-space: pre-wrap;
}
compositeblock.section-ref > div {
  display: inline;
}

/**start-ignore-save-as-html*/
document-tree {
  display: block;
  font-size: 13px;
  user-select: none;
  cursor: default;
  padding: 5px 0;
  position: relative;
  min-height: calc(100% - 10px);
}
document-tree.mobile-tablet {
  font-size: 18px;
}
document-tree side-gradient {
  position: absolute;
  display: block;
  right: 0;
  top: 5;
  bottom: 0;
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, #ffffff 90%);
  z-index: 1;
  width: 15px;
}
document-tree node-directory {
  display: block;
}
document-tree node-children {
  display: block;
}
document-tree .node-name {
  display: block;
  padding: 4px 2px;
  white-space: nowrap;
  overflow: hidden;
}
document-tree .node-name.disabled {
  background: unset;
  opacity: 0.4;
}
document-tree .node-name > icon {
  font-size: 0.9em;
  position: relative;
}
document-tree.active .node-name:hover {
  background: #f7f7f7;
}
document-tree.active .node-name.selected {
  background: #e1e8f5;
}
document-tree node-document.node-name > icon {
  padding: 0 0.3em;
  margin-right: 0.2em;
}
document-tree node-directory-name.node-name.expanded > icon > i {
  transform: rotate(45deg);
}
document-tree node-directory-name.node-name > icon {
  cursor: pointer;
  padding: 0 0.3em;
  margin-right: 0.2em;
}
.drag-icon {
  height: 100px;
  width: 100px;
  background-color: red;
}
/**end-ignore-save-as-html*/

icon-dropdown-items icon-dropdown-item:hover {
  background: #eae9e9;
}
icon-dropdown ii-icon {
  fill: gray;
}
icon-dropdown:hover {
  color: #4e4d4d;
}
icon-dropdown:hover ii-icon {
  fill: #4e4d4d;
}

resizable-container {
  display: block;
  position: relative;
  min-height: 0;
  flex-shrink: 0;
}

/**start-ignore-save-as-html*/
share-view {
  display: flex;
  height: 100%;
  flex-direction: column;
}
share-view share-view-header {
  position: relative;
  font-size: 12px;
  padding: 5px 0.5em;
  background: #f7f7f7;
  display: block;
  border: 1px solid lightgray;
  border-left: none;
  border-right: none;
  box-shadow: 0px 1px 2px 0px #dad7d7;
  flex-shrink: 0;
}
share-view share-view-header.mobile-tablet {
  font-size: 16px;
}
share-view share-view-header > i {
  position: absolute;
  right: 0.4em;
  padding: 0.4em;
  top: 0px;
  cursor: pointer;
}
share-view share-view-content {
  display: block;
  flex-grow: 1;
  overflow: auto;
}
/**end-ignore-save-as-html*/

/**start-ignore-save-as-html*/
loadable-area {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  min-height: 0px;
}
loadable-area warning-error-region {
  flex-shrink: 0;
}
loadable-area warning-error-region error {
  width: 100%;
}
loadable-area loadable-content-area {
  overflow: auto;
  height: 100%;
}
/**end-ignore-save-as-html*/

a.fce {
  cursor: pointer;
}
a.fce:hover svg {
  fill: #2d4373;
}
a.fce svg {
  fill: #3b5998;
}
a.fce i {
  text-align: center;
  color: white;
}
a.gg {
  cursor: pointer;
}
a.gg:hover svg {
  fill: #c23321;
}
a.gg svg {
  fill: #dd4b39;
}
a.gg i {
  text-align: center;
  color: white;
}
a.twr {
  cursor: pointer;
}
a.twr:hover svg {
  fill: #2795e9;
}
a.twr svg {
  fill: #55acee;
}
a.twr i {
  text-align: center;
  color: white;
}
a.gh {
  cursor: pointer;
}
a.gh:hover svg {
  fill: #2b2b2b;
}
a.gh svg {
  fill: #444;
}
a.gh i {
  text-align: center;
  color: white;
}

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
modal-dialog.swift.login-dialog {
  z-index: 20000;
}
modal-dialog.swift.login-dialog modal-container {
  width: 300px;
}
modal-dialog.swift.login-dialog modal-content {
  padding-bottom: 20px;
}
modal-dialog.swift.login-dialog buttons-group {
  width: 100%;
}
login-dialog {
  width: 100%;
  text-align: center;
}
login-dialog message {
  margin: 10px;
  display: block;
}
login-dialog .btn-gh {
  color: #fff;
  background-color: #444;
  border-color: rgba(0, 0, 0, 0.2);
}
login-dialog .btn-gh:hover {
  color: #fff;
  background-color: #2b2b2b;
  border-color: rgba(0, 0, 0, 0.2);
}
login-dialog .btn-fce {
  color: #fff;
  background-color: #3b5998;
  border-color: rgba(0, 0, 0, 0.2);
}
login-dialog .btn-fce:hover {
  color: #fff;
  background-color: #2d4373;
  border-color: rgba(0, 0, 0, 0.2);
}
login-dialog .btn-gg {
  color: #fff;
  background-color: #dd4b39;
  border-color: rgba(0, 0, 0, 0.2);
}
login-dialog .btn-gg:hover {
  color: #fff;
  background-color: #c23321;
  border-color: rgba(0, 0, 0, 0.2);
}
login-dialog .btn-twr {
  color: #fff;
  background-color: #55acee;
  border-color: rgba(0, 0, 0, 0.2);
}
login-dialog .btn-twr:hover {
  color: #fff;
  background-color: #2795e9;
  border-color: rgba(0, 0, 0, 0.2);
}
login-dialog .btn-block + .btn-block {
  margin-top: 5px;
}
login-dialog .btn {
  width: 150px;
  display: block;
  margin: auto;
  padding: 6px 13px;
  width: 200px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.4;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}
login-dialog .btn-socl {
  margin-bottom: 10px;
  position: relative;
  padding-left: 44px;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
login-dialog .btn-socl > :first-child {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 32px;
  line-height: 34px;
  font-size: 1.6em;
  text-align: center;
  border-right: 1px solid rgba(0, 0, 0, 0.2);
}
login-dialog .btn-socl.btn-sm {
  padding-left: 38px;
}
login-dialog .btn-sm {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
/**end-ignore-save-as-html*/

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**different context*/
/**different context*/
/**Context Level 1*/
/**both Level 1 and 2 Context */
/**Context Level 2 inside docsContainerZIndex*/
.dsh-filter {
  position: absolute;
  right: 28px;
  top: 2px;
  top: 5px;
  bottom: 5px;
  width: 20px;
  outline: none;
  z-index: 50;
}
.dsh-filter > svg {
  position: absolute;
  left: 4px;
  top: 7px;
}
.dsh-filter > input[type="text"] {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  width: 100%;
  background: transparent;
  border: none;
  outline: none;
}
.dsh-filter > .fa-times {
  display: none;
}
.dsh-filter:focus-within,
.dsh-filter.has-search {
  left: 5px;
  right: 5px;
  background-color: white;
  border: 1px solid lightgray;
  width: unset;
}
.dsh-filter:focus-within > input[type="text"],
.dsh-filter.has-search > input[type="text"] {
  left: 20px;
  right: 20px;
}
.dsh-filter:focus-within > .fa-times,
.dsh-filter.has-search > .fa-times {
  display: block;
  position: absolute;
  right: 5px;
  top: 5px;
  cursor: pointer;
}
.document-sidebar-container {
  color: gray;
}
.document-sidebar-container.mobile-tablet {
  cursor: pointer;
}
.document-sidebar-container.mobile-tablet i.hide-side-bar {
  font-size: 18px;
  padding-top: 8px;
  padding-bottom: 6px;
  padding-right: 10px;
}
.document-sidebar-container.mobile-tablet i.hide-share {
  font-size: 18px;
  padding-top: 3px;
  padding-bottom: 6px;
  padding-right: 6px;
}
.document-sidebar-container show-indicator {
  display: block;
  cursor: pointer;
  padding: 8px 5px;
  display: none;
}
.document-sidebar-container document-sidebar-header {
  height: 35px;
  min-height: 35px;
  padding-left: 6px;
  display: block;
  border-bottom: 1px solid lightgray;
  background: #f7f7f7;
  box-shadow: 0px 1px 2px 0px #dad7d7;
  font-size: 14px;
  line-height: 35px;
  position: relative;
}
.document-sidebar-container document-sidebar-header.mobile-tablet {
  font-size: 18px;
  height: 38px;
  min-height: 38px;
  padding-top: 5px;
}
.document-sidebar-container document-sidebar-header > div.rename-icon:hover {
  color: #4e4d4d;
}
.document-sidebar-container document-sidebar-header > div.rename-icon.disabled {
  color: lightgray;
  cursor: default;
}
.document-sidebar-container document-sidebar-header > i.fa,
.document-sidebar-container document-sidebar-header > icon {
  margin-left: 0.5em;
  margin-right: 0.5em;
  cursor: pointer;
}
.document-sidebar-container document-sidebar-header > i.fa:hover,
.document-sidebar-container document-sidebar-header > icon:hover {
  color: #4e4d4d;
  fill: #4e4d4d;
}
.document-sidebar-container document-sidebar-header > i.fa.disabled,
.document-sidebar-container document-sidebar-header > icon.disabled {
  color: lightgray;
  fill: lightgray;
  cursor: default;
  pointer-events: none;
}
.document-sidebar-container document-sidebar-header > i.fa.fa-angle-double-left {
  float: right;
  padding-right: 0.8em;
  padding-left: 10px;
  font-size: 0.9em;
  margin-right: 0;
  height: 100%;
  line-height: 35px;
}
.document-sidebar-container .documents-area {
  flex-grow: 1;
}
document-sidebar-expanded {
  z-index: 1;
  position: absolute;
  left: 0px;
  top: 0px;
  width: 200px;
  bottom: -1px;
  border: 1px solid #cecbcb;
  background: white;
  box-shadow: 1px 1px 1px 0px #e0dddd;
  display: flex;
  flex-direction: column;
}
document-sidebar-collapsed {
  z-index: 1;
  display: block;
  cursor: pointer;
  position: absolute;
  top: 50%;
  width: 20px;
  height: 37px;
  left: -1px;
  border: 1px solid #cecbcb;
  background: white;
  box-shadow: 1px 1px 1px 0px #e0dddd;
  line-height: 2.4em;
  text-align: center;
}
document-sidebar-collapsed.mobile-tablet {
  top: 84px;
  width: 28px;
}
document-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}
.mobile-tablet div.rename-icon {
  width: 25px;
  height: 25px;
}
.mobile-tablet div.rename-icon > i.fa.fa-i-cursor {
  font-size: 14px;
}
.mobile-tablet div.rename-icon > i.fa.fa-font {
  font-size: 16px;
  top: 0px;
}
div.rename-icon {
  position: relative;
  display: inline-block;
  cursor: pointer;
  width: 20px;
}
div.rename-icon > i.fa.fa-i-cursor {
  font-size: 11px;
}
div.rename-icon > i.fa.fa-font {
  font-size: 12px;
  position: absolute;
  top: 3px;
  left: 5px;
}
.composite-icon {
  fill: gray;
  cursor: pointer;
}
.composite-icon:hover {
  color: #4e4d4d;
  fill: #4e4d4d;
}
.composite-icon.disabled {
  color: lightgray;
  fill: lightgray;
  cursor: default;
  pointer-events: none;
}
.composite-icon.disabled > .fa {
  color: lightgray;
}
.fbk-mns {
  font-size: 13px;
  display: flex;
  height: 35px;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid lightgray;
  background-color: #f7f7f7;
  cursor: pointer;
}
.fbk-mns:hover {
  color: black;
}
/**end-ignore-save-as-html*/

modal-dialog.swift.document-name modal-container.mt-common-dialog {
  width: 500px;
  max-width: 95vw;
}
modal-dialog.swift.document-name document-name {
  width: 100%;
  display: flex;
  flex-direction: column;
}
modal-dialog.swift.document-name document-name label {
  display: block;
  font-size: 13px;
}
modal-dialog.swift.document-name document-name textarea {
  width: auto;
  display: block;
  margin: 5px 0px;
  height: 39px;
  outline: none;
  border: 1px solid lightgray;
  resize: vertical;
}

.dock-tab-content {
  flex: 1;
  min-height: 0;
  min-width: 0;
  display: none;
}
.dock-tab-content.active {
  display: flex;
}

quick-start editarea.root-editor {
  min-height: 100px;
}
quick-start > qs-math-type-container > math-type:hover {
  border: 1px solid gray;
}
quick-start > qs-math-type-container > math-type tool-bar {
  display: none;
}
quick-start > qs-math-type-container > math-type items-bar {
  display: none;
  left: -60px;
  top: 0px;
  box-shadow: 0px 1px 2px 0px #4c4949;
}

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**Context Level 1*/
/**both Level 1 and 2 Context */
/**Context Level 2 inside docsContainerZIndex*/
container-layer {
  display: block;
  min-height: 400px;
  background: #eeeeee;
  position: relative;
  height: calc(100% - 35px);
  outline: none;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-tap-highlight-color: transparent;
}
container-layer find-and-replace-area {
  z-index: 5;
}
container-layer items-bar {
  z-index: 3;
}
container-layer .doc-scroll-container::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
container-layer .docs-container {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
}
container-layer .doc-scroll-container {
  z-index: 2;
  position: relative;
}
container-layer .message-box-container {
  z-index: 7;
}
container-layer.mobile-tablet .doc-scroll-container::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
container-layer.mobile-tablet-ios {
  -webkit-overflow-scrolling: touch;
}
container-layer content-area {
  display: block;
  flex-direction: column;
  align-items: center;
  color: gray;
  box-sizing: border-box;
  width: 100%;
  /**must set overflow visible here, as we need to have some components appear outside view*/
  overflow: visible;
  padding-top: 70px;
}
container-layer content-area > green-bar {
  display: block;
  height: 15px;
  background: #4CAF50;
  width: 100%;
  border-bottom: 1px solid #c7c5c5;
  margin-top: -1px;
}
container-layer content-area > editor-container {
  -webkit-user-select: none;
  /* Chrome all / Safari all */
  -moz-user-select: none;
  /* Firefox all */
  -ms-user-select: none;
  margin: auto;
  margin-top: 10px;
  position: relative;
  display: block;
}
container-layer content-area > editor-container > embeded-editor {
  flex-grow: 1;
  display: block;
  min-height: 400px;
}
container-layer loading-layer {
  display: none;
  z-index: 10;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
container-layer loading-layer.show {
  display: block;
}
container-layer loading-layer overlay-loading {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
  background: rgba(0, 0, 0, 0.1);
  z-index: 10;
}
container-layer loading-layer .thin-loader {
  z-index: 11;
}
warning-error-region {
  font-size: 12px;
  display: flex;
  justify-content: center;
  text-align: center;
  white-space: pre-line;
  z-index: 999;
}
warning-error-region warning,
warning-error-region error,
warning-error-region info {
  background: white;
  padding: 2px 7px;
  border: 1px solid lightgray;
}
warning-error-region warning > i.fa.fa-exclamation-triangle,
warning-error-region error > i.fa.fa-exclamation-triangle,
warning-error-region info > i.fa.fa-exclamation-triangle {
  padding-right: 5px;
}
warning-error-region warning {
  color: orange;
}
warning-error-region error {
  color: #ce0303;
}
warning-error-region info {
  color: #1155cc;
}
/**end-ignore-save-as-html*/

/**start-ignore-save-as-html*/
.print-preview-container {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 99999;
}
.print-container {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: auto;
  background: #eeeeee;
}
.print-preview-controls-container {
  width: 200px;
  position: absolute;
  left: calc(50% - 100px);
  height: 26px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #cecccc;
  border: 1px solid #b7b6b6;
  padding: 3px;
  z-index: 10;
  bottom: 10px;
}
/**end-ignore-save-as-html*/

/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**Context Level 1*/
/**both Level 1 and 2 Context */
/**Context Level 2 inside docsContainerZIndex*/
document-info-container document-info-bar {
  display: block;
  position: absolute;
  right: 15px;
  bottom: 5px;
  background: #f7f7f7;
  box-shadow: 1px 1px 1px 0px #e0dddd;
  border: 1px lightgray solid;
  font-size: 12px;
  padding: 0px;
  color: gray;
  opacity: 0.8;
  z-index: 4;
}
document-info-container document-info-bar:hover {
  opacity: 1;
}
document-info-container modal-container button.ok.btn-primary {
  display: none;
}
.Popover-body {
  font-size: 12px;
  padding: 10px;
  background: #4CAF50;
  box-shadow: #888686 1px 1px 1px 1px;
  color: white;
  max-width: 250px;
}
.Popover-tip {
  fill: #4CAF50;
}

/**Context Level 1*/
/**both Level 1 and 2 Context */
/**Context Level 2 inside docsContainerZIndex*/
.doc-toolbar-container {
  position: relative;
  background: #f7f7f7;
  top: 0;
  left: 0;
  width: 100%;
  border: 1px lightgray solid;
  margin-left: -2px;
  z-index: 6;
}
.doc-toolbar-container > tool-bar {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  padding-left: 0px;
  min-width: 770px;
  width: 770px;
  margin: auto;
  background: transparent;
  box-shadow: none;
  border: none;
  height: 35px;
}

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
.message-box-container {
  font-size: 12px;
  width: 500px;
  height: auto;
  align-items: center;
  padding-top: 10px;
  padding-bottom: 10px;
  font-family: Verdana, sans-serif;
  border: 1px solid lightgray;
  max-width: 100%;
  bottom: 4px;
  display: flex;
  flex-direction: row;
}
.message-box-container.is-android {
  bottom: 24px;
}
.message-box-container.center {
  left: 50%;
  transform: translate(-50%, -50%);
  position: absolute;
}
.message-box-container .message {
  flex: 1;
}
.message-box-container .message-icon {
  width: 40px;
  flex-shrink: 0;
  text-align: center;
}
.message-box-container.show {
  visibility: visible;
  opacity: 1;
}
.message-box-container.hide {
  display: none;
  opacity: 0;
}
.message-box-container .close-icon {
  color: dimgray;
  padding-right: 10px;
  padding-left: 10px;
  flex-shrink: 0;
  cursor: pointer;
}
.message-box-container .close-icon :hover {
  color: black;
}
.message-box-container.warning-effect {
  color: #9F6000;
  background-color: #FEEFB3;
}
.message-box-container.error-effect {
  color: #D8000C;
  background-color: #FFBABA;
}
.message-box-container.info-effect {
  color: #4F8A10;
  background-color: #DFF2BF;
}
/**end-ignore-save-as-html*/

/**start-ignore-save-as-html*/
/**we use alpha as we have problem with background (without alpha) in Electron version (older Chrome) **/
/* z-index region*/
/**Context Level 1*/
/**both Level 1 and 2 Context */
/**Context Level 2 inside docsContainerZIndex*/
header .doc-share-button {
  border: 1px solid lightgray;
  padding: 4px 15px;
  margin: 5px 3px;
  border-radius: 4px;
  background: transparent;
  color: white;
  cursor: pointer;
  outline: none;
  margin-left: 20px;
  transition: background-color 0.5s, color 0.5s;
}
header .doc-share-button:disabled {
  color: lightgray;
  cursor: default;
}
header .doc-share-button:hover {
  background: white;
  color: gray;
}
header .inside-curve-left-bottom {
  width: 10px;
  height: 10px;
  position: absolute;
  left: -10px;
  bottom: 0;
  fill: #4caf4f;
}
header .inside-curve-right-bottom {
  width: 10px;
  height: 10px;
  position: absolute;
  right: -10px;
  bottom: 0;
  fill: #4caf4f;
  transform: scale(-1, 1);
}
header .name-tabs {
  display: flex;
  font-size: 13px;
  padding-top: 3px;
  background: #388a3b;
  padding-left: 10px;
  padding-right: 10px;
  flex: 1;
  min-width: 0;
  margin-bottom: 4px;
  -webkit-app-region: drag;
}
header .name-tabs .tab-split {
  position: absolute;
  right: 0;
  top: 5px;
  height: 19px;
  border-right: 1px solid #d6d4d4;
}
header .name-tabs .close-icon {
  position: absolute;
  right: 5px;
  top: 5px;
  padding: 2px 5px;
  vertical-align: middle;
  border-radius: 10px;
  cursor: pointer;
  transition: color 0.3s;
}
header .name-tabs .close-icon:hover {
  color: #ce0303;
}
header .name-tabs .name-tab {
  width: 280px;
  box-sizing: border-box;
  padding: 7px;
  padding-left: 15px;
  padding-right: 30px;
  padding-top: 8px;
  cursor: default;
  position: relative;
  color: #d6d4d4;
  min-width: 0;
  -webkit-app-region: no-drag;
  transition: background-color 0.3s, color 0.3s;
}
header .name-tabs .name-tab .name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline;
}
header .name-tabs .name-tab:hover {
  background: #489e4b;
  border-radius: 4px 4px 0px 0px;
  color: #ededed;
}
header .name-tabs .name-tab.active {
  -webkit-app-region: drag;
  background: #4caf4f;
  cursor: default;
  border-radius: 4px 4px 0px 0px;
  color: white;
}
header {
  box-sizing: border-box;
  top: 0px;
  background: #4caf4f;
  display: flex;
  width: 100%;
  height: 35px;
  color: white;
  position: relative;
  align-items: baseline;
  line-height: 0.8em;
  z-index: 2;
}
header .windows-controls {
  -webkit-app-region: no-drag;
}
header .windows-control {
  transition: background-color 0.2s;
  display: flex;
}
header .windows-control svg {
  fill: white;
  transition: fill 0.2s;
}
header .windows-control:hover {
  background-color: #58ca5c;
}
header .windows-control:hover.close > svg {
  fill: #ce0303;
}
header .doc-sinfo {
  padding: 0px 5px;
  color: #bfdcc0;
  font-size: 0.9em;
  display: inline;
  cursor: pointer;
}
header .doc-sinfo:hover {
  color: white;
}
header > login-name {
  text-align: right;
  font-size: 13px;
  transition: background 0.5s;
  font-size: 12px;
  flex-shrink: 0;
  display: block;
}
header > login-name > display-name {
  cursor: pointer;
  padding: 0px 10px;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
header > login-name.need-login > display-name {
  text-decoration: underline;
}
header separator-bar {
  border-left: 1px solid white;
}
header > document-name {
  -webkit-app-region: drag;
  font-size: 14px;
  display: block;
  width: initial;
  font-family: monospace;
  flex: 1;
  -webkit-user-select: none;
  min-width: 0px;
  position: relative;
}
header > document-name:hover div.rename-icon {
  visibility: visible;
}
header > document-name div.rename-icon {
  visibility: hidden;
  width: 20px;
  text-align: left;
  cursor: pointer;
  color: white;
}
header > document-name div.rename-icon.hide {
  visibility: hidden;
}
header > document-name > display-name {
  position: relative;
}
header > document-name > display-name > span {
  display: block;
  overflow: hidden;
  /* word-wrap: normal; */
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;
  line-height: 1.2em;
}
header.scroll {
  border-bottom: 1px solid #c7c5c5;
}
header app-mode {
  font-size: 12px;
}
header > logo {
  font-size: 20px;
  display: inline-block;
  box-sizing: border-box;
  padding-left: 5px;
  cursor: default;
}
header > logo > span {
  display: block;
  float: left;
  font-size: 16px;
}
header > logo > power {
  display: block;
  float: left;
  font-size: 0.6em;
}
header > logo > index {
  display: block;
}
header > logo > index > img {
  width: 25px;
  height: 30px;
}
header > logo > index > svg {
  vertical-align: middle;
  width: 19px;
  height: 30px;
}
header > account-information {
  margin-right: 10px;
  position: absolute;
  right: 0px;
  font-size: 12px;
  display: block;
  color: #616161;
  height: 100%;
  width: 70px;
  box-sizing: border-box;
  padding-top: 5px;
}
header > about-information {
  display: block;
  height: 100%;
  width: 100px;
  position: absolute;
  box-sizing: border-box;
  right: 0px;
  padding-right: 15px;
  /* top: 17px; */
  font-size: 0.7em;
  cursor: default;
  text-align: right;
  /* transition: top 0.5s; */
  height: 30px;
  padding-top: 10px;
}
header > about-information > about-text {
  display: block;
}
header > about-information:hover {
  text-decoration: underline;
}
modal-dialog.swift.logout-dialog modal-container.mt-common-dialog {
  width: 400px;
  top: 30%;
}
modal-dialog.swift.logout-dialog modal-container.mt-common-dialog modal-footer button.ok.btn-primary {
  display: none;
}
modal-dialog.swift.logout-dialog modal-container.mt-common-dialog modal-footer > div {
  flex-direction: column;
  align-items: center;
}
modal-dialog.swift.logout-dialog logout-dialog {
  text-align: center;
  width: 100%;
  padding: 10px;
}
/**end-ignore-save-as-html*/

/**start-ignore-save-as-html*/
.export-bar-container {
  font-size: 18px;
  width: 150px;
  display: flex;
  flex-direction: row;
  outline: none;
  color: white;
  padding: 0 0 0 0px;
  font-family: "Segoe UI", Arial, Verdana, sans-serif;
  min-width: 135px;
  align-items: baseline;
}
.export-bar-container.mobile-small-layout {
  width: 100px;
}
.export-bar-container.mobile-small-layout > div {
  margin-left: 0;
}
.export-bar-container.mobile-small-layout div.menu-bar-container div.button {
  width: 40px;
  font-size: 14px;
  padding-top: 0px;
  margin-right: 5px;
  padding-left: 5px;
  padding-right: 5px;
}
.export-bar-container.mobile-small-layout save-button {
  margin-left: 13px;
}
.export-bar-container > div {
  margin-left: 10px;
}
.export-bar-container .items {
  color: black;
}
.export-bar-container share-button,
.export-bar-container save-button,
.export-bar-container print-button {
  font-size: 12px;
  padding-top: 2px;
  padding: 2px 6px 0px 5px;
  display: block;
  margin-top: 7px;
  margin-left: 14px;
  height: 16px;
  cursor: pointer;
}
.export-bar-container share-button,
.export-bar-container print-button {
  border: 1px solid transparent;
}
.export-bar-container share-button i.fa,
.export-bar-container print-button i.fa {
  padding-right: 0px;
  margin-right: 3px;
  /* font-size: 14px; */
  display: inline-block;
}
.export-bar-container share-button:hover,
.export-bar-container print-button:hover {
  border: 1px solid white;
}
.export-bar-container share-button.disabled,
.export-bar-container print-button.disabled {
  color: lightgray;
  border: 1px solid transparent;
  cursor: default;
}
.export-bar-container save-button {
  border: 1px solid white;
  line-height: 1.2em;
}
.export-bar-container save-button::before {
  content: "Save";
}
.export-bar-container save-button:hover {
  background: #54c158;
}
.export-bar-container save-button.readonly {
  border: 1px solid lightgray;
  cursor: default;
  color: lightgray;
}
.export-bar-container save-button.readonly:hover {
  background: transparent;
}
.export-bar-container save-button.readonly::before {
  content: "Readonly";
}
.export-bar-container save-button.insync-readonly::before {
  content: "InSync|Read";
}
.export-bar-container save-button.insync::before {
  content: "InSync";
}
.export-bar-container save-button.saving {
  border: none;
  cursor: default;
  color: lightgray;
}
.export-bar-container save-button.saving:hover {
  background: transparent;
}
.export-bar-container save-button.saving::before {
  content: "Saving";
}
.export-bar-container save-button.saved {
  cursor: default;
  border: 1px solid lightgray;
  opacity: 0.5;
  color: lightgray;
}
.export-bar-container save-button.saved:hover {
  background: transparent;
}
.export-bar-container save-button.saved::before {
  content: "Saved";
}
/**end-ignore-save-as-html*/

/**start-ignore-save-as-html*/
.menu-bar-container {
  font-size: 12px;
  outline: none;
}
.menu-bar-container.disabled .button {
  color: lightgray;
}
.menu-bar-container.disabled .button:hover {
  text-decoration: none;
}
.menu-bar-container .hide-button {
  width: 45px;
  height: 30px;
}
.menu-bar-container .button {
  color: white;
  cursor: default;
  text-align: center;
  width: 45px;
  height: 30px;
  line-height: 35px;
  border: 1px solid transparent;
  box-shadow: none;
}
.menu-bar-container .button:hover {
  text-decoration: underline;
}
.menu-bar-container .button.selected {
  color: black;
  border: 1px solid lightgray;
  border-bottom: none;
  background-color: white;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.2);
  transition: none;
}
.menu-bar-container .items {
  border: 1px solid lightgray;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  background-color: white;
  width: 220px;
  position: absolute;
  padding: 4px 0px 4px 0px;
}
.menu-bar-container .items .item {
  display: flex;
  flex-direction: row;
  min-height: 30px;
  align-items: center;
  cursor: pointer;
}
.menu-bar-container .items .item .selected {
  background-color: #bfe4bd;
}
.menu-bar-container .items .item .inside-box {
  width: 100%;
  height: 30px;
  line-height: 30px;
}
.menu-bar-container .items .item .inside-box > i {
  display: inline-block;
  width: 25px;
  text-align: center;
}
.menu-bar-container .items .item .disabled {
  color: lightgray;
}
.menu-bar-container .show {
  visibility: visible;
}
.menu-bar-container .hide {
  display: none;
}
/**end-ignore-save-as-html*/

/**start-ignore-save-as-html*/
user-popup {
  color: gray;
  border: 1px solid #e2e1e1;
  background: white;
  box-shadow: 1px 1px 7px grey;
  text-align: left;
  position: absolute;
  top: 35px;
  right: 9px;
  width: 170px;
  outline: none;
  padding: 5px;
  cursor: default;
  z-index: 999999;
}
user-popup svg {
  position: absolute;
  left: 155px;
  top: -8px;
  width: 20px;
  height: 20px;
}
user-popup display-name {
  font-weight: bold;
}
user-popup logout-region {
  display: block;
  border-top: 1px solid lightgray;
  padding-top: 5px;
  margin-top: 10px;
}
user-popup logout-region button {
  float: right;
}
/**end-ignore-save-as-html*/

/**Context Level 1*/
/**both Level 1 and 2 Context */
/**Context Level 2 inside docsContainerZIndex*/
/**start-ignore-save-as-html*/
html,
body,
page,
#root {
  -webkit-font-smoothing: subpixel-antialiased;
  -webkit-text-stroke: 1px transparent;
  height: 100%;
  width: 100%;
  display: block;
  padding: 0;
  margin: 0;
  background: #eeeeee;
  font-family: "Segoe UI", Arial, Verdana, sans-serif;
  font-size: 16px;
  font-weight: 400;
  overflow: hidden;
}
logo > .ver-info {
  position: absolute;
  left: 0px;
  top: 27px;
  display: none;
  /* height: 30px; */
  font-size: 11px;
  background: white;
  text-align: center;
  color: #4caf4f;
  padding: 5px;
  border: 1px solid lightgray;
  text-align: left;
}
logo:hover > .ver-info {
  display: block;
}
button {
  padding: 2px 6px 2px;
  font-size: 12px;
  font-family: "Segoe UI", Arial, Verdana, sans-serif;
}
.modal-dialog-root-container {
  z-index: 3;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
input:not([type]),
input[type="text"],
input[type="password"],
textarea {
  border: 1px solid lightgray;
  border-radius: 0;
  outline: none;
  padding: 2px 2px 2px 2px;
  margin: 0px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  font-size: 12px;
  line-height: 1.35em;
}
input[type="radio"] {
  vertical-align: middle;
  margin-top: -2px;
}
page.ios {
  -webkit-user-select: none;
  /* disable selection/Copy of UIWebView */
  -webkit-touch-callout: none;
  /* disable the IOS popup when long-press on a link */
  user-select: none;
}
body {
  background: #eeeeee;
}
container {
  display: block;
  width: 100%;
  height: calc(100% - 50px);
  background: white;
  position: relative;
}
file-list {
  display: block;
  width: 100px;
  height: calc(100% - 5px);
  float: left;
}
content-area {
  width: 100%;
  overflow: auto;
  float: left;
  overflow: hidden;
  background: #eeeeee;
}
.thin-loader {
  height: 4px;
  top: 0;
  width: 100%;
  position: absolute;
  overflow: hidden;
  background-color: #ddd;
  display: none;
}
.thin-loader.show {
  display: block;
}
.thin-loader:before {
  display: block;
  position: absolute;
  content: "";
  left: -200px;
  width: 200px;
  height: 4px;
  background-color: #4caf50;
  animation: loading 2s linear infinite;
}
.thin-loader.suppress:before {
  animation: none;
}
@keyframes loading {
  from {
    left: -200px;
    width: 30%;
  }
  50% {
    width: 30%;
  }
  70% {
    width: 70%;
  }
  80% {
    left: 50%;
  }
  95% {
    left: 120%;
  }
  to {
    left: 100%;
  }
}
.btn-normal,
.btn-primary {
  display: block;
  cursor: pointer;
  outline: none;
  height: 22px;
  line-height: 1.3em;
}
.btn-primary {
  border: 1px solid #97d899;
  background: #4CAF50;
  color: white;
}
.btn-primary:hover {
  background: #4d9a50;
}
.btn-primary:disabled {
  background: #e0e0e0;
  border: 1px solid lightgray;
  cursor: default;
  color: gray;
}
.btn-normal {
  background: white;
  border: 1px solid lightgray;
  color: gray;
  fill: gray;
  line-height: 1.35em;
}
.btn-normal.no-bdg {
  border: 1px solid transparent;
  background-color: transparent;
}
.btn-normal.no-bdg:hover {
  background-color: transparent;
  border: 1px solid lightgray;
}
.btn-normal:hover {
  background: #f7f6f6;
}
.btn-normal:disabled {
  background: #e0e0e0;
  border: 1px solid lightgray;
  cursor: default;
  color: gray;
}
.btn-large {
  height: 24px;
  font-size: 12px;
}
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: transparent;
  -webkit-border-radius: 100px;
}
::-webkit-scrollbar:hover {
  background-color: #e7e7e7;
}
::-webkit-scrollbar-thumb {
  background-color: lightgray;
  -webkit-border-radius: 100px;
  cursor: pointer;
}
::-webkit-scrollbar-thumb:hover {
  background-color: gray;
}
::-webkit-scrollbar-thumb:active {
  background-color: gray;
}
/* add vertical min-height & horizontal min-width */
::-webkit-scrollbar-thumb:vertical {
  min-height: 35px;
}
::-webkit-scrollbar-thumb:horizontal {
  min-width: 35px;
}
.hover-hightlight:hover {
  background: #f3f2f2;
}
/**end-ignore-save-as-html*/

</style>