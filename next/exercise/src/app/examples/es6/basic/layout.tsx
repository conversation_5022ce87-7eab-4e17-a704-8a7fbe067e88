import { ThemeProvider } from 'next-themes';

import '@/app/example/es6/css/common.css';

import type { ReactNode } from 'react';
import type { Metadata } from 'next';

// ... existing code ...
export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

// ... existing code ...
export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      {children}
    </ThemeProvider>
  );
}
// ... existing code ...