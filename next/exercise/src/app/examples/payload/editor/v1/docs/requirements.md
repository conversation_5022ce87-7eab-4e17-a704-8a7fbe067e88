# Payload CMS 编辑器示例页面需求文档

## 项目概述
基于 Payload CMS 的编辑器功能和媒体管理器功能，创建一个示例页面来演示类似的功能实现。

## 功能需求

### 1. 编辑器功能
- **可视化编辑器**：实现类似 Payload CMS 的可视化编辑功能
- **实时预览**：支持编辑时实时预览内容
- **富文本编辑**：支持文本格式化、链接、列表等基本富文本功能
- **字段类型支持**：支持多种字段类型（文本、图片、选择器等）
- **版本控制**：基本的撤销/重做功能
- **自动保存**：编辑内容的自动保存功能

### 2. 媒体管理器功能
- **文件上传**：支持图片和文件上传
- **媒体预览**：上传文件的预览功能
- **文件组织**：基本的文件分类和管理
- **媒体选择器**：在编辑器中选择已上传的媒体文件
- **拖拽上传**：支持拖拽方式上传文件

### 3. 编辑器与媒体管理器的集成
- **无缝集成**：编辑器中直接访问媒体管理器
- **媒体插入**：在编辑器中直接插入选中的媒体文件
- **统一界面**：在同一个界面中管理内容和媒体
- **实时同步**：媒体管理器的变更实时反映到编辑器中

## 技术实现

### 技术栈
- **框架**：Next.js 15 + App Router
- **语言**：TypeScript
- **样式**：Tailwind CSS + Shadcn UI
- **编辑器**：Tiptap 或类似的富文本编辑器
- **状态管理**：React hooks
- **文件处理**：浏览器原生 File API

### 组件架构
- **主页面组件**：PayloadEditorDemo
- **编辑器组件**：VisualEditor
- **媒体管理器组件**：MediaManager
- **媒体选择器组件**：MediaSelector
- **工具栏组件**：EditorToolbar

### 数据结构
- **文档结构**：支持嵌套的内容块
- **媒体文件结构**：文件信息、预览URL、元数据
- **编辑历史**：支持撤销/重做的状态管理

## 用户界面设计

### 布局结构
- **左侧面板**：媒体管理器
- **中央区域**：可视化编辑器
- **右侧面板**：属性设置和预览
- **顶部工具栏**：编辑工具和操作按钮

### 交互设计
- **响应式设计**：支持桌面和移动设备
- **拖拽交互**：支持内容块和媒体文件的拖拽
- **键盘快捷键**：常用编辑操作的快捷键支持
- **实时反馈**：操作结果的即时视觉反馈

## 实现优先级

### 第一阶段（核心功能）
1. 基本的富文本编辑器
2. 简单的媒体上传和预览
3. 编辑器与媒体管理器的基本集成

### 第二阶段（增强功能）
1. 可视化编辑模式
2. 拖拽交互
3. 自动保存和版本控制

### 第三阶段（高级功能）
1. 高级媒体管理功能
2. 自定义字段类型
3. 协作编辑功能

## 参考资料
- Payload CMS 官方文档
- Tiptap 编辑器文档
- Next.js 15 App Router 最佳实践
- Shadcn UI 组件库