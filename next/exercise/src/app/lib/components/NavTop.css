div.nav {
    position: absolute;
    opacity: 0.4;

    /* 添加过渡动画：对opacity属性应用0.3秒的缓动效果 */
    transition: opacity 0.6s ease;

    display: flex;
    flex-direction: row; /* 横向排列 */
    gap: 12px;
}

div.nav:hover {
    opacity: 1;
}

div.nav svg {

    width: 20px;
    height: 20px;

    opacity: 0.65;

    /* 添加过渡动画：对opacity属性应用0.3秒的缓动效果 */
    transition: opacity 0.6s ease;
    vertical-align: middle;
}

div.nav svg:hover {
    opacity: 1;
}