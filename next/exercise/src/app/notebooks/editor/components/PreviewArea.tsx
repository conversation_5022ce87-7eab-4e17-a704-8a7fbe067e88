"use client";

import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, MathJaxContext } from "better-react-mathjax";
import { Edit3, RefreshCw, Play, Square } from "lucide-react";
import { useStatus } from "@/app/lib/atoms";
import { toast } from "react-toastify";
import "../css/style.css";

interface NoteData {
  id?: number;
  nbid?: number;
  tid?: number;
  title?: string;
  body?: string;
  question?: string;
  answer?: string;
  figures?: string;
  body_script?: string;
  body_extra?: string;
  note?: string;
  note_extra?: string;
  deleted?: boolean;
  created?: string;
  weight?: string;
}

interface PreviewAreaProps {
  noteData: NoteData;
}

// 循环模式类型定义
// Loop mode type definition
type LoopMode = "none" | "single" | "all";

// 编辑对话框的数据接口
// Interface for edit dialog data
interface SpanEditData {
  ariaLabel: string;
  dataSpeaker: string;
  dataVoiceId: string;
  spanContent: string; // 新增：span的内容 / New: span content
}

// 音频播放状态接口
// Audio playback state interface
interface AudioState {
  isPlaying: boolean;
  currentVoiceId: string | null;
  audio: HTMLAudioElement | null;
}

// 当前span状态接口
// Current span state interface
interface CurrentSpanState {
  sectionTitle: string | null;
  voiceId: string | null;
  spanElement: HTMLSpanElement | null;
}

// 编辑对话框组件
// Edit dialog component
interface EditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: SpanEditData) => void;
  initialData: SpanEditData;
}

function EditDialog({ isOpen, onClose, onSave, initialData }: EditDialogProps) {
  const [formData, setFormData] = useState<SpanEditData>(initialData);
  const dialogRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setFormData(initialData);
  }, [initialData]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div
        ref={dialogRef}
        className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4 shadow-xl"
        onClick={e => e.stopPropagation()}
      >
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">
          编辑 Span 属性 / Edit Span Attributes
        </h3>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Span 内容 / Span Content</label>
            <textarea
              value={formData.spanContent}
              onChange={e => setFormData({ ...formData, spanContent: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              rows={3}
              placeholder="输入 span 显示的内容..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">aria-label</label>
            <textarea
              value={formData.ariaLabel}
              onChange={e => setFormData({ ...formData, ariaLabel: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              rows={3}
              placeholder="输入 aria-label 内容..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">data-speaker</label>
            <select
              value={formData.dataSpeaker}
              onChange={e => setFormData({ ...formData, dataSpeaker: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
            >
              <option value="">无 / None</option>
              <option value="male">男性 / Male</option>
              <option value="female">女性 / Female</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">data-voice-id</label>
            <input
              type="text"
              value={formData.dataVoiceId}
              onChange={e => setFormData({ ...formData, dataVoiceId: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-100"
              placeholder="输入 data-voice-id 内容..."
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            取消 / Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            保存 / Save
          </button>
        </div>
      </div>
    </div>
  );
}

export function PreviewArea({ noteData }: PreviewAreaProps) {
  // 全局状态管理 - 用于控制 ProcessingMask
  // Global state management - for controlling ProcessingMask
  const [status, setStatus] = useStatus();

  // 本地可编辑的数据副本 - 用于维护编辑后的内容
  // Local editable data copy - for maintaining edited content
  const [editableNoteData, setEditableNoteData] = useState<NoteData>(noteData);

  // 当noteData props变化时，更新本地副本
  // Update local copy when noteData props change
  useEffect(() => {
    setEditableNoteData(noteData);
  }, [noteData]);

  // 编辑对话框状态管理
  // Edit dialog state management
  const [editDialog, setEditDialog] = useState({
    isOpen: false,
    spanElement: null as HTMLSpanElement | null,
    data: { ariaLabel: "", dataSpeaker: "", dataVoiceId: "", spanContent: "" } as SpanEditData,
  });

  // 语音生成状态
  // Voice generation state
  const [isGeneratingVoice, setIsGeneratingVoice] = useState<string | null>(null);

  // 每个 section 的循环模式设置
  // Loop mode settings for each section
  const [sectionLoopModes, setSectionLoopModes] = useState<Record<string, LoopMode>>({});

  // 音频播放状态
  // Audio playback state
  const [audioState, setAudioState] = useState<AudioState>({
    isPlaying: false,
    currentVoiceId: null,
    audio: null,
  });

  // 强制重新渲染的状态
  // Force re-render state
  const [renderKey, setRenderKey] = useState(0);

  // 当前span状态管理
  // Current span state management
  const [currentSpanState, setCurrentSpanState] = useState<Record<string, CurrentSpanState>>({});

  // 自动播放状态管理
  // Auto-play state management
  const [isAutoPlaying, setIsAutoPlaying] = useState(false);
  const [autoPlayQueue, setAutoPlayQueue] = useState<{sectionTitle: string, voiceIds: string[]}>({sectionTitle: '', voiceIds: []});
  
  // 批量生成语音状态管理
  // Batch voice generation state management
  const [isGeneratingVoices, setIsGeneratingVoices] = useState(false);
  // 清理音频资源
  // Cleanup audio resources
  useEffect(() => {
    return () => {
      if (audioState.audio) {
        audioState.audio.pause();
        audioState.audio = null;
      }
    };
  }, []);

  // 获取section中所有的span元素
  // Get all span elements in a section
  const getSectionSpans = (sectionTitle: string): HTMLSpanElement[] => {
    const sectionElements = document.querySelectorAll('.mb-6');
    for (const sectionElement of sectionElements) {
      const titleElement = sectionElement.querySelector('h3');
      if (titleElement && titleElement.textContent === sectionTitle) {
        const spans = sectionElement.querySelectorAll('span[data-voice-id]') as NodeListOf<HTMLSpanElement>;
        return Array.from(spans);
      }
    }
    return [];
  };

  // 设置当前span
  // Set current span
  const setCurrentSpan = (sectionTitle: string, voiceId: string) => {
    const spans = getSectionSpans(sectionTitle);
    const spanElement = spans.find(span => span.getAttribute('data-voice-id') === voiceId);
    
    if (spanElement) {
      setCurrentSpanState(prev => ({
        ...prev,
        [sectionTitle]: {
          sectionTitle,
          voiceId,
          spanElement
        }
      }));
      setRenderKey(prev => prev + 1);
    }
  };

  // 获取前一个span
  // Get previous span
  const getPreviousSpan = (sectionTitle: string): string | null => {
    const spans = getSectionSpans(sectionTitle);
    const currentState = currentSpanState[sectionTitle];
    
    if (!currentState || !currentState.voiceId) {
      return spans.length > 0 ? spans[spans.length - 1].getAttribute('data-voice-id') : null;
    }
    
    const currentIndex = spans.findIndex(span => span.getAttribute('data-voice-id') === currentState.voiceId);
    if (currentIndex > 0) {
      return spans[currentIndex - 1].getAttribute('data-voice-id');
    } else if (spans.length > 0) {
      return spans[spans.length - 1].getAttribute('data-voice-id'); // 循环到最后一个
    }
    return null;
  };

  // 获取下一个span
  // Get next span
  const getNextSpan = (sectionTitle: string): string | null => {
    const spans = getSectionSpans(sectionTitle);
    const currentState = currentSpanState[sectionTitle];
    
    if (!currentState || !currentState.voiceId) {
      return spans.length > 0 ? spans[0].getAttribute('data-voice-id') : null;
    }
    
    const currentIndex = spans.findIndex(span => span.getAttribute('data-voice-id') === currentState.voiceId);
    if (currentIndex >= 0 && currentIndex < spans.length - 1) {
      return spans[currentIndex + 1].getAttribute('data-voice-id');
    } else if (spans.length > 0) {
      return spans[0].getAttribute('data-voice-id'); // 循环到第一个
    }
    return null;
  };

  // MathJax configuration
  const mathJaxConfig = {
    loader: { load: ["[tex]/mhchem"] },
    tex: {
      packages: { "[+]": ["mhchem"] },
      inlineMath: [["$", "$"]],
      displayMath: [["$$", "$$"]],
      processEscapes: true,
      processEnvironments: true,
    },
    options: {
      renderActions: {
        addMenu: [0, "", ""],
      },
      skipHtmlTags: ["script", "noscript", "style", "textarea", "pre", "code"],
      ignoreHtmlClass: "cm-editor|CodeMirror",
    },
    startup: {
      typeset: false,
    },
  };

  // 处理编辑按钮点击
  // Handle edit button click
  const handleEditClick = (spanElement: HTMLSpanElement) => {
    const ariaLabel = spanElement.getAttribute("aria-label") || "";
    const dataSpeaker = spanElement.getAttribute("data-speaker") || "";
    const dataVoiceId = spanElement.getAttribute("data-voice-id") || "";
    const spanContent = spanElement.textContent || ""; // 获取span的文本内容 / Get span text content

    console.log('handleEditClick called:', { ariaLabel, dataSpeaker, dataVoiceId, spanContent });

    // 找到当前span所属的section
    // Find the section that contains this span
    let sectionTitle = "";
    let currentElement = spanElement.parentElement;
    while (currentElement) {
      const titleElement = currentElement.querySelector('h3');
      if (titleElement) {
        sectionTitle = titleElement.textContent || "";
        break;
      }
      currentElement = currentElement.parentElement;
    }

    console.log('Found section title:', sectionTitle);

    // 设置当前span
    // Set current span
    if (sectionTitle && dataVoiceId) {
      setCurrentSpan(sectionTitle, dataVoiceId);
      console.log('Set current span:', { sectionTitle, dataVoiceId });
    }

    setEditDialog({
      isOpen: true,
      spanElement,
      data: { ariaLabel, dataSpeaker, dataVoiceId, spanContent },
    });
  };

  // 处理编辑保存
  // Handle edit save
  const handleEditSave = (data: SpanEditData) => {
    console.log('handleEditSave called with data:', data);
    console.log('editDialog.spanElement:', editDialog.spanElement);
    
    if (editDialog.spanElement) {
      const spanElement = editDialog.spanElement;
      const originalVoiceId = spanElement.getAttribute("data-voice-id") || "";
      
      console.log('Before update - span attributes:', {
        ariaLabel: spanElement.getAttribute("aria-label"),
        dataSpeaker: spanElement.getAttribute("data-speaker"),
        dataVoiceId: spanElement.getAttribute("data-voice-id"),
        textContent: spanElement.textContent
      });
      
      // 找到当前span所属的section
      // Find the section that contains this span
      let sectionTitle = "";
      let sectionKey = "";
      
      // 方法1：向上遍历DOM树查找包含h3的容器
      // Method 1: Traverse up the DOM tree to find container with h3
      let currentElement = spanElement.parentElement;
      while (currentElement && currentElement !== document.body) {
        // 查找当前元素或其子元素中的h3
        const titleElement = currentElement.querySelector('h3');
        if (titleElement) {
          sectionTitle = titleElement.textContent || "";
          break;
        }
        // 检查当前元素本身是否包含section信息
        if (currentElement.classList.contains('mb-6')) {
          const titleInChildren = currentElement.querySelector('h3');
          if (titleInChildren) {
            sectionTitle = titleInChildren.textContent || "";
            break;
          }
        }
        currentElement = currentElement.parentElement;
      }
      
      // 方法2：如果方法1失败，尝试查找所有section容器
      // Method 2: If method 1 fails, try to find all section containers
      if (!sectionTitle) {
        const allSections = document.querySelectorAll('.mb-6');
        for (const section of allSections) {
          if (section.contains(spanElement)) {
            const titleElement = section.querySelector('h3');
            if (titleElement) {
              sectionTitle = titleElement.textContent || "";
              break;
            }
          }
        }
      }
      
      // 根据section标题确定对应的数据字段
      // Determine the corresponding data field based on section title
      if (sectionTitle) {
        const sectionMap: Record<string, keyof NoteData> = {
          "Body": "body",
          "Question": "question",
          "Answer": "answer",
          "Figures": "figures",
          "Body Script": "body_script",
          "Body Extra": "body_extra",
          "Note": "note",
          "Note Extra": "note_extra"
        };
        sectionKey = sectionMap[sectionTitle] || "";
      }
      
      console.log('Section search details:', {
        spanElement: spanElement.outerHTML.substring(0, 100),
        foundSectionTitle: sectionTitle,
        foundSectionKey: sectionKey,
        parentElement: spanElement.parentElement?.tagName,
        grandParent: spanElement.parentElement?.parentElement?.tagName
      });
      
      console.log('Found section for save:', { sectionTitle, sectionKey });
      
      if (sectionKey && editableNoteData[sectionKey as keyof NoteData]) {
        // 更新本地数据副本中的内容
        // Update content in local data copy
        const currentContent = editableNoteData[sectionKey as keyof NoteData] as string;
        
        // 使用正则表达式找到并替换对应的span内容
        // Use regex to find and replace the corresponding span content
        const spanRegex = new RegExp(
          `(<span[^>]*data-voice-id="${originalVoiceId.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"[^>]*>)(.*?)(<\/span>)`,
          'g'
        );
        
        const updatedContent = currentContent.replace(spanRegex, (match, openTag, innerContent, closeTag) => {
          // 更新属性
          let newOpenTag = openTag;
          newOpenTag = newOpenTag.replace(/aria-label="[^"]*"/, `aria-label="${data.ariaLabel}"`);
          newOpenTag = newOpenTag.replace(/data-speaker="[^"]*"/, `data-speaker="${data.dataSpeaker}"`);
          newOpenTag = newOpenTag.replace(/data-voice-id="[^"]*"/, `data-voice-id="${data.dataVoiceId}"`);
          
          // 如果没有找到对应属性，则添加
          if (!newOpenTag.includes('aria-label=')) {
            newOpenTag = newOpenTag.replace('>', ` aria-label="${data.ariaLabel}">`);
          }
          if (!newOpenTag.includes('data-speaker=')) {
            newOpenTag = newOpenTag.replace('>', ` data-speaker="${data.dataSpeaker}">`);
          }
          if (!newOpenTag.includes('data-voice-id=')) {
            newOpenTag = newOpenTag.replace('>', ` data-voice-id="${data.dataVoiceId}">`);
          }
          
          return newOpenTag + data.spanContent + closeTag;
        });
        
        console.log('Updated content:', { original: currentContent.substring(0, 100), updated: updatedContent.substring(0, 100) });
        
        // 更新本地数据副本
        // Update local data copy
        setEditableNoteData(prev => ({
          ...prev,
          [sectionKey]: updatedContent
        }));
        
        // 更新当前span状态
        // Update current span state
        if (sectionTitle && data.dataVoiceId) {
          setCurrentSpan(sectionTitle, data.dataVoiceId);
          console.log('Updated current span state:', { sectionTitle, voiceId: data.dataVoiceId });
        }
        
        // 强制重新渲染以更新UI
        // Force re-render to update UI
        setRenderKey(prev => {
          const newKey = prev + 1;
          console.log('Force re-render, new renderKey:', newKey);
          return newKey;
        });
        
        toast.success("Span 内容已更新 / Span content updated");
        console.log('Save completed successfully');
      } else {
        console.error('Could not find section key or content:', { sectionTitle, sectionKey });
        toast.error("保存失败：无法确定section / Save failed: could not determine section");
      }
    } else {
      console.error('No spanElement found in editDialog');
      toast.error("保存失败：未找到span元素 / Save failed: span element not found");
    }
  };

  // 处理音频播放
  // Handle audio playback
  const handlePlayAudio = (voiceId: string, sectionTitle: string) => {
    console.log('handlePlayAudio called:', { voiceId, isPlaying: audioState.isPlaying, currentVoiceId: audioState.currentVoiceId });
    
    // 如果当前正在播放相同的音频，则停止播放
    // If currently playing the same audio, stop it
    if (audioState.isPlaying && audioState.currentVoiceId === voiceId) {
      console.log('Stopping audio for voiceId:', voiceId);
      stopAudio();
      return;
    }

    // 停止当前播放的音频（如果有的话）
    // Stop currently playing audio (if any)
    if (audioState.audio) {
      const currentAudio = audioState.audio;
      // 移除当前音频的事件监听器
      // Remove current audio event listeners
      if ((currentAudio as any)._onCanPlay) {
        currentAudio.removeEventListener("canplay", (currentAudio as any)._onCanPlay);
      }
      if ((currentAudio as any)._onEnded) {
        currentAudio.removeEventListener("ended", (currentAudio as any)._onEnded);
      }
      if ((currentAudio as any)._onError) {
        currentAudio.removeEventListener("error", (currentAudio as any)._onError);
      }
      
      currentAudio.pause();
      currentAudio.currentTime = 0;
      currentAudio.src = "";
      console.log('Previous audio stopped and cleaned up');
    }

    // 获取当前 section 的循环模式
    // Get current section's loop mode
    const loopMode = sectionLoopModes[sectionTitle] || "none";

    // 构建音频文件路径
    // Build audio file path
    const tid = noteData.tid;
    const tidToDirectoryMap: Record<string, string> = {
      "21": "chinese-compositions",
      "22": "chinese-poetry",
      "23": "chinese-literature",
      "24": "chinese-essays",
      "25": "chinese-novels",
    };

    const directory = tidToDirectoryMap[String(tid)];
    if (!directory) {
      toast.error("无效的 tid，无法确定音频文件路径");
      return;
    }

    const voiceName = process.env.NEXT_PUBLIC_SPEECH_VOICE_CHINESE || "zh-CN-XiaoxiaoNeural";
    const firstChar = voiceId.charAt(0).toLowerCase();
    const audioPath = `/refs/notes/${directory}/${voiceName}/${firstChar}/${voiceId}.wav`;

    // 创建新的音频对象
    // Create new audio object
    const audio = new Audio(audioPath);

    // 设置循环模式
    // Set loop mode
    if (loopMode === "single" || loopMode === "all") {
      audio.loop = true;
    }

    // 定义事件监听器函数，以便正确移除
    // Define event listener functions for proper removal
    const onCanPlay = () => {
      console.log('Audio can play, setting state and starting playback');
      setAudioState({
        isPlaying: true,
        currentVoiceId: voiceId,
        audio: audio,
      });
      setRenderKey(prev => prev + 1);

      audio
        .play()
        .then(() => {
          toast.success("开始播放音频");
        })
        .catch(error => {
          console.error("音频播放失败:", error);
          toast.error("音频播放失败");
          setAudioState({
            isPlaying: false,
            currentVoiceId: null,
            audio: null,
          });
          setRenderKey(prev => prev + 1);
        });
    };

    const onEnded = () => {
      console.log('Audio ended, checking loop mode');
      // 检查当前循环模式（可能在播放过程中被改变）
      // Check current loop mode (might have been changed during playback)
      const currentLoopMode = sectionLoopModes[sectionTitle] || "none";
      if (currentLoopMode === "none") {
        setAudioState({
          isPlaying: false,
          currentVoiceId: null,
          audio: null,
        });
        setRenderKey(prev => prev + 1);
        toast.info("音频播放完毕");
      }
      // 对于 single 和 all 模式，由于设置了 loop=true，会自动循环
      // For single and all modes, it will loop automatically due to loop=true
    };

    const onError = () => {
      console.error("音频加载失败:", audioPath);
      toast.error("音频文件加载失败");
      setAudioState({
        isPlaying: false,
        currentVoiceId: null,
        audio: null,
      });
      setRenderKey(prev => prev + 1);
    };

    // 设置音频事件监听器
    // Set audio event listeners
    audio.addEventListener("canplay", onCanPlay);
    audio.addEventListener("ended", onEnded);
    audio.addEventListener("error", onError);

    // 将事件监听器函数保存到音频对象上，以便后续移除
    // Save event listener functions to audio object for later removal
    (audio as any)._onCanPlay = onCanPlay;
    (audio as any)._onEnded = onEnded;
    (audio as any)._onError = onError;

    // 开始加载音频
    // Start loading audio
    audio.load();
  };

  // 停止音频播放
  // Stop audio playback
  const stopAudio = () => {
    console.log('stopAudio called, current state:', { isPlaying: audioState.isPlaying, currentVoiceId: audioState.currentVoiceId });
    
    if (audioState.audio) {
      // 移除所有事件监听器，防止重复触发
      // Remove all event listeners to prevent duplicate triggers
      const audio = audioState.audio;
      if ((audio as any)._onCanPlay) {
        audio.removeEventListener("canplay", (audio as any)._onCanPlay);
      }
      if ((audio as any)._onEnded) {
        audio.removeEventListener("ended", (audio as any)._onEnded);
      }
      if ((audio as any)._onError) {
        audio.removeEventListener("error", (audio as any)._onError);
      }
      
      // 停止并重置音频
      // Stop and reset audio
      audio.pause();
      audio.currentTime = 0;
      audio.src = ""; // 清空音频源
      console.log('Audio stopped and reset');
    }
    
    setAudioState({
      isPlaying: false,
      currentVoiceId: null,
      audio: null,
    });
    // 停止自动播放
    // Stop auto-play
    setIsAutoPlaying(false);
    // 强制重新渲染以更新图标
    // Force re-render to update icons
    setRenderKey(prev => prev + 1);
    toast.info("音频播放已停止");
  };

  // Section级别的音频播放处理
  // Section-level audio playback handler
  const handleSectionPlayAudio = (sectionTitle: string, voiceId: string) => {
    console.log('handleSectionPlayAudio called:', { sectionTitle, voiceId });
    
    // 设置当前span
    setCurrentSpan(sectionTitle, voiceId);
    
    // 停止当前播放的音频（如果有的话）
    if (audioState.audio) {
      const currentAudio = audioState.audio;
      if ((currentAudio as any)._onCanPlay) {
        currentAudio.removeEventListener("canplay", (currentAudio as any)._onCanPlay);
      }
      if ((currentAudio as any)._onEnded) {
        currentAudio.removeEventListener("ended", (currentAudio as any)._onEnded);
      }
      if ((currentAudio as any)._onError) {
        currentAudio.removeEventListener("error", (currentAudio as any)._onError);
      }
      
      currentAudio.pause();
      currentAudio.currentTime = 0;
      currentAudio.src = "";
      console.log('Previous audio stopped and cleaned up');
    }

    // 获取当前 section 的循环模式
    const loopMode = sectionLoopModes[sectionTitle] || "none";
    
    // 设置自动播放状态
    setIsAutoPlaying(true);
    
    // 构建音频文件路径
    const tid = noteData.tid;
    const tidToDirectoryMap: Record<string, string> = {
      "21": "chinese-compositions",
      "22": "chinese-poetry",
      "23": "chinese-literature",
      "24": "chinese-essays",
      "25": "chinese-novels",
    };

    const directory = tidToDirectoryMap[String(tid)];
    if (!directory) {
      toast.error("无效的 tid，无法确定音频文件路径");
      return;
    }

    const voiceName = process.env.NEXT_PUBLIC_SPEECH_VOICE_CHINESE || "zh-CN-XiaoxiaoNeural";
    const firstChar = voiceId.charAt(0).toLowerCase();
    const audioPath = `/refs/notes/${directory}/${voiceName}/${firstChar}/${voiceId}.wav`;

    // 创建新的音频对象
    const audio = new Audio(audioPath);

    // 定义事件监听器函数
    const onCanPlay = () => {
      console.log('Section audio can play, setting state and starting playback');
      setAudioState({
        isPlaying: true,
        currentVoiceId: voiceId,
        audio: audio,
      });
      setRenderKey(prev => prev + 1);

      audio
        .play()
        .then(() => {
          toast.success("开始播放音频");
        })
        .catch(error => {
          console.error("音频播放失败:", error);
          toast.error("音频播放失败");
          setAudioState({
            isPlaying: false,
            currentVoiceId: null,
            audio: null,
          });
          setRenderKey(prev => prev + 1);
          setIsAutoPlaying(false);
        });
    };

    const onEnded = () => {
      console.log('Section audio ended, checking loop mode and auto-play');
      const currentLoopMode = sectionLoopModes[sectionTitle] || "none";
      
      if (currentLoopMode === "single") {
        // 单句循环：重新播放当前句子
        console.log('Single loop mode: replaying current span');
        audio.currentTime = 0;
        audio.play();
      } else {
        // 不循环或全文循环：播放下一句
        const spans = getSectionSpans(sectionTitle);
        const currentIndex = spans.findIndex(span => span.getAttribute('data-voice-id') === voiceId);
        
        if (currentIndex >= 0 && currentIndex < spans.length - 1) {
          // 有下一句，播放下一句
          const nextVoiceId = spans[currentIndex + 1].getAttribute('data-voice-id');
          if (nextVoiceId) {
            console.log('Playing next span:', nextVoiceId);
            setCurrentSpan(sectionTitle, nextVoiceId);
            setTimeout(() => {
              handleSectionPlayAudio(sectionTitle, nextVoiceId);
            }, 100);
            return;
          }
        }
        
        // 已经是最后一句
        if (currentLoopMode === "all") {
          // 全文循环：从第一句开始
          if (spans.length > 0) {
            const firstVoiceId = spans[0].getAttribute('data-voice-id');
            if (firstVoiceId) {
              console.log('All loop mode: restarting from first span:', firstVoiceId);
              setCurrentSpan(sectionTitle, firstVoiceId);
              setTimeout(() => {
                handleSectionPlayAudio(sectionTitle, firstVoiceId);
              }, 100);
              return;
            }
          }
        }
        
        // 不循环或播放完毕：停止播放
        console.log('Playback finished');
        setAudioState({
          isPlaying: false,
          currentVoiceId: null,
          audio: null,
        });
        setRenderKey(prev => prev + 1);
        setIsAutoPlaying(false);
        toast.info("播放完毕");
      }
    };

    const onError = () => {
      console.error("音频加载失败:", audioPath);
      toast.error("音频文件加载失败");
      setAudioState({
        isPlaying: false,
        currentVoiceId: null,
        audio: null,
      });
      setRenderKey(prev => prev + 1);
      setIsAutoPlaying(false);
    };

    // 设置音频事件监听器
    audio.addEventListener("canplay", onCanPlay);
    audio.addEventListener("ended", onEnded);
    audio.addEventListener("error", onError);

    // 保存事件监听器函数引用
    (audio as any)._onCanPlay = onCanPlay;
    (audio as any)._onEnded = onEnded;
    (audio as any)._onError = onError;

    // 开始加载音频
    audio.load();
  };

  // 批量生成/更新section中所有span的语音文件
  // Batch generate/update voice files for all spans in a section
  const handleBatchRefreshVoices = async (sectionTitle: string) => {
    console.log('开始批量生成语音文件:', sectionTitle);
    console.log('Starting batch voice generation:', sectionTitle);
    
    // 防止重复点击
    // Prevent duplicate clicks
    if (isGeneratingVoices) {
      toast.warning("正在生成语音文件，请稍候...");
      return;
    }
    
    // 获取section中所有的span元素
    // Get all span elements in the section
    const spans = getSectionSpans(sectionTitle);
    
    if (spans.length === 0) {
      toast.warning("该section中没有找到可生成语音的内容");
      return;
    }
    
    // 提取语音项目数据
    // Extract voice item data
    const voiceItems = spans.map(span => {
      const text = span.getAttribute('aria-label') || span.textContent || '';
      const voiceId = span.getAttribute('data-voice-id') || '';
      return { text: text.trim(), voiceId: voiceId.trim() };
    }).filter(item => item.text && item.voiceId); // 过滤掉空的项目
    
    if (voiceItems.length === 0) {
      toast.warning("该section中没有找到有效的语音数据");
      return;
    }
    
    setIsGeneratingVoices(true);
    toast.info(`开始批量生成 ${voiceItems.length} 个语音文件...`);
    
    try {
      // 调用批量生成API
      // Call batch generation API
      const response = await fetch('/api/notebooks/notes/voice/chinese/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tid: noteData.tid,
          voiceItems: voiceItems
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        toast.success(`批量生成成功！共生成 ${result.data.summary.success} 个语音文件`);
        console.log('批量生成结果:', result.data);
      } else {
        // 部分成功的情况
        if (result.data && result.data.summary) {
          const { summary } = result.data;
          toast.warning(`批量生成部分成功：成功 ${summary.success} 个，失败 ${summary.failure} 个`);
          console.log('批量生成结果:', result.data);
          
          // 显示错误详情
          if (result.data.errors && result.data.errors.length > 0) {
            console.error('生成失败的项目:', result.data.errors);
          }
        } else {
          toast.error(`批量生成失败: ${result.error}`);
        }
      }
    } catch (error) {
      console.error('批量生成语音文件时发生错误:', error);
      toast.error('批量生成语音文件时发生网络错误');
    } finally {
      setIsGeneratingVoices(false);
    }
  };

  // 设置 section 的循环模式
  // Set section loop mode
  const setSectionLoopMode = (sectionTitle: string, mode: LoopMode) => {
    setSectionLoopModes(prev => ({
      ...prev,
      [sectionTitle]: mode,
    }));

    // 如果当前正在播放该 section 的音频，更新循环设置
    // If currently playing audio from this section, update loop settings
    if (audioState.isPlaying && audioState.audio) {
      // 查找当前播放的音频属于哪个 section
      // Find which section the currently playing audio belongs to
      const currentVoiceId = audioState.currentVoiceId;
      if (currentVoiceId) {
        // 检查当前播放的音频是否属于这个 section
        // Check if the currently playing audio belongs to this section
        const spanElement = document.querySelector(`[data-voice-id="${currentVoiceId}"]`);
        if (spanElement) {
          // 查找包含这个 span 的 section
          // Find the section containing this span
          const sectionElement = spanElement.closest(".mb-6");
          if (sectionElement) {
            const sectionTitleElement = sectionElement.querySelector("h3");
            if (sectionTitleElement && sectionTitleElement.textContent === sectionTitle) {
              // 当前播放的音频属于这个 section，更新循环设置
              // Currently playing audio belongs to this section, update loop settings
              if (mode === "single" || mode === "all") {
                audioState.audio.loop = true;
                toast.info(`已切换到${mode === "single" ? "单句" : "全文"}循环模式`);
              } else {
                audioState.audio.loop = false;
                toast.info("已切换到不循环模式，播放完毕后将停止");
              }
            }
          }
        }
      }
    }
  };

  // 处理语音刷新按钮点击
  // Handle voice refresh button click
  const handleRefreshVoice = async (spanElement: HTMLSpanElement) => {
    const ariaLabel = spanElement.getAttribute("aria-label");
    const dataVoiceId = spanElement.getAttribute("data-voice-id");
    const tid = noteData.tid;

    if (!ariaLabel || !dataVoiceId || !tid) {
      toast.error("缺少必要的参数：aria-label、data-voice-id 或 tid");
      return;
    }

    // 启用 ProcessingMask 防止误操作
    // Enable ProcessingMask to prevent misoperations
    setStatus(prev => ({
      ...prev,
      isProcessing: true,
    }));

    setIsGeneratingVoice(dataVoiceId);

    try {
      const response = await fetch("/api/notebooks/notes/voice/chinese", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ariaLabel,
          voiceId: dataVoiceId,
          tid,
        }),
      });

      const result = await response.json();

      if (result.success) {
        toast.success("语音生成成功！/ Voice generated successfully!");
      } else {
        toast.error(`语音生成失败：${result.error}`);
      }
    } catch (error) {
      console.error("语音生成请求失败:", error);
      toast.error("语音生成请求失败，请检查网络连接");
    } finally {
      // 关闭 ProcessingMask
      // Disable ProcessingMask
      setStatus(prev => ({
        ...prev,
        isProcessing: false,
      }));

      setIsGeneratingVoice(null);
    }
  };

  // 为内容添加图标的函数
  // Function to add icons to content
  const addIconsToSpans = (content: string): string => {
    console.log('addIconsToSpans called, renderKey:', renderKey);
    console.log('Current span states:', currentSpanState);
    console.log('Audio state:', { isPlaying: audioState.isPlaying, currentVoiceId: audioState.currentVoiceId });
    
    // 使用正则表达式匹配 span 标签
    // Use regex to match span tags
    const spanRegex = /(<span[^>]*aria-label="[^"]*"[^>]*data-voice-id="[^"]*"[^>]*>)(.*?)(<\/span>)/g;

    return content.replace(spanRegex, (match, openTag, innerContent, closeTag) => {
      // 为每个匹配的 span 添加唯一的 data-span-id
      // Add unique data-span-id to each matched span
      const spanId = `span-${Math.random().toString(36).substr(2, 9)}`;
      const modifiedOpenTag = openTag.replace(">", ` data-span-id="${spanId}">`);

      // 提取 data-voice-id 来判断播放状态
      // Extract data-voice-id to determine play state
      const voiceIdMatch = openTag.match(/data-voice-id="([^"]*)"/); 
      const voiceId = voiceIdMatch ? voiceIdMatch[1] : "";
      const isCurrentlyPlaying = audioState.isPlaying && audioState.currentVoiceId === voiceId;

      // 检查是否为当前span（需要高亮显示）
      // Check if this is the current span (needs highlighting)
      let isCurrentSpan = false;
      let matchedSection = "";
      for (const sectionTitle in currentSpanState) {
        if (currentSpanState[sectionTitle]?.voiceId === voiceId) {
          isCurrentSpan = true;
          matchedSection = sectionTitle;
          break;
        }
      }

      // 调试信息
      if (voiceId) {
        console.log('Processing span with voiceId:', voiceId, { 
          isPlaying: audioState.isPlaying, 
          currentVoiceId: audioState.currentVoiceId, 
          isCurrentlyPlaying, 
          isCurrentSpan,
          matchedSection,
          innerContent: innerContent.substring(0, 20) + '...'
        });
      }

      // 根据播放状态选择图标
      // Choose icon based on play state
      const playIcon = isCurrentlyPlaying ? "⏹️" : "▶️";
      const playTitle = isCurrentlyPlaying ? "停止播放 / Stop Audio" : "播放音频 / Play Audio";

      // 为当前span添加背景色样式
      // Add background color style for current span
      const currentSpanStyle = isCurrentSpan ? 'background-color: rgba(34, 197, 94, 0.2); padding: 2px 4px; border-radius: 4px;' : '';
      const modifiedOpenTagWithStyle = modifiedOpenTag.replace('>', ` style="${currentSpanStyle}">`);

      return `${modifiedOpenTagWithStyle}${innerContent}${closeTag}<span class="span-icons" data-target="${spanId}" style="margin-left: 4px; opacity: 0.7;"><button class="icon-btn edit-btn" data-action="edit" data-target="${spanId}" style="background: none; border: none; cursor: pointer; padding: 2px; margin: 0 1px; color: #666; hover:color: #333;" title="编辑 / Edit">✏️</button><button class="icon-btn refresh-btn" data-action="refresh" data-target="${spanId}" style="background: none; border: none; cursor: pointer; padding: 2px; margin: 0 1px; color: #666; hover:color: #333;" title="刷新语音 / Refresh Voice">🔄</button><button class="icon-btn play-btn" data-action="play" data-target="${spanId}" style="background: none; border: none; cursor: pointer; padding: 2px; margin: 0 1px; color: #666; hover:color: #333;" title="${playTitle}">${playIcon}</button></span>`;
    });
  };

  // 创建内容点击处理器的工厂函数
  // Factory function for content click handlers
  const createContentClickHandler = (sectionTitle: string) => {
    return (e: React.MouseEvent) => {
      const target = e.target as HTMLElement;

      if (target.classList.contains("icon-btn")) {
        e.preventDefault();
        e.stopPropagation();

        const action = target.getAttribute("data-action");
        const spanId = target.getAttribute("data-target");

        if (spanId) {
          const spanElement = document.querySelector(`[data-span-id="${spanId}"]`) as HTMLSpanElement;

          if (spanElement) {
            if (action === "edit") {
              handleEditClick(spanElement);
            } else if (action === "refresh") {
              handleRefreshVoice(spanElement);
            } else if (action === "play") {
              const voiceId = spanElement.getAttribute("data-voice-id");
              if (voiceId) {
                handlePlayAudio(voiceId, sectionTitle);
              } else {
                toast.error("缺少 data-voice-id 属性");
              }
            }
          }
        }
      } else if (target.hasAttribute && target.hasAttribute("data-voice-id")) {
        // 点击span本身，设置为当前span
        // Click on span itself, set as current span
        e.preventDefault();
        e.stopPropagation();
        
        const voiceId = target.getAttribute("data-voice-id");
        if (voiceId) {
          setCurrentSpan(sectionTitle, voiceId);
          toast.info(`已设置当前句子: ${target.getAttribute("aria-label") || voiceId}`);
        }
      }
    };
  };

  const renderSection = (title: string, content: string | undefined) => {
    if (!content || content.trim() === "") return null;

    // 为内容添加图标（依赖 renderKey 确保重新渲染）
    // Add icons to content (depends on renderKey for re-rendering)
    const contentWithIcons = addIconsToSpans(content);
    
    console.log('renderSection called:', { title, contentLength: content.length, renderKey });

    // 获取当前 section 的循环模式
    // Get current section's loop mode
    const currentLoopMode = sectionLoopModes[title] || "none";

    return (
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300">{title}</h3>
            
            {/* Section播放控制按钮 / Section playback control buttons */}
            <div className="flex items-center space-x-1">
              <button
                onClick={() => {
                  const prevVoiceId = getPreviousSpan(title);
                  if (prevVoiceId) {
                    setCurrentSpan(title, prevVoiceId);
                    toast.info("已切换到前一句");
                  }
                }}
                className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                title="前一个 / Previous"
              >
                ⏮️
              </button>
              <button
                onClick={() => {
                  const currentState = currentSpanState[title];
                  if (currentState && currentState.voiceId) {
                    if (audioState.isPlaying && audioState.currentVoiceId === currentState.voiceId) {
                      // 停止播放
                      stopAudio();
                      setIsAutoPlaying(false);
                    } else {
                      // 开始播放当前span
                      handleSectionPlayAudio(title, currentState.voiceId);
                    }
                  } else {
                    // 没有当前span，播放第一个
                    const firstVoiceId = getNextSpan(title);
                    if (firstVoiceId) {
                      setCurrentSpan(title, firstVoiceId);
                      handleSectionPlayAudio(title, firstVoiceId);
                    }
                  }
                }}
                className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                title={audioState.isPlaying && currentSpanState[title]?.voiceId === audioState.currentVoiceId ? "停止播放 / Stop" : "播放 / Play"}
              >
                {audioState.isPlaying && currentSpanState[title]?.voiceId === audioState.currentVoiceId ? "⏹️" : "▶️"}
              </button>
              <button
                onClick={() => {
                  const nextVoiceId = getNextSpan(title);
                  if (nextVoiceId) {
                    setCurrentSpan(title, nextVoiceId);
                    toast.info("已切换到下一句");
                  }
                }}
                className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                title="下一个 / Next"
              >
                ⏭️
              </button>
            </div>
            
            {/* Refresh按钮 - 批量生成语音 / Refresh button - Batch generate voices */}
            <button
              onClick={() => handleBatchRefreshVoices(title)}
              className="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              title="批量生成/更新语音文件 / Batch generate/update voice files"
              disabled={isGeneratingVoices}
            >
              {isGeneratingVoices ? "🔄" : "🔄"}
            </button>
          </div>

          {/* 循环模式选择器 / Loop mode selector */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500 dark:text-gray-400">循环模式:</span>
            <div className="flex space-x-1">
              <label className="flex items-center space-x-1 text-sm">
                <input
                  type="radio"
                  name={`loop-${title}`}
                  value="none"
                  checked={currentLoopMode === "none"}
                  onChange={() => setSectionLoopMode(title, "none")}
                  className="w-3 h-3"
                />
                <span className="text-gray-600 dark:text-gray-300">不循环</span>
              </label>
              <label className="flex items-center space-x-1 text-sm">
                <input
                  type="radio"
                  name={`loop-${title}`}
                  value="single"
                  checked={currentLoopMode === "single"}
                  onChange={() => setSectionLoopMode(title, "single")}
                  className="w-3 h-3"
                />
                <span className="text-gray-600 dark:text-gray-300">单句循环</span>
              </label>
              <label className="flex items-center space-x-1 text-sm">
                <input
                  type="radio"
                  name={`loop-${title}`}
                  value="all"
                  checked={currentLoopMode === "all"}
                  onChange={() => setSectionLoopMode(title, "all")}
                  className="w-3 h-3"
                />
                <span className="text-gray-600 dark:text-gray-300">全文循环</span>
              </label>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <MathJax hideUntilTypeset="first">
            <div
              dangerouslySetInnerHTML={{ __html: contentWithIcons }}
              className="body-content prose max-w-none dark:prose-invert"
              onClick={createContentClickHandler(title)}
            />
          </MathJax>
        </div>
      </div>
    );
  };

  return (
    <MathJaxContext config={mathJaxConfig}>
      <div className="h-full overflow-auto">
        {/* 编辑对话框 / Edit Dialog */}
        <EditDialog
          isOpen={editDialog.isOpen}
          onClose={() => setEditDialog({ ...editDialog, isOpen: false })}
          onSave={handleEditSave}
          initialData={editDialog.data}
        />
        {/* Title */}
        {editableNoteData.title && (
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">{editableNoteData.title}</h2>
          </div>
        )}

        {/* Meta Information */}
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="grid grid-cols-2 gap-2 text-sm">
            {noteData.id && (
              <div>
                <span className="font-medium">ID:</span> {noteData.id}
              </div>
            )}
            {noteData.nbid && (
              <div>
                <span className="font-medium">Notebook ID:</span> {noteData.nbid}
              </div>
            )}
            {noteData.tid && (
              <div>
                <span className="font-medium">Type ID:</span> {noteData.tid}
              </div>
            )}
            {noteData.weight && (
              <div>
                <span className="font-medium">Weight:</span> {noteData.weight}
              </div>
            )}
            {noteData.created && (
              <div>
                <span className="font-medium">Created:</span> {new Date(noteData.created).toLocaleString()}
              </div>
            )}
            {noteData.deleted && (
              <div className="col-span-2">
                <span className="inline-block px-2 py-1 bg-red-100 text-red-800 rounded text-xs font-medium">
                  DELETED
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Content Sections */}
        {renderSection("Body", editableNoteData.body)}
        {renderSection("Question", editableNoteData.question)}
        {renderSection("Answer", editableNoteData.answer)}
        {renderSection("Figures", editableNoteData.figures)}
        {renderSection("Body Script", editableNoteData.body_script)}
        {renderSection("Body Extra", editableNoteData.body_extra)}
        {renderSection("Note", editableNoteData.note)}
        {renderSection("Note Extra", editableNoteData.note_extra)}

        {/* Empty State */}
        {!editableNoteData.title &&
          !editableNoteData.body &&
          !editableNoteData.question &&
          !editableNoteData.answer &&
          !editableNoteData.figures &&
          !editableNoteData.body_script &&
          !editableNoteData.body_extra &&
          !editableNoteData.note &&
          !editableNoteData.note_extra && (
            <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <p className="text-lg mb-2">No content to preview</p>
                <p className="text-sm">Start editing to see the preview</p>
              </div>
            </div>
          )}
      </div>
    </MathJaxContext>
  );
}
