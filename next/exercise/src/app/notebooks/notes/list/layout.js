import {ThemeProvider} from 'next-themes';

import '@/app/notebooks/css/page.css';

// 字体已在根布局的 /fonts/fonts.css 中定义，无需重复加载

export const metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export default function RootLayout({children}) {
  return (
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        {children}
      </ThemeProvider>
  );
}
