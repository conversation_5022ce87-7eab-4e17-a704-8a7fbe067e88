# 音频依次播放功能需求文档

## 功能概述

在听力对话页面(`/notebooks/notes/listening-dialog`)中实现音频依次播放功能，允许用户一键播放页面上所有听力对话的音频文件，按顺序自动播放，提升学习体验。

## 核心需求

### 1. 基础播放控制
- **播放按钮**: 页面顶部提供一个播放/暂停按钮
- **开始播放**: 点击播放按钮开始依次播放所有音频
- **暂停功能**: 播放过程中点击按钮可暂停当前播放
- **恢复播放**: 暂停后再次点击可从当前位置继续播放
- **按钮状态**: 播放时显示暂停图标，暂停时显示播放图标

### 2. 顺序播放逻辑
- **播放顺序**: 按照页面上音频项的显示顺序依次播放
- **自动切换**: 当前音频播放完成后自动播放下一个音频
- **起始位置**: 从当前选中的音频项开始播放（如果没有选中则从第一个开始）
- **播放完成**: 所有音频播放完成后自动停止顺序播放模式

### 3. 视觉反馈
- **当前项高亮**: 正在播放的音频项有明显的视觉标识（边框高亮）
- **播放状态**: 全局播放状态与各个音频组件的状态保持同步
- **按钮图标**: 根据播放状态动态切换播放/暂停图标

### 4. 交互体验
- **点击选择**: 用户可以点击任意音频项设为当前选中项
- **循环禁用**: 顺序播放时自动禁用各个音频的循环播放功能
- **状态保持**: 暂停后恢复播放时从暂停位置继续

## 技术实现要求

### 1. 状态管理
- 使用Jotai进行全局状态管理
- 维护当前播放索引(`currentNoteIndex`)
- 维护全局播放状态(`isPlaying`)
- 维护顺序播放模式状态(`isSequentialPlaying`)

### 2. 组件通信
- 使用自定义事件(CustomEvent)实现组件间通信
- 主页面控制播放逻辑，音频组件响应播放指令
- 音频组件播放结束时通知主页面切换到下一个

### 3. 音频控制
- 基于Howler.js实现音频播放控制
- 支持音频的播放、暂停、停止、定位等操作
- 处理音频加载、播放结束等事件

### 4. 错误处理
- 音频文件加载失败时的处理
- 网络异常时的降级处理
- 浏览器兼容性问题的处理

## 用户故事

### 故事1: 开始顺序播放
**作为** 英语学习者  
**我想要** 点击一个按钮就能依次播放所有听力对话  
**以便** 我可以连续练习听力而不需要手动点击每个音频  

**验收标准**:
- 点击播放按钮后第一个音频开始播放
- 按钮图标变为暂停图标
- 当前播放项有视觉高亮

### 故事2: 自动切换播放
**作为** 英语学习者  
**我想要** 当前音频播放完成后自动播放下一个  
**以便** 我可以专注于听力内容而不被打断  

**验收标准**:
- 第一个音频播放完成后自动播放第二个
- 高亮状态自动切换到当前播放项
- 播放状态保持连续

### 故事3: 暂停和恢复
**作为** 英语学习者  
**我想要** 能够暂停顺序播放并稍后恢复  
**以便** 我可以在需要时中断学习  

**验收标准**:
- 点击暂停按钮能立即停止当前音频
- 按钮图标变为播放图标
- 再次点击能从暂停位置继续播放

### 故事4: 选择起始位置
**作为** 英语学习者  
**我想要** 能够选择从哪个音频开始顺序播放  
**以便** 我可以跳过已经熟悉的内容  

**验收标准**:
- 点击任意音频项能设为当前选中项
- 开始顺序播放时从选中项开始
- 选中项有明显的视觉标识

## 非功能性需求

### 1. 性能要求
- 音频切换延迟不超过200ms
- 页面响应时间不超过100ms
- 内存使用合理，避免内存泄漏

### 2. 兼容性要求
- 支持Chrome、Firefox、Safari、Edge等主流浏览器
- 支持桌面和移动设备
- 兼容不同的音频格式(MP3、WAV等)

### 3. 可用性要求
- 界面直观易懂，用户无需学习即可使用
- 提供清晰的视觉反馈
- 支持键盘操作(可选)

### 4. 可维护性要求
- 代码结构清晰，易于理解和修改
- 组件职责分离，低耦合高内聚
- 提供充分的注释和文档

## 实现状态

### ✅ 已完成功能
1. **基础播放控制**
   - 播放/暂停按钮已实现
   - 按钮图标状态切换正常
   - 播放状态管理完整

2. **状态管理**
   - Jotai状态管理已配置
   - 全局状态结构已定义
   - 状态更新逻辑已实现

3. **组件通信**
   - 自定义事件系统已实现
   - 主页面控制逻辑已完成
   - 音频组件事件监听已配置

4. **视觉反馈**
   - 当前项高亮显示已实现
   - 播放状态同步已完成
   - UI交互体验良好

### ❌ 存在问题
1. **顺序播放不工作**
   - 音频播放结束事件可能未正确触发
   - 自动切换到下一个音频的逻辑可能有问题
   - 需要进一步调试和修复

### 🔧 需要修复的问题
1. **事件触发机制**
   - 检查`sequentialAudioEnded`事件是否正确触发
   - 验证事件监听器是否正确绑定
   - 确保事件传递的时机正确

2. **音频状态同步**
   - 检查Howl实例的状态管理
   - 验证音频播放结束的检测逻辑
   - 确保循环播放被正确禁用

3. **错误处理**
   - 添加音频加载失败的处理
   - 增加网络异常的降级方案
   - 提供用户友好的错误提示

## 测试计划

### 1. 功能测试
- [ ] 播放按钮点击测试
- [ ] 顺序播放流程测试
- [ ] 暂停恢复功能测试
- [ ] 当前项选择测试
- [ ] 播放完成后状态重置测试

### 2. 兼容性测试
- [ ] 不同浏览器测试
- [ ] 移动设备测试
- [ ] 不同音频格式测试

### 3. 性能测试
- [ ] 音频切换延迟测试
- [ ] 内存使用监控
- [ ] 长时间使用稳定性测试

### 4. 用户体验测试
- [ ] 界面直观性测试
- [ ] 操作流畅性测试
- [ ] 错误场景处理测试

## 优化建议

### 1. 短期优化
- 修复当前的顺序播放问题
- 添加更详细的错误处理
- 优化音频切换的流畅性

### 2. 中期优化
- 添加播放进度显示
- 支持播放速度调节
- 增加键盘快捷键支持

### 3. 长期优化
- 支持播放列表管理
- 添加学习进度跟踪
- 集成语音识别功能

## 相关文档

- [功能分析文档](./sequential-play-analysis.md) - 详细的代码实现分析
- [故障排查指南](./troubleshooting-guide.md) - 问题诊断和解决方案
- [API文档](../../../api/notebooks/notes/) - 相关API接口文档

## 更新日志

### v1.0.0 (当前版本)
- 实现基础的顺序播放框架
- 完成状态管理和组件通信
- 添加视觉反馈和用户交互
- 存在顺序播放不工作的问题

### 计划中的更新
- v1.0.1: 修复顺序播放问题
- v1.1.0: 添加进度显示和速度控制
- v1.2.0: 增加键盘支持和更多交互功能