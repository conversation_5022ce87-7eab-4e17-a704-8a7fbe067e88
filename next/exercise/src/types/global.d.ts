declare namespace JSX {
  interface IntrinsicElements {
    'hidden-input-wrapper': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
    'editor-container': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
    'math-type': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
    'editarea': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
    'area-baseline': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
    'prefix': React.DetailedHTMLProps<React.HTMLAttributes<HTMLElement>, HTMLElement>;
  }
}