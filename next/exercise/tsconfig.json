{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "checkJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "incremental": true, "module": "esnext", "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "strictNullChecks": false}, "include": ["./src/**/*", "next-env.d.ts", ".next/types/**/*.ts", "**/*.ts", "**/*.js", "**/*.tsx"], "exclude": ["node_modules"]}