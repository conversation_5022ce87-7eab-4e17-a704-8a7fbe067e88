{"name": "exercise", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "bun-dev": "bun --bun next dev", "build": "next build", "bun-build": "bun --bun next build", "start": "next start", "bun-start": "bun --bun next start", "lint": "next lint", "bun-lint": "bun --bun next lint"}, "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/commands": "^6.8.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/language": "^6.11.2", "@codemirror/lint": "^6.8.5", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.3", "@codemirror/view": "^6.38.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@flmngr/flmngr-server-node": "^1.5.3", "@headlessui/react": "^2.2.6", "@lexical/react": "^0.33.1", "@prisma/client": "^6.12.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@tailwindcss/typography": "^0.5.16", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@types/wavesurfer.js": "^6.0.12", "@uiw/react-codemirror": "^4.24.1", "better-react-mathjax": "^2.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dark-mode-toggle": "^0.17.0", "dotenv": "^17.2.0", "eslint-linter-browserify": "^9.31.0", "he": "^1.2.0", "highlight.js": "^11.11.1", "howler": "^2.2.4", "jotai": "^2.12.5", "katex": "^0.16.22", "lexical": "^0.33.1", "lexorank": "^1.0.5", "lucide-react": "^0.525.0", "mathjax": "^3.2.2", "mathlive": "^0.106.0", "microsoft-cognitiveservices-speech-sdk": "^1.45.0", "mysql2": "^3.14.2", "nanoid": "^5.1.5", "next": "^15.4.3", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-mathjax2": "^0.0.2", "react-mathlive": "^3.0.5-preview.1", "react-toastify": "^11.0.5", "react-window": "^1.8.11", "sentence-splitter": "^5.0.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "vaul": "^1.1.2", "wavesurfer.js": "^7.10.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "22.16.5", "@types/react": "19.1.8", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-window": "^1.8.8", "eslint": "^9", "eslint-config-next": "15.4.3", "prisma": "^6.12.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "5.8.3"}}